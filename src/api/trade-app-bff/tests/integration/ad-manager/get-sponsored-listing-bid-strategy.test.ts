import {
  BidStrategyType,
  GetSponsoredListingBidStrategyResponse,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import * as adManagerService from "../../../src/services/ad-manager";
import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const companyId = faker.number.int({ min: 1000, max: 9999 });
const campaignId = faker.string.uuid();

describe("GET /ad-manager/sponsored-search-campaigns/:campaignId - controller", () => {
  let getSponsoredListingBidStrategySpy: jest.SpyInstance;

  beforeEach(() => {
    getSponsoredListingBidStrategySpy = jest.spyOn(
      adManagerService.adManager,
      "getSponsoredListingBidStrategy",
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 and bid strategy when request is valid", async () => {
    const mockBidStrategy: GetSponsoredListingBidStrategyResponse = [
      BidStrategyType.Player,
      BidStrategyType.Leader,
      BidStrategyType.Competitor,
    ];

    getSponsoredListingBidStrategySpy.mockResolvedValue(mockBidStrategy);

    const response = await getTestRequestWithValidToken(companyId).get(
      `/ad-manager/sponsored-search-campaigns/${campaignId}`,
    );

    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockBidStrategy);
    expect(getSponsoredListingBidStrategySpy).toHaveBeenCalledWith(
      expect.any(Object),
      campaignId,
    );
  });

  it("should return 401 when user has invalid token", async () => {
    const response = await getTestRequestWithInvalidToken().get(
      `/ad-manager/sponsored-search-campaigns/${campaignId}`,
    );

    expect(response.status).toBe(401);
    expect(getSponsoredListingBidStrategySpy).not.toHaveBeenCalled();
  });

  it("should return 500 when service throws an error", async () => {
    const errorMessage = "Service error";
    getSponsoredListingBidStrategySpy.mockRejectedValue(
      new Error(errorMessage),
    );

    const response = await getTestRequestWithValidToken(companyId).get(
      `/ad-manager/sponsored-search-campaigns/${campaignId}`,
    );

    expect(response.status).toBe(500);
    expect(getSponsoredListingBidStrategySpy).toHaveBeenCalledWith(
      expect.any(Object),
      campaignId,
    );
  });

  it("should pass the correct logger to the service", async () => {
    const mockBidStrategy: GetSponsoredListingBidStrategyResponse = [
      BidStrategyType.Player,
    ];
    getSponsoredListingBidStrategySpy.mockResolvedValue(mockBidStrategy);

    await getTestRequestWithValidToken(companyId).get(
      `/ad-manager/sponsored-search-campaigns/${campaignId}`,
    );

    const loggerArg = getSponsoredListingBidStrategySpy.mock.calls[0][0];
    expect(loggerArg).toBeDefined();
    expect(typeof loggerArg.info).toBe("function");
    expect(typeof loggerArg.error).toBe("function");
  });
});
