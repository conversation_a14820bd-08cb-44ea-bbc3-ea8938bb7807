import { NotFoundError } from "@checkatrade/errors";
import {
  CompanyRole,
  MemberProfileStatus,
  MembershipType,
  ProductType,
  TradingType,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import * as revettingFirestore from "../../../src/services/firebase/firestore/revetting";
import { mitek } from "../../../src/services/mitek";
import * as teamService from "../../../src/services/team";
import * as getMemberByCompanyIdModule from "../../../src/services/team/get-member-by-company-id";
import { getTestRequestWithValidToken } from "../../helpers/test-request";

const companyId = faker.number.int({ min: 1000, max: 9999 });
const memberId = faker.string.uuid();

describe("GET /revetting/url - controller", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return revetting url when member exists", async () => {
    const mockTeamMember = {
      companyId: faker.number.int(),
      traderId: faker.number.int(),
      name: faker.company.name(),
      legacyProductType: ProductType.Standard,
      isMembershipFlexible: true,
      uniqueName: faker.company.name().toLowerCase().replace(/\s+/g, "-"),
      isCampaignMember: false,
      membershipType: MembershipType.FullMember,
      joinedDate: faker.date.past().toISOString(),
      acceptedEssentialsTermsDate: faker.date.past().toISOString(),
      registeredName: faker.company.name(),
      registeredNumber: faker.string.numeric(8),
      isVatRegistered: true,
      vatRegistrationNumber: faker.string.numeric(9),
      isOnProbation: false,
      profileStatus: MemberProfileStatus.Live,
      tradingType: TradingType.LimitedCompany,
      deletedDate: null,
      profileDescription: faker.lorem.sentence(),
      isTradeAvaialable: true,
      address: {
        line1: faker.location.streetAddress(),
        line2: faker.location.secondaryAddress(),
        city: faker.location.city(),
        county: faker.location.county(),
        postalCode: faker.location.zipCode(),
        country: "United Kingdom",
      },
    };

    const mockTeamPerson = {
      data: [
        {
          id: faker.string.uuid(),
          fullName: faker.person.fullName(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          dateOfBirth: faker.date.past().toISOString(),
          lastUpdated: faker.date.recent().toISOString(),
          address: {
            line1: faker.location.streetAddress(),
            line2: faker.location.secondaryAddress(),
            city: faker.location.city(),
            county: faker.location.county(),
            postalCode: faker.location.zipCode(),
          },
          phone: faker.phone.number(),
          email: faker.internet.email(),
          relationshipType: "Owner",
          membershipType: MembershipType.FullMember,
          role: CompanyRole.Owner,
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 1,
        totalPagesCount: 1,
      },
    };

    const mockMitekResponse = {
      id: faker.string.uuid(),
      reference: faker.string.uuid(),
      link: faker.internet.url(),
    };

    jest
      .spyOn(teamService.team, "getTeamPerson")
      .mockResolvedValue(mockTeamPerson);
    jest
      .spyOn(teamService.team, "getTeamMember")
      .mockResolvedValue(mockTeamMember);
    jest
      .spyOn(mitek, "createMitekRequest")
      .mockResolvedValue(mockMitekResponse);
    jest
      .spyOn(getMemberByCompanyIdModule, "getMemberByCompanyId")
      .mockResolvedValue({ memberId });
    jest
      .spyOn(revettingFirestore, "getRevettingEntry")
      .mockResolvedValue(undefined);
    jest.spyOn(revettingFirestore, "createRevettingEntry").mockResolvedValue({
      companyId,
      mitekReferenceId: mockMitekResponse.reference,
    });

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/revetting/url");

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({ url: mockMitekResponse.link });
    expect(teamService.team.getTeamMember).toHaveBeenCalledWith(
      memberId,
      {},
      expect.any(Object),
    );
  }, 10000);

  it("should return 404 when team member is not found", async () => {
    jest.spyOn(teamService.team, "getTeamPerson").mockResolvedValue({
      data: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        totalPagesCount: 0,
      },
    });
    jest
      .spyOn(teamService.team, "getTeamMember")
      .mockRejectedValue(new NotFoundError("Team member not found"));
    jest
      .spyOn(getMemberByCompanyIdModule, "getMemberByCompanyId")
      .mockResolvedValue({ memberId });
    jest
      .spyOn(revettingFirestore, "getRevettingEntry")
      .mockResolvedValue(undefined);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/revetting/url");

    expect(response.status).toEqual(404);
    expect(response.body).toEqual({
      detail: "Team person not found",
      instance: "/revetting/url",
      status: 404,
      title: "Not Found",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404",
    });
  }, 10000);
});
