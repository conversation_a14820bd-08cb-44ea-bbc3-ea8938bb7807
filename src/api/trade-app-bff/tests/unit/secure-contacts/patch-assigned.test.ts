import * as getCompanyDocModule from "../../../src/services/firebase/firestore/get-company";
import { secureContacts } from "../../../src/services/secure-contacts";
import {
  TestRequest,
  getTestRequestWithValidToken,
} from "../../helpers/test-request";

describe("PATCH /secure-contacts/assigned/:secureContactId", () => {
  let request: TestRequest;
  const mockCompanyId = 123;
  const mockSecureContactId = "secure-contact-123";
  const mockContactId = "contact-456";

  beforeEach(() => {
    request = getTestRequestWithValidToken(mockCompanyId);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 404 if getCompanyDoc returns undefined", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue(undefined);
    const spy = jest.spyOn(secureContacts, "patchSecureContact");

    const response = await request
      .patch(`/secure-contacts/assigned/${mockSecureContactId}`)
      .send({ contactId: mockContactId });

    expect(response.statusCode).toBe(404);
    expect(spy).not.toHaveBeenCalled();
  });

  it("should successfully patch assigned secure contact", async () => {
    const mockTradeId = 789;
    const mockPatchedContact = {
      id: mockContactId,
      contactId: mockContactId,
      divert: {
        secureNumber: "07700900123",
        destinationNumber: "01234567890",
      },
    };

    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: mockTradeId });
    jest
      .spyOn(secureContacts, "patchSecureContact")
      .mockResolvedValue(mockPatchedContact);

    const response = await request
      .patch(`/secure-contacts/assigned/${mockSecureContactId}`)
      .send({ contactId: mockContactId });

    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual(mockPatchedContact);
    expect(secureContacts.patchSecureContact).toHaveBeenCalledWith(
      mockTradeId,
      mockSecureContactId,
      mockContactId,
      expect.any(Object), // logger
    );
  });
});

// comment
