import { NumberType } from "@checkatrade/trade-bff-types";

import * as getCompanyDocModule from "../../../src/services/firebase/firestore/get-company";
import { secureContacts } from "../../../src/services/secure-contacts";
import {
  TestRequest,
  getTestRequestWithValidToken,
} from "../../helpers/test-request";

describe("GET /secure-contacts/options", () => {
  let request: TestRequest;
  const mockCompanyId = 123;

  beforeEach(() => {
    request = getTestRequestWithValidToken(mockCompanyId);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 404 if getCompanyDoc returns undefined", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue(undefined);
    const spy = jest.spyOn(secureContacts, "getSecureContactOptions");
    const response = await request.get("/secure-contacts/options");
    expect(response.statusCode).toBe(404);
    expect(spy).not.toHaveBeenCalled();
  });

  it("should return formatted options in response", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: 1 });
    jest.spyOn(secureContacts, "getSecureContactOptions").mockResolvedValue([
      {
        id: "1",
        salutation: "Ms",
        firstName: "Jane",
        lastName: "Smith",
        personalNumber: true,
        number: "555",
        numberType: NumberType.MOBILE,
      },
    ]);
    const response = await request.get("/secure-contacts/options");
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      mobileOptions: [
        {
          id: "1",
          name: "Ms Jane Smith",
          number: "555",
        },
      ],
    });
  });

  it("should handle options with -Phone suffix correctly", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: 1 });
    jest.spyOn(secureContacts, "getSecureContactOptions").mockResolvedValue([
      {
        id: "1",
        salutation: "Mr",
        firstName: "John",
        lastName: "Doe",
        personalNumber: true,
        number: "*********",
        numberType: NumberType.MOBILE,
      },
      {
        id: "1-Phone",
        salutation: "Mr",
        firstName: "John",
        lastName: "Doe",
        personalNumber: true,
        number: "*********",
        numberType: NumberType.MOBILE,
      },
    ]);
    const response = await request.get("/secure-contacts/options");
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      mobileOptions: [
        {
          id: "1",
          name: "Mr John Doe",
          number: "*********",
        },
        {
          id: "1-Phone",
          name: "Mr John Doe",
          number: "*********",
        },
      ],
    });
  });

  it("should not duplicate secondary options when they have the same number as primary", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: 1 });
    jest.spyOn(secureContacts, "getSecureContactOptions").mockResolvedValue([
      {
        id: "1",
        salutation: "Mr",
        firstName: "John",
        lastName: "Doe",
        personalNumber: true,
        number: "*********",
        numberType: NumberType.MOBILE,
      },
      {
        id: "1-Phone",
        salutation: "Mr",
        firstName: "John",
        lastName: "Doe",
        personalNumber: true,
        number: "*********", // Same number as primary
        numberType: NumberType.MOBILE,
      },
    ]);
    const response = await request.get("/secure-contacts/options");
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      mobileOptions: [
        {
          id: "1",
          name: "Mr John Doe",
          number: "*********",
        },
      ],
    });
  });

  it("should filter out non-mobile number types", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: 1 });
    jest.spyOn(secureContacts, "getSecureContactOptions").mockResolvedValue([
      {
        id: "1",
        salutation: "Mr",
        firstName: "John",
        lastName: "Doe",
        personalNumber: true,
        number: "*********",
        numberType: NumberType.MOBILE,
      },
      {
        id: "2",
        salutation: "Ms",
        firstName: "Jane",
        lastName: "Smith",
        personalNumber: true,
        number: "*********",
        numberType: NumberType.LANDLINE, // Non-mobile type
      },
    ]);
    const response = await request.get("/secure-contacts/options");
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      mobileOptions: [
        {
          id: "1",
          name: "Mr John Doe",
          number: "*********",
        },
      ],
    });
  });

  it("should filter out options without phone numbers", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: 1 });
    jest.spyOn(secureContacts, "getSecureContactOptions").mockResolvedValue([
      {
        id: "1",
        salutation: "Mr",
        firstName: "John",
        lastName: "Doe",
        personalNumber: true,
        number: "*********",
        numberType: NumberType.MOBILE,
      },
      {
        id: "2",
        salutation: "Ms",
        firstName: "Jane",
        lastName: "Smith",
        personalNumber: true,
        number: "", // Empty number
        numberType: NumberType.MOBILE,
      },
    ]);
    const response = await request.get("/secure-contacts/options");
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      mobileOptions: [
        {
          id: "1",
          name: "Mr John Doe",
          number: "*********",
        },
      ],
    });
  });
});
