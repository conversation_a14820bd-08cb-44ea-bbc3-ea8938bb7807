import * as getCompanyDocModule from "../../../src/services/firebase/firestore/get-company";
import { secureContacts } from "../../../src/services/secure-contacts";
import {
  TestRequest,
  getTestRequestWithValidToken,
} from "../../helpers/test-request";

describe("GET /secure-contacts/assigned", () => {
  let request: TestRequest;
  const mockCompanyId = 123;

  beforeEach(() => {
    request = getTestRequestWithValidToken(mockCompanyId);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 404 if getCompanyDoc returns undefined", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue(undefined);
    const spy = jest.spyOn(secureContacts, "getMobileAssignedSecureContact");
    const response = await request.get("/secure-contacts/assigned");
    expect(response.statusCode).toBe(404);
    expect(spy).not.toHaveBeenCalled();
  });

  it("should return 404 if mobile assigned secure contact is not found", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: 1 });
    jest
      .spyOn(secureContacts, "getMobileAssignedSecureContact")
      .mockResolvedValue([]);
    const response = await request.get("/secure-contacts/assigned");
    expect(response.statusCode).toBe(404);
    expect(response.body.detail).toBe(
      "Mobile assigned secure contact not found",
    );
  });

  it("should return 404 if mobile contact is missing required fields", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: 1 });
    jest
      .spyOn(secureContacts, "getMobileAssignedSecureContact")
      .mockResolvedValue([
        {
          id: "SecureMobile1",
          divert: {
            secureNumber: undefined,
            destinationNumber: "*********",
          },
        },
      ]);
    const response = await request.get("/secure-contacts/assigned");
    expect(response.statusCode).toBe(404);
    expect(response.body.detail).toBe(
      "Mobile assigned secure contact not found",
    );
  });

  it("should return 404 if mobile contact is diverted to reception", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: 1 });
    jest
      .spyOn(secureContacts, "getMobileAssignedSecureContact")
      .mockResolvedValue([{ id: "SecureMobile1", divertToReception: true }]);
    const response = await request.get("/secure-contacts/assigned");
    expect(response.statusCode).toBe(404);
    expect(response.body.detail).toBe(
      "Mobile assigned secure contact not found",
    );
  });

  it("should return mobile assigned secure contact in response", async () => {
    jest
      .spyOn(getCompanyDocModule, "getCompanyDoc")
      .mockResolvedValue({ id: 1, tradeId: 1 });
    jest
      .spyOn(secureContacts, "getMobileAssignedSecureContact")
      .mockResolvedValue([
        {
          id: "SecureMobile1",
          contactId: "123456",
          divert: {
            secureNumber: "07700900123",
            destinationNumber: "0*********0",
          },
        },
      ]);
    const response = await request.get("/secure-contacts/assigned");
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      id: "123456",
      secureNumber: "07700900123",
      destinationNumber: "0*********0",
    });
  });
});
