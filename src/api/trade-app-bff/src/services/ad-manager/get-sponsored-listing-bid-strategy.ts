import { Logger } from "@checkatrade/fastify-five";
import {
  GetSponsoredListingBidStrategyResponse,
  getSponsoredListingBidStrategyResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { adManagerHttpClient } from "./adManagerHttpClient";

export const getSponsoredListingBidStrategy = async (
  logger: Logger,
  campaignId: string,
): Promise<GetSponsoredListingBidStrategyResponse | undefined> => {
  try {
    const url = `/sponsored-search-campaigns/${campaignId}`;

    logger.info(
      { url, campaignId },
      "Fetching sponsored listing bid strategy from ad-manager service",
    );
    const response = await adManagerHttpClient.get(url);

    logger.info(
      {
        status: response.status,
        campaignId,
        bidStrategiesCount: response.data?.length,
      },
      "Successfully fetched sponsored listing bid strategy from ad-manager service",
    );

    return Value.Parse(getSponsoredListingBidStrategyResponse, response.data);
  } catch (error) {
    logger.error(
      { error, url: `/sponsored-search-campaigns/${campaignId}`, campaignId },
      "Failed to get sponsored listing bid strategy from ad-manager service",
    );
    throw error;
  }
};
