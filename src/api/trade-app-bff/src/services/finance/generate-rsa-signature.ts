import { httpClient } from "@checkatrade/axios";
import {
  ApiError,
  InternalServerError,
  UnprocessableEntityError,
} from "@checkatrade/errors";
import { Logger } from "@checkatrade/fastify-five";
import { gcp } from "@checkatrade/gcp";
import {
  RsaSignatureBody,
  RsaSignatureError,
  RsaSignatureResponse,
  rsaSignatureResponseSchema,
} from "@checkatrade/trade-bff-types";
import { TypeBoxError } from "@sinclair/typebox";
import { Value } from "@sinclair/typebox/value";

import { config } from "./config";

export const isRsaSignatureError = (
  response: RsaSignatureResponse | RsaSignatureError,
): response is RsaSignatureError => {
  return "type" in response && "status" in response;
};

export const generateRsaSignature = async (
  payload: RsaSignatureBody,
  logger: Logger,
): Promise<RsaSignatureResponse | RsaSignatureError> => {
  const token = await gcp.generateBearerToken(config.financeServiceHost);

  try {
    const response = await httpClient.post(
      `${config.financeServiceHost}/rsa-signatures`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );
    return Value.Parse(rsaSignatureResponseSchema, response.data);
  } catch (error) {
    if (error instanceof ApiError) {
      logger.error(error, "Failed to fetch rsa signature from zuora");
      throw error;
    }
    if (error instanceof TypeBoxError) {
      throw new UnprocessableEntityError(error.message);
    }
    throw new InternalServerError("Error getting RSA signature from Zuora");
  }
};
