import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { ForbiddenError, apiErrorSchema } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  GetSponsoredListingBidStrategyParams,
  GetSponsoredListingBidStrategyResponse,
  companyIdHeader,
  getSponsoredListingBidStrategyParams,
  getSponsoredListingBidStrategyResponse,
} from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { hasAccessToCompany } from "../../helpers/auth-helpers";
import { adManager } from "../../services/ad-manager";

export const getSponsoredListingBidStrategy: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: GetSponsoredListingBidStrategyParams;
  Reply: GetSponsoredListingBidStrategyResponse | Static<typeof apiErrorSchema>;
}> = {
  method: "GET",
  url: "/ad-manager/sponsored-search-campaigns/:campaignId",
  schema: {
    summary: "Get Sponsored Listing Bid Strategy",
    description: "Retrieves the bid strategy for a Sponsored Listing campaign",
    operationId: "getSponsoredListingBidStrategy",
    tags: ["Ad Manager", "Sponsored Listings"],
    headers: companyIdHeader,
    params: getSponsoredListingBidStrategyParams,
    response: {
      200: getSponsoredListingBidStrategyResponse,
      401: apiErrorSchema,
      403: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const authToken = authTradeCapi(req);
    const { campaignId } = req.params;

    if (!hasAccessToCompany(authToken, companyId)) {
      throw new ForbiddenError(
        "Only the account owner can get sponsored listing bid strategy",
      );
    }

    const { log: logger } = req;

    try {
      const bidStrategy = await adManager.getSponsoredListingBidStrategy(
        logger,
        campaignId,
      );
      return res.send(bidStrategy);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
