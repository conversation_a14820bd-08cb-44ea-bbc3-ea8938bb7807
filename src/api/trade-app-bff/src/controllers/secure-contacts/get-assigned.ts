import { authTrade } from "@checkatrade/auth-trade";
import { NotFoundError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  AssignedResponse,
  CompanyIdHeader,
  MobileAssigned,
  companyIdHeader,
  mobileAssignedSchema,
} from "@checkatrade/trade-bff-types";
import z from "zod";

import { getCompanyDoc } from "../../services/firebase/firestore/get-company";
import { secureContacts } from "../../services/secure-contacts";

// quick route to test the token generation for secure contacts api
export const getAssigned: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: MobileAssigned;
}> = {
  method: "GET",
  url: "/secure-contacts/assigned",
  schema: {
    summary: "Get assigned Secure Contacts",
    description: "Fetches assigned secure contact for a company",
    operationId: "getAssignedSecureContacts",
    tags: ["Secure Contacts"],
    headers: companyIdHeader,
    response: {
      200: z.toJSONSchema(mobileAssignedSchema),
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const company = await getCompanyDoc(companyId.toString());

    if (!company?.tradeId) {
      logger.error({ companyId }, "Company tradeId not found");
      throw new NotFoundError("Company tradeId not found");
    }

    const assigned: AssignedResponse =
      await secureContacts.getMobileAssignedSecureContact(
        company.tradeId,
        logger,
      );

    const mobile = assigned.find(
      (contact) => contact.id === "SecureMobile1" && !contact.divertToReception,
    );

    if (
      !mobile?.contactId ||
      !mobile?.divert?.secureNumber ||
      !mobile?.divert?.destinationNumber
    ) {
      throw new NotFoundError("Mobile assigned secure contact not found");
    }

    const mobileAssigned: MobileAssigned = {
      id: mobile.contactId,
      secureNumber: mobile.divert.secureNumber,
      destinationNumber: mobile.divert.destinationNumber,
    };

    return res.send(mobileAssigned);
  },
};
