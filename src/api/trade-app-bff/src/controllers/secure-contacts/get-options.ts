import { authTrade } from "@checkatrade/auth-trade";
import { NotFoundError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  FormattedOption,
  NumberType,
  SecureContactOption,
  SecureContactsOptionsResponse,
  companyIdHeader,
  secureContactsOptionsResponseSchema,
} from "@checkatrade/trade-bff-types";
import z from "zod";

import { getCompanyDoc } from "../../services/firebase/firestore/get-company";
import { secureContacts } from "../../services/secure-contacts";

export const getOptions: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: SecureContactsOptionsResponse;
}> = {
  method: "GET",
  url: "/secure-contacts/options",
  schema: {
    summary: "Get Secure Contacts options",
    description: "Fetches secure contact options for a company",
    operationId: "getSecureContactsOptions",
    tags: ["Secure Contacts"],
    headers: companyIdHeader,
    response: {
      200: z.toJSONSchema(secureContactsOptionsResponseSchema),
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const company = await getCompanyDoc(companyId.toString());

    if (!company?.tradeId) {
      logger.error({ companyId }, "Company tradeId not found");
      throw new NotFoundError("Company tradeId not found");
    }

    const options: SecureContactOption[] =
      await secureContacts.getSecureContactOptions(company.tradeId, logger);

    const primaryIdToNumberMap = new Map<string, string>();
    const uniqueOptions: FormattedOption[] = [];
    const secondaryOptionsMap = new Map<string, FormattedOption>();

    for (const option of options) {
      categorizeMobileNumberIntoPrimaryAndSecondaryOptions(
        option,
        secondaryOptionsMap,
        uniqueOptions,
        primaryIdToNumberMap,
      );
    }

    if (secondaryOptionsMap.size === 0) {
      return res.send({
        mobileOptions: uniqueOptions,
      });
    }

    for (const [id, secondaryOption] of secondaryOptionsMap) {
      addSecondaryOptionIfUnique(
        id,
        secondaryOption,
        uniqueOptions,
        primaryIdToNumberMap,
      );
    }

    return res.send({
      mobileOptions: uniqueOptions,
    });
  },
};

const formatOption = (option: SecureContactOption): FormattedOption => {
  const name =
    option.personalNumber ?
      [option.salutation, option.firstName, option.lastName]
        .filter(Boolean)
        .join(" ")
    : "Company number";
  return {
    id: option.id,
    name,
    number: option.number!,
  };
};

const categorizeMobileNumberIntoPrimaryAndSecondaryOptions = (
  option: SecureContactOption,
  secondaryOptionsMap: Map<string, FormattedOption>,
  uniqueOptions: FormattedOption[],
  primaryIdToNumberMap: Map<string, string>,
) => {
  if (option.numberType === NumberType.MOBILE && option.number) {
    if (option.id.endsWith("-Phone")) {
      secondaryOptionsMap.set(
        option.id.replace(/-Phone$/, ""),
        formatOption(option),
      );
    } else {
      uniqueOptions.push(formatOption(option));
      primaryIdToNumberMap.set(option.id, option.number);
    }
  }
};

const addSecondaryOptionIfUnique = (
  id: string,
  secondaryOption: FormattedOption,
  uniqueOptions: FormattedOption[],
  primaryIdToNumberMap: Map<string, string>,
) => {
  if (primaryIdToNumberMap.get(id) !== secondaryOption.number) {
    uniqueOptions.push(secondaryOption);
  }
};
