import z from "zod";

export enum AssignedType {
  MOBILE = "MOBILE",
  LANDLINE = "LANDLINE",
  DIRECTORY = "DIRECTORY",
}

export enum ProviderSyncStatus {
  SCHEDULED = "SCHEDULED",
  PENDING = "PENDING",
  SYNCED = "SYNCED",
}

export const divertSchema = z.object({
  locked: z.boolean().optional(),
  secureNumber: z.string().optional(),
  displaySecureNumber: z.string().optional(),
  destinationNumber: z.string().optional(),
  ntsNumber: z.string().optional(),
  syncStatus: z.enum(ProviderSyncStatus).optional(),
});

export const assignedSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  number: z.string().optional(),
  type: z.enum(AssignedType).optional(),
  areaCode: z.string().optional(),
  contactId: z.string().optional(),
  divert: divertSchema.optional(),
  divertToReception: z.boolean().optional(),
  tag: z.string().optional(),
});

export const mobileAssignedSchema = z.object({
  id: z.string(),
  secureNumber: z.string(),
  destinationNumber: z.string(),
});

export const patchAssignedParamsSchema = z.object({
  secureContactId: z.string(),
});

export const patchAssignedBodySchema = z.object({
  contactId: z.string(),
});

export const assignedResponseSchema = z.array(assignedSchema);

export type Assigned = z.infer<typeof assignedSchema>;
export type AssignedResponse = z.infer<typeof assignedResponseSchema>;
export type MobileAssigned = z.infer<typeof mobileAssignedSchema>;
export type PatchAssignedParams = z.infer<typeof patchAssignedParamsSchema>;
export type PatchAssignedBody = z.infer<typeof patchAssignedBodySchema>;
