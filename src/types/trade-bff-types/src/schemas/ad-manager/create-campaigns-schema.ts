import { Static, TSchema, Type } from "@sinclair/typebox";

// Reusable validation types
const dateString = Type.String({ format: "date" });
const positiveNumber = Type.Number({ minimum: 0.01 });

/**
 * Creates a non-empty array schema
 * @param schema The schema for array items
 * @param minItems Minimum number of items (default: 1)
 * @returns A schema for a non-empty array
 */
const nonEmptyArray = <T extends TSchema>(schema: T, minItems = 1) =>
  Type.Array(schema, { minItems });

/**
 * Geography types for campaign targeting
 */
export enum GeographyType {
  PostcodeArea = "PostcodeArea",
  PostcodeDistrict = "PostcodeDistrict",
  PostcodeSector = "PostcodeSector",
  DirectoryArea = "DirectoryArea",
}

/**
 * Search types for campaign targeting
 */
export enum CatSearchType {
  LISTING = "Listings",
  RAQ = "RequestAQuote",
}

/**
 * Bid strategies for campaign optimization
 */
export enum BidStrategy {
  Player = "player",
  Leader = "leader",
  Competitor = "competitor",
  None = "none",
}

/**
 * Pacing types for campaign optimization
 */
export enum PacingType {
  ASAP = "ASAP",
  Evenly = "Evenly",
  Accelerated = "Accelerated",
}

/**
 * Campaign types
 */
export enum CampaignType {
  MDP_SPONSORED_SEARCH = "MdpSponsoredSearch",
}

/**
 * Category schema for campaign targeting
 */
const categorySchema = Type.Object({
  categoryId: Type.Number(),
  isSearchable: Type.Boolean(),
  name: Type.Optional(Type.String()),
});

/**
 * Sub-category schema for campaign targeting
 */
const subCategorySchema = Type.Object({
  categoryId: Type.Number(),
  parentCategoryId: Type.Number(),
  name: Type.Optional(Type.String()),
});

/**
 * Geography schema for campaign targeting
 */
const geographySchema = Type.Object({
  value: Type.String(),
  type: Type.Enum(GeographyType),
});

/**
 * MDP Sponsored Search schema for campaign configuration
 */
const mdpSponsoredSearchSchema = Type.Object({
  endDate: Type.Optional(Type.Union([dateString, Type.Null()])),
  bidAmount: Type.Optional(Type.Union([positiveNumber, Type.Null()])),
  primaryCampaignId: Type.String(),
  searchType: Type.Enum(CatSearchType),
  bidStrategy: Type.Optional(Type.Enum(BidStrategy)),
  pacing: Type.Optional(Type.Enum(PacingType)),
});

/**
 * Campaign schema for creating Sponsored Listings campaigns
 */
const campaignSchema = Type.Object({
  type: Type.Optional(Type.Enum(CampaignType)),
  maxBudget: positiveNumber,
  pausedUntil: Type.Union([dateString, Type.Null()]),
  budgetPeriod: Type.Optional(dateString),
  category: categorySchema,
  subCategories: nonEmptyArray(subCategorySchema),
  geographies: nonEmptyArray(geographySchema),
  mdpSponsoredSearch: mdpSponsoredSearchSchema,
});

/**
 * Request schema for creating Sponsored Listings campaigns
 */
export const createCampaignsRequestSchema = Type.Array(campaignSchema, {
  minItems: 1,
});

/**
 * Response schema for created campaign IDs
 */
export const createCampaignsResponseSchema = Type.Array(Type.String());

export type CreateCampaignsRequest = Static<
  typeof createCampaignsRequestSchema
>;
export type CreateCampaignsResponse = Static<
  typeof createCampaignsResponseSchema
>;
