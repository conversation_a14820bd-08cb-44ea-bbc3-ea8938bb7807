import { Static, Type } from "@sinclair/typebox";

/**
 * Bid strategy types for sponsored listing campaigns
 */
export enum BidStrategyType {
  Player = "player",
  Leader = "leader",
  Competitor = "competitor",
  None = "none",
}

/**
 * Path parameters for getting sponsored listing bid strategy
 */
export const getSponsoredListingBidStrategyParams = Type.Object({
  campaignId: Type.String({ format: "uuid" }),
});

/**
 * Response schema for sponsored listing bid strategy
 */
export const getSponsoredListingBidStrategyResponse = Type.Object(
  Type.Enum(BidStrategyType),
);

export type GetSponsoredListingBidStrategyParams = Static<
  typeof getSponsoredListingBidStrategyParams
>;
export type GetSponsoredListingBidStrategyResponse = Static<
  typeof getSponsoredListingBidStrategyResponse
>;
