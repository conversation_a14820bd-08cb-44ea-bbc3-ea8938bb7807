# -*- mode: yaml -*-
# +----------------------------------------------------------------------------+
# | /:\ gitStream: Workflow automation for the code review process.            |
# +----------------------------------------------------------------------------+
# | This file contains one or more /:\ gitStream automations:                  |
# | https:// docs.gitstream.cm                                                 |
# |                                                                            |
# | gitStream uses YAML syntax with nunjucks templating via Jinja 2.           |
# |                                                                            |
# | Automations follow an "if this, then that" execution format.               |
# | More info here: https://docs.gitstream.cm/how-it-works/                    |
# |                                                                            |
# +----------------------------------------------------------------------------+

# /:\ gitStream Reference Docs:
#    Context Variables: https://docs.gitstream.cm/context-variables/
#    Filter Functions: https://docs.gitstream.cm/filter-functions/
#    Automation Actions: https://docs.gitstream.cm/automation-actions/

manifest:
  version: 1.0

# +----------------------------------------------------------------------------+
# | Config                                                                     |
# +----------------------------------------------------------------------------+

# Files to exclude from gitStream automations.
config:
  ignore_files:
    - 'package-lock.json'

# +----------------------------------------------------------------------------+
# | Automations
# +----------------------------------------------------------------------------+

automations:

  # Use LinearB's AI service to review the changes
  linearb_ai_review:
    on:
      - pr_created
      - commit
    if:
      - {{ not pr.draft }}
      - {{ pr.author | match(list=['github-actions', 'dependabot', '[bot]']) | nope }}
    run:
      - action: code-review@v1

  # Flag PRs that are missing a Jira ticket reference in the title or description.
  label_missing_jira_info:
    if:
      - {{ not (has.jira_ticket_in_title or has.jira_ticket_in_desc) }}
    run:
      - action: add-label@v1
        args:
          label: "missing-jira"
          color: {{ colors.red }}

  # Flag PRs that delete files to highlight potential refactors that need extra scrutiny.
  flag_deleted_files:
    if:
      - {{ has.deleted_files }}
    run:
      - action: add-label@v1
        args:
          label: 🗑️ Deleted files
          color: {{ colors.orange }}

  enforce_pr_title:
    if:
      - {{ pr.title | match(regex=titlePolicy.titleRegex) | nope }}
    run:
      - action: request-changes@v1
        args:
          comment: |
            All PRs must be titled according to our semantic naming policy: `<type>(<jira>): <short summary>`

            Type must be: (feature|bugfix|fix|next|story|chore)
            JIRA reference: (e.g. MPL-123)

            Example: `feature(MPL-123): AI feature to take over the world`

  # request_screenshot:
    # Triggered for PRs that lack an image file or link to an image in the PR description
  #  if:
  #    - {{ not (has.screenshot_link or has.image_uploaded or has.image_or_video_uploaded) }}
  #  run:
  #    - action: add-label@v1
  #      args:
  #       label: 'no-screenshot'
  #        color: '#FF000A'
  #    - action: add-comment@v1
  #      args:
  #        comment: |
  #          Be a life saver 🛟 by adding a screenshot of the changes you made.

  review_todo_comments:
    if:
      - {{ source.diff.files | matchDiffLines(regex=r/(TODO)|(todo)/) | some }}
    run:
      - action: add-comment@v1
        args:
          comment: |
            This PR contains a TODO statement. Please check to see if they should be removed.

  block_do_not_merge:
    if:
      - {{ has.do_not_merge_label }}
    run:
      - action: request-changes@v1
        args:
          comment: Blocking merge. "do not merge" label is applied.

# +----------------------------------------------------------------------------+
# | Custom Expressions                                                         |
# | https://docs.gitstream.cm/how-it-works/#custom-expressions                 |
# +----------------------------------------------------------------------------+

has:
  jira_ticket_in_title: {{ pr.title | includes(regex=r/\b[A-Za-z]+-\d+\b/) }}
  jira_ticket_in_desc: {{ pr.description | includes(regex=r/atlassian.net\/browse\/\w{1,}-\d{3,4}/) }}
  deleted_files: {{ source.diff.files | map(attr='new_file') | match(term='/dev/null') | some }}
  do_not_merge_label: {{ pr.labels | match(term=':stop_sign: do not merge') | some }}
  screenshot_link: {{ pr.description | includes(regex=r/!\[.*\]\(.*(jpg|svg|png|gif|psd).*\)/) }}
  image_uploaded: {{ pr.description | includes(regex=r/<img.*src.*(jpg|svg|png|gif|psd).*>/) }}
  image_or_video_uploaded: {{ pr.description | includes(regex=r/https:\/\/github\.com\/([^\/]+)\/([^\/]+)\/assets\/(\d+)\/([^\/]+)/) }}

titlePolicy:
    titleRegex: r/\b(feat|feature|bugfix|fix|next|story|chore)\b\s*\(([A-Za-z]+-\d+)\).*/

# These are all of the colors in GitHub's default label color palette.
colors:
  red: 'b60205'
  orange: 'd93f0b'
  yellow: 'fbca04'
  green: '0e8a16'
  blue: '1d76db'
  purple: '5319e7'
