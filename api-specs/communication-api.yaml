openapi: '3.0.0'
info:
  title: Communication Api
  description: Provides an API for the communications data
  version: v3
security:
  - bearerAuth: []
paths:
  '/api/trade/user/get-external-id':
    get:
      security:
        - Bearer: []
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  external_id:
                    type: string
