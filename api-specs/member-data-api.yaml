openapi: 3.0.1
info:
  title: MemberData.Api
  contact:
    name: Trade Experience Team
    url: https://github.com/cat-home-experts/gcp-member-data
  version: *******
paths:
  '/api/v1/accreditationlibrary/{accreditationId}/accreditation':
    post:
      tags:
        - AccreditationLibrary
      summary: Updates or inserts into the Firestore accreditation library for a given accreditation
      operationId: UpsertAccreditation
      parameters:
        - name: accreditationId
          in: path
          description: Accreditation Id
          required: true
          schema:
            type: integer
            format: int32
      requestBody:
        description: Accreditation library Dto
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccreditationLibraryDto'
          text/json:
            schema:
              $ref: '#/components/schemas/AccreditationLibraryDto'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AccreditationLibraryDto'
      responses:
        '200':
          description: Success
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  '/api/v1/company/{companyId}/credit':
    get:
      tags:
        - Company
      summary: Retrieves the given company current maximum credit and credit safe limit
      operationId: GetTradeCredit
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/SalesforceCreditDto'
            application/json:
              schema:
                $ref: '#/components/schemas/SalesforceCreditDto'
            text/json:
              schema:
                $ref: '#/components/schemas/SalesforceCreditDto'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  '/api/v1/company/{companyId}/member-promise':
    get:
      tags:
        - Company
      summary: "Retrieves the given company's current status of a member promise which helps a member\r\nto take actions to improve the performance of their membership."
      description: "Description\r\n-----------\r\n_ Retrieves the given company's current status of member promise, as configured by\r\nCheckatrade._\r\n_The source of the data is Salesforce._\r\n_The results are not cached._"
      operationId: GetMemberPromise
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/MemberPromiseReadDto'
            application/json:
              schema:
                $ref: '#/components/schemas/MemberPromiseReadDto'
            text/json:
              schema:
                $ref: '#/components/schemas/MemberPromiseReadDto'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  /api/v1/vettinghandoff:
    post:
      tags:
        - CompanyAccreditationsVettingHandoff
      summary: Updates or inserts into the Firestore company accreditations collection.
      operationId: UpsertAccreditations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyAccreditationHandoffMessage'
          text/json:
            schema:
              $ref: '#/components/schemas/CompanyAccreditationHandoffMessage'
          application/*+json:
            schema:
              $ref: '#/components/schemas/CompanyAccreditationHandoffMessage'
      responses:
        '200':
          description: Success
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  '/api/v1/contact/{userId}/preferences':
    get:
      tags:
        - Contact
      summary: Provides a list of Marketing Preferences for a given UserId
      description: "Description\r\n-----------\r\n_This endpoint will return a list of Marketing Preferences for an individual user's GUID_\r\n_The source of the data is Salesforce_\r\n_No strict ordering has been applied_\r\n_The results are not cached_\r\n[click for Mobile Page](https://checkatrade.atlassian.net/wiki/spaces/MPT/overview?homepageId=3286892548)\r\n[click for Architectural\r\nDiagrams](https://checkatrade.atlassian.net/wiki/spaces/MPT/pages/3337716085/TE+Salesforce+GCP+Architecture)"
      operationId: GetPreferences
      parameters:
        - name: userId
          in: path
          description: ''
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ContactPreferencesDto'
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ContactPreferencesDto'
            text/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ContactPreferencesDto'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
    patch:
      tags:
        - Contact
      summary: Updates the Marketing Preferences preferences for a given user
      description: "Description\r\n-----------\r\n_This endpoint will update the Marketing Preferences for an individual user's GUID_\r\n_The source of the data is Salesforce_\r\n[click for Mobile Page](https://checkatrade.atlassian.net/wiki/spaces/MPT/overview?homepageId=3286892548)\r\n[click for Architectural\r\nDiagrams](https://checkatrade.atlassian.net/wiki/spaces/MPT/pages/3337716085/TE+Salesforce+GCP+Architecture)"
      operationId: PatchPreferences
      parameters:
        - name: userId
          in: path
          description: ''
          required: true
          schema:
            type: string
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactPreferencesMessageDto'
          text/json:
            schema:
              $ref: '#/components/schemas/ContactPreferencesMessageDto'
          application/*+json:
            schema:
              $ref: '#/components/schemas/ContactPreferencesMessageDto'
      responses:
        '200':
          description: Success
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  '/api/v1/leads/secure-contacts-logs/{companyId}':
    get:
      tags:
        - Leads
      summary: Gets paginated secure contact logs for the given companyId
      description: "    Description\r\n    -----------\r\n    _Gets the call and sms logs from secure contacts for the given companyId._\r\n\t_Only users with a valid token that grants access to the provided companyId will get results._\r\n\t_The source of the data is Secure Contacts API._\r\n    _The results are not cached._"
      operationId: GetSecureContactsLogsForCompany
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: Page
          in: query
          schema:
            type: integer
            format: int32
        - name: Limit
          in: query
          schema:
            type: integer
            format: int32
        - name: FromDate
          in: query
          schema:
            type: string
            format: date-time
        - name: ToDate
          in: query
          schema:
            type: string
            format: date-time
        - name: SortBy
          in: query
          schema:
            $ref: '#/components/schemas/InboundSortableFields'
        - name: SortDirection
          in: query
          schema:
            $ref: '#/components/schemas/SortDirection'
        - name: IncludeUnsuccessful
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecureContactLogDto'
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecureContactLogDto'
            text/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SecureContactLogDto'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
components:
  schemas:
    Accreditation:
      type: object
      properties:
        Accred_ID:
          type: integer
          format: int32
        Company_ID:
          type: integer
          format: int32
        name:
          type: string
          nullable: true
        Accreditation_Type:
          type: integer
          format: int32
        Expiry_Date:
          type: string
          format: date-time
          nullable: true
        Registration_Number:
          type: string
          nullable: true
        SFAccred_Status:
          type: string
          nullable: true
        Accred_Status:
          type: integer
          format: int32
        Accred_Note:
          type: string
          nullable: true
        Accred_Proof:
          type: string
          nullable: true
        Visible_On_Profile:
          type: boolean
        Approved_Dt:
          type: string
          format: date-time
          nullable: true
        Modify_Dt:
          type: string
          format: date-time
          nullable: true
        Modify_by:
          type: integer
          format: int32
          nullable: true
        Status_ind:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    AccreditationLibraryDto:
      type: object
      properties:
        correlationId:
          type: string
          nullable: true
        originator:
          type: string
          nullable: true
        accreditationId:
          type: integer
          format: int32
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        logoFilename:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        urlText:
          type: string
          nullable: true
        visibleOnProfile:
          type: boolean
        requiresApproval:
          type: boolean
        canExpire:
          type: boolean
        isDeleted:
          type: boolean
      additionalProperties: false
    CommunicationMethod:
      format: string
      enum:
        - CALL
        - SMS
      type: string
    CompanyAccreditationHandoffMessage:
      type: object
      properties:
        accreditations:
          type: array
          items:
            $ref: '#/components/schemas/Accreditation'
          nullable: true
        correlationId:
          type: string
          nullable: true
        originator:
          type: string
          nullable: true
      additionalProperties: false
    ContactPreferencesBundleDto:
      type: object
      properties:
        group:
          type: string
          nullable: true
        preferences:
          type: object
          additionalProperties:
            type: boolean
          nullable: true
      additionalProperties: false
    ContactPreferencesDto:
      type: object
      properties:
        group:
          type: string
          nullable: true
        preferences:
          $ref: '#/components/schemas/PreferencesDto'
      additionalProperties: false
    ContactPreferencesMessageDto:
      type: object
      properties:
        contact-preferences:
          type: array
          items:
            $ref: '#/components/schemas/ContactPreferencesBundleDto'
          nullable: true
      additionalProperties: false
    InboundSortableFields:
      format: string
      enum:
        - contactTime
      type: string
    MemberPromiseReadDto:
      type: object
      properties:
        status:
          type: boolean
        errorMessage:
          type: string
          nullable: true
      additionalProperties: false
    PreferencesDto:
      type: object
      properties:
        email:
          type: boolean
        phone:
          type: boolean
        post:
          type: boolean
        sms:
          type: boolean
      additionalProperties: false
    ProblemDetails:
      type: object
      properties:
        type:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        status:
          type: integer
          format: int32
          nullable: true
        detail:
          type: string
          nullable: true
        instance:
          type: string
          nullable: true
      additionalProperties: false
    SalesforceCreditDto:
      type: object
      properties:
        maximumCredit:
          type: number
          format: double
          nullable: true
        creditSafeLimit:
          type: number
          format: double
          nullable: true
        errorMessage:
          type: string
          nullable: true
      additionalProperties: false
    SecureContactLogDto:
      type: object
      properties:
        secureContactId:
          type: string
          nullable: true
        rowKey:
          type: string
          nullable: true
        communicationMethod:
          $ref: '#/components/schemas/CommunicationMethod'
        fromPhoneNumber:
          type: string
          nullable: true
        contactTime:
          type: string
          format: date-time
        wasSuccessfulCall:
          type: boolean
        durationSeconds:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    SortDirection:
      format: string
      enum:
        - ASCENDING
        - DESCENDING
      type: string
    ValidationProblemDetails:
      type: object
      properties:
        type:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        status:
          type: integer
          format: int32
          nullable: true
        detail:
          type: string
          nullable: true
        instance:
          type: string
          nullable: true
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          nullable: true
          readOnly: true
      additionalProperties: false
