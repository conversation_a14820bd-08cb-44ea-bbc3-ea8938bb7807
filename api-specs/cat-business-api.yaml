openapi: 3.0.0
info:
  title: CAT for Business API
  version: 1.0.0
  description: API documentation for CAT for Business API
servers:
  - url: http://localhost:8080/api
    description: Development server
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication
  security:
    - bearerAuth: []
  schemas:
    JobDocumentType:
      type: string
      enum:
        - JOB_PHOTO
        - JOB_COMPLETION_PHOTO
        - JOB_ISSUE_REPORT_PHOTO
        - INVOICE
        - OTHER
      description: Type of document associated with a job
    TimeSlot:
      type: string
      enum:
        - MORNING
        - AFTERNOON
        - EVENING
      description: 'Available time slots for jobs (MORNING: 09:00AM, AFTERNOON:
        12:00PM, EVENING: 05:00PM)'
    JobStatus:
      type: string
      enum:
        - PENDING_PAYMENT
        - REQUESTED
        - SCHEDULED
        - EXPIRED
        - IN_PROGRESS
        - PENDING_COMPLETION_APPROVAL
        - COMPLETED
        - CANCELED
        - NEEDS_ATTENTION
      description: Current status of the job
      enumDescriptions:
        PENDING_PAYMENT:
          Initial state when a job is created but payment hasn't been
          processed. Job is not visible to trades until payment is successful.
        REQUESTED:
          Initial state when a business creates a new job. Job is visible to
          matched trades in their "Business Jobs" tab.
        SCHEDULED: Job has been accepted by a trade and scheduled.
        EXPIRED:
          Job has expired because all matched trades rejected it or 24 hours
          before earliest job time passed without any acceptance.
        IN_PROGRESS: Trade has started working on the job.
        PENDING_COMPLETION_APPROVAL:
          Trade has marked the job as complete, waiting for
          business customer to approve completion.
        COMPLETED: Job has been completed and approved by the business.
        CANCELED: Job was canceled by either the business or the trade.
        NEEDS_ATTENTION: Job requires attention due to a dispute or issue.
    JobUrgency:
      type: string
      enum:
        - Standard
        - Flexible
        - Urgent
      description: The urgency level of the job
    PropertyType:
      type: string
      enum:
        - House
        - Apartment/Flat
        - Office
        - Retail
        - Industrial/Warehouse
        - Hotel/Restaurant
        - School/University
        - Hospital/Clinic
        - Other
      description: Type of property where the job will be performed
    JobDate:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the job date
        jobId:
          type: string
          format: uuid
          description: ID of the associated job
        date:
          type: string
          format: date-time
          description: The date and time for the job
        priority:
          type: integer
          minimum: 1
          maximum: 3
          description: Priority of this date (1-3)
        timeSlot:
          $ref: '#/components/schemas/TimeSlot'
          description: The time slot for this job date
      required:
        - id
        - jobId
        - date
        - priority
        - timeSlot
    Job:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the job
        companyId:
          type: string
          format: uuid
          description: ID of the company that created the job
        userId:
          type: string
          format: uuid
          description: ID of the user (trade) assigned to the job
          nullable: true
        categoryName:
          type: string
          description: Name of the job category
        categoryId:
          type: number
          description: ID of the job category
        categoryParentId:
          type: number
          description: ID of the parent category
        estimatedHours:
          type: number
          description: Estimated hours to complete the job
        title:
          type: string
          description: Job title
        description:
          type: string
          description: Detailed description of the job
        equipmentNeeded:
          type: string
          description: Materials needed for the job
          nullable: true
        jobUrgency:
          $ref: '#/components/schemas/JobUrgency'
        price:
          type: number
          description: Price of the job
          minimum: 0
          format: float
        status:
          $ref: '#/components/schemas/JobStatus'
          type: string
          enum:
            - REQUESTED
            - SCHEDULED
            - EXPIRED
            - IN_PROGRESS
            - PENDING_COMPLETION_APPROVAL
            - COMPLETED
            - CANCELED
            - NEEDS_ATTENTION
          description: Current status of the job
        expiresAt:
          type: string
          format: date-time
          description: When the job request expires (24 hours before earliest job time)
        cancellation:
          $ref: '#/components/schemas/JobCancellation'
          nullable: true
        completionSignature:
          type: string
          nullable: true
          description: Digital signature for job completion
        completionApprovedAt:
          type: string
          format: date-time
          nullable: true
          description: When the job completion was approved
        matchedTradesCount:
          type: integer
          description: Number of trades matched to this job
          minimum: 0
        dates:
          type: array
          items:
            $ref: '#/components/schemas/JobDate'
        location:
          $ref: '#/components/schemas/JobLocation'
        issueReports:
          type: array
          items:
            $ref: '#/components/schemas/JobIssueReport'
        history:
          type: array
          items:
            $ref: '#/components/schemas/JobHistory'
        edits:
          type: array
          items:
            $ref: '#/components/schemas/JobEdit'
        documents:
          type: array
          items:
            $ref: '#/components/schemas/JobDocument'
        tradeMatches:
          type: array
          items:
            $ref: '#/components/schemas/TradeJobMatch'
        createdAt:
          type: string
          format: date-time
          description: When the job was created
        updatedAt:
          type: string
          format: date-time
          description: When the job was last updated
        company:
          $ref: '#/components/schemas/Company'
        user:
          $ref: '#/components/schemas/User'
      required:
        - id
        - companyId
        - categoryName
        - categoryId
        - categoryParentId
        - estimatedHours
        - title
        - description
        - jobUrgency
        - price
        - status
        - expiresAt
        - matchedTradesCount
        - createdAt
        - updatedAt
    JobLocation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Location ID
        jobId:
          type: string
          format: uuid
          description: Associated job ID
        addressKey:
          type: string
          description: Unique key for the address
        address:
          type: string
          description: Full address
        postcode:
          type: string
          description: Postcode
        onsiteContactName:
          type: string
          description: Name of the onsite contact person
        onsiteContactPhone:
          type: string
          description: Phone number of the onsite contact
        onsiteContactEmail:
          type: string
          format: email
          description: Email of the onsite contact
        businessContactName:
          type: string
          description: Name of the business contact person
        businessContactPhone:
          type: string
          description: Phone number of the business contact
        businessContactEmail:
          type: string
          format: email
          description: Email of the business contact
        parkingAvailable:
          type: boolean
          description: Whether parking is available
        parkingRestrictions:
          type: string
          nullable: true
          description: Details about parking restrictions if any
        propertyType:
          $ref: '#/components/schemas/PropertyType'
          nullable: true
        customerPresent:
          type: boolean
          description: Whether customer will be present
        sitePreparation:
          type: string
          nullable: true
          description: Site preparation details if any
    JobDocument:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the document
        jobId:
          type: string
          format: uuid
          description: ID of the associated job
        filePath:
          type: string
          description: Path to access the file (returns signed URL when retrieved)
          format: uri
        fileName:
          type: string
          description: Original name of the file
        fileType:
          type: string
          description: MIME type of the file
        fileSize:
          type: number
          description: Size of the file in bytes
        description:
          type: string
          nullable: true
          description: Optional description of the file
        documentType:
          $ref: '#/components/schemas/JobDocumentType'
          description: Type of the document
          default: OTHER
        uploadedAt:
          type: string
          format: date-time
          description: When the file was uploaded
        uploadedById:
          type: string
          format: uuid
          nullable: true
          description: ID of the user who uploaded the file
      required:
        - id
        - jobId
        - filePath
        - fileName
        - fileType
        - fileSize
        - documentType
        - uploadedAt
    JobIssueReport:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the issue report
        jobId:
          type: string
          format: uuid
          description: ID of the job this issue is reported for
        issueType:
          type: string
          description: Type of issue being reported
        description:
          type: string
          description: Detailed description of the issue
        reportedAt:
          type: string
          format: date-time
          description: When the issue was reported
        reportedBy:
          type: string
          format: uuid
          description: ID of the user who reported the issue
      required:
        - id
        - jobId
        - issueType
        - description
        - reportedAt
        - reportedBy
    TradeJobMatch:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the match
        jobId:
          type: string
          format: uuid
          description: ID of the job
        tradeId:
          type: string
          format: uuid
          description: ID of the trade
        status:
          type: string
          enum:
            - PENDING
            - ACCEPTED
            - REJECTED
          default: PENDING
          description: Status of the match
        rejectionReason:
          type: string
          nullable: true
          description: Reason for rejection if status is REJECTED
        selectedDateId:
          type: string
          format: uuid
          nullable: true
          description: ID of the selected job date
        oneDayReminderSent:
          type: boolean
          default: false
          description: Whether the one day reminder has been sent
        twoHourReminderSent:
          type: boolean
          default: false
          description: Whether the two hour reminder has been sent
        selectedDate:
          $ref: '#/components/schemas/JobDate'
          nullable: true
          description: Selected date and time slot
        job:
          $ref: '#/components/schemas/Job'
          description: Associated job details
        createdAt:
          type: string
          format: date-time
          description: When the match was created
        updatedAt:
          type: string
          format: date-time
          description: When the match was last updated
        suggestedPrice:
          type: number
          format: float
          nullable: true
          description: Suggested price if provided during rejection
      required:
        - id
        - jobId
        - tradeId
        - status
        - oneDayReminderSent
        - twoHourReminderSent
        - createdAt
        - updatedAt
    ChangeSource:
      type: string
      enum:
        - SYSTEM
        - USER
        - TRADE
      description: Source of a job history change
    JobHistory:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the history entry
        jobId:
          type: string
          format: uuid
          description: ID of the associated job
        status:
          $ref: '#/components/schemas/JobStatus'
        changedAt:
          type: string
          format: date-time
          description: When the change occurred
        changeSource:
          $ref: '#/components/schemas/ChangeSource'
          default: SYSTEM
        referenceId:
          type: string
          format: uuid
          nullable: true
          description:
            ID of who made the change (can be userId, tradeId, or null for
            system)
        changedByName:
          type: string
          nullable: true
          maxLength: 255
          description:
            Name of who made the change (e.g. "John Doe", "Trade Company Name",
            "System")
        changeDetails:
          type: string
          nullable: true
          description: Detailed description of what happened
      required:
        - id
        - jobId
        - status
        - changedAt
        - changeSource
    JobEdit:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the edit
        jobId:
          type: string
          format: uuid
          description: ID of the associated job
        fieldName:
          type: string
          description: Name of the field that was edited
        oldValue:
          type: string
          description: Previous value of the field
        newValue:
          type: string
          description: New value of the field
        editedAt:
          type: string
          format: date-time
          description: When the edit was made
        createdById:
          type: string
          format: uuid
          nullable: true
          description: ID of the user who made the edit
      required:
        - id
        - jobId
        - fieldName
        - oldValue
        - newValue
        - editedAt
    BusinessCancellationType:
      type: string
      enum:
        - MORE_THAN_24H
        - WITHIN_24H
        - WITHIN_2H
        - NO_ACCESS
        - INACCURATE_DESCRIPTION
        - TRADE_CANCELLATION
      description: Type of business cancellation
    CancelledBy:
      type: string
      enum:
        - BUSINESS
        - TRADE
        - SYSTEM
      description: Who cancelled the job
    JobCancellation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the cancellation
        jobId:
          type: string
          format: uuid
          description: ID of the cancelled job
        userCancellationType:
          type: string
          description: User-provided cancellation type
        businessCancellationType:
          $ref: '#/components/schemas/BusinessCancellationType'
        reason:
          type: string
          description: Reason for cancellation
        cancelledBy:
          $ref: '#/components/schemas/CancelledBy'
        businessRefund:
          type: integer
          description:
            Amount in minor units (pence). This is the amount refunded to the
            business, including VAT.
        tradeCompensation:
          type: integer
          description:
            Amount in minor units (pence). This is what the trade receives
            after deducting Checkatrade fee, including VAT.
        checkatradeFee:
          type: integer
          description:
            Amount in minor units (pence). Our service fee calculated on
            pre-VAT amount and then VAT added.
        totalCancellationFee:
          type: integer
          description:
            Amount in minor units (pence). Total fee charged for cancellation
            (trade_compensation + checkatrade_fee), including VAT.
        effectiveRate:
          type: number
          format: decimal
          description: The effective service fee percentage, calculated on pre-VAT amounts.
          minimum: 0
          maximum: 100
        cancelledByUserId:
          type: string
          format: uuid
          nullable: true
          description: ID of the user who cancelled the job
        tradeCompanyId:
          type: string
          nullable: true
          description: If cancelled by a trade, this will store the trade's company ID
        cancelledAt:
          type: string
          format: date-time
          description: When the job was cancelled
        scheduledDate:
          type: string
          format: date-time
          nullable: true
          description:
            If the job had an accepted match at the time of cancellation, this
            will store the scheduled date
        eligibleForRematch:
          type: boolean
          default: false
          description:
            Whether the job was eligible for rematch after cancellation (only
            applicable for trade cancellations)
      required:
        - id
        - jobId
        - userCancellationType
        - businessCancellationType
        - reason
        - cancelledBy
        - businessRefund
        - tradeCompensation
        - checkatradeFee
        - totalCancellationFee
        - effectiveRate
        - cancelledAt
        - eligibleForRematch
    Company:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the company
        name:
          type: string
          description: Company name
        size:
          type: string
          description: Size of the company
        businessType:
          type: string
          description: Type of business
        status:
          type: string
          enum:
            - ACTIVE
            - INACTIVE
            - SUSPENDED
          description: Company status
        createdAt:
          type: string
          format: date-time
          description: When the company was created
        updatedAt:
          type: string
          format: date-time
          description: When the company was last updated
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the user
        email:
          type: string
          format: email
          description: User's email address
        firstName:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
        companyId:
          type: string
          format: uuid
          description: ID of the associated company
        roles:
          type: array
          items:
            type: string
          description: User's roles
        status:
          type: string
          enum:
            - ACTIVE
            - INACTIVE
          description: User's status
        createdAt:
          type: string
          format: date-time
          description: When the user was created
        updatedAt:
          type: string
          format: date-time
          description: When the user was last updated
paths:
  /trade-jobs/matches:
    get:
      security:
        - bearerAuth: []
      summary: Get all jobs matched to the authenticated trade
      tags:
        - Trade Jobs
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number for pagination
        - in: query
          name: size
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          description: Number of items per page
        - in: query
          name: sortBy
          schema:
            type: string
            enum:
              - price
              - date
          description: Field to sort by
        - in: query
          name: sortDirection
          schema:
            type: string
            enum:
              - ASC
              - DESC
          description: Sort direction
        - in: query
          name: statuses
          schema:
            type: string
          description: Comma-separated list of job statuses to filter by
      responses:
        '200':
          description: List of matched jobs with pagination info
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                        status:
                          type: string
                          enum:
                            - PENDING
                            - ACCEPTED
                            - REJECTED
                        job:
                          type: object
                          properties:
                            id:
                              type: string
                              format: uuid
                            status:
                              type: string
                              enum:
                                - REQUESTED
                                - SCHEDULED
                                - IN_PROGRESS
                                - COMPLETED
                                - EXPIRED
                                - CANCELED
                                - PENDING_COMPLETION_APPROVAL
                                - NEEDS_ATTENTION
                                - PENDING_PAYMENT
                            price:
                              type: number
                            description:
                              type: string
                            companyName:
                              type: string
                            location:
                              type: object
                              properties:
                                postcode:
                                  type: string
                                address:
                                  type: string
                            dates:
                              type: array
                              items:
                                type: object
                                properties:
                                  id:
                                    type: string
                                    format: uuid
                                  date:
                                    type: string
                                    format: date-time
                                  timeSlot:
                                    type: string
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                        description: Current page number
                      size:
                        type: integer
                        description: Number of items per page
                      total:
                        type: integer
                        description: Total number of items
                      totalPages:
                        type: integer
                        description: Total number of pages
        '400':
          description: Invalid pagination, sorting, or status parameters
        '401':
          description: Unauthorized - Authentication required
        '500':
          description: Server error
  /trade-jobs/matches/{jobId}/accept:
    post:
      security:
        - bearerAuth: []
      summary: Accept a job that was matched to the trade
      tags:
        - Trade Jobs
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
            format: uuid
          description: Job ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - selectedDateId
              properties:
                selectedDateId:
                  type: string
                  format: uuid
                  description: ID of the selected time slot from job.dates
      responses:
        '200':
          description: Job accepted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TradeJobMatch'
        '400':
          description: Job ID or selected time slot is required
        '401':
          description: Unauthorized - Authentication required
        '404':
          description: Job not found
        '409':
          description: Job is no longer available or already accepted
        '500':
          description: Server error
  /trade-jobs/matches/{jobId}/reject:
    post:
      security:
        - bearerAuth: []
      summary: Reject a job with optional price feedback
      tags:
        - Trade Jobs
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
            format: uuid
          description: Job ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - reason
              properties:
                reason:
                  type: string
                  description: Reason for rejecting the job
                suggestedPrice:
                  type: number
                  format: float
                  description: Optional suggested price for the job
      responses:
        '200':
          description: Job rejected successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TradeJobMatch'
        '400':
          description: Job ID and reason are required
        '401':
          description: Unauthorized - Authentication required
        '404':
          description: Job match not found or already processed
        '500':
          description: Server error
  /trade-jobs/{jobId}/complete:
    post:
      security:
        - bearerAuth: []
      summary: Mark a job as complete (only available to assigned trade)
      tags:
        - Trade Jobs
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
            format: uuid
          description: Job ID
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Completion photos (max 5 files, 10MB each)
      responses:
        '200':
          description: Job marked as complete successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Job'
        '400':
          description: Job ID is required
        '401':
          description: Unauthorized - Authentication required
        '403':
          description: Only assigned trade can mark job as complete
        '404':
          description: Job not found
        '409':
          description: Job must be in progress to mark as complete
        '413':
          description: Files too large
        '500':
          description: Server error
  /trade-jobs/matches/{jobId}/cancel:
    post:
      security:
        - bearerAuth: []
      summary: Cancel a job with a reason (only available to assigned trade)
      tags:
        - Trade Jobs
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
            format: uuid
          description: Job ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - reason
              properties:
                reason:
                  type: string
                  description: Reason for canceling the job
      responses:
        '200':
          description: Job canceled successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Job'
        '400':
          description: Job ID and cancellation reason are required
        '401':
          description: Unauthorized - Authentication required
        '403':
          description: Only assigned trade can cancel this job
        '404':
          description: Job not found
        '409':
          description: Job can only be canceled when scheduled or in progress
        '500':
          description: Server error
  /trade-jobs/matches/{jobId}:
    get:
      security:
        - bearerAuth: []
      summary: Get detailed information for a specific job
      tags:
        - Trade Jobs
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
            format: uuid
          description: Job ID
      responses:
        '200':
          description:
            Detailed job information including location, dates, documents, and
            issues
          content:
            application/json:
              schema:
                type: object
                properties:
                  job:
                    $ref: '#/components/schemas/Job'
                  location:
                    type: object
                    properties:
                      postcode:
                        type: string
                      address:
                        type: string
                  dates:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                        date:
                          type: string
                          format: date-time
                        timeSlot:
                          type: string
                  match:
                    type: object
                    properties:
                      status:
                        type: string
                        enum:
                          - PENDING
                          - ACCEPTED
                          - REJECTED
                      selectedDateId:
                        type: string
                        format: uuid
                      rejectionReason:
                        type: string
                  issues:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                        type:
                          type: string
                        description:
                          type: string
                        reportedAt:
                          type: string
                          format: date-time
                        reportedBy:
                          type: string
                        status:
                          type: string
                  documents:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                        type:
                          type: string
                          enum:
                            - JOB_PHOTO
                            - JOB_COMPLETION_PHOTO
                            - JOB_ISSUE_REPORT_PHOTO
                            - OTHER
                        url:
                          type: string
                        description:
                          type: string
                        uploadedAt:
                          type: string
                          format: date-time
        '400':
          description: Job ID is required
        '401':
          description: Unauthorized - Authentication required
        '404':
          description: Job not found or no match found for this trade
        '500':
          description: Server error
tags:
  - name: Address
    description: Address lookup and validation endpoints
  - name: Auth
    description: Authentication endpoints
  - name: Categories
    description: Service category management endpoints
  - name: Companies House
    description: Companies House API integration endpoints
  - name: Companies
    description: Company management endpoints
  - name: Documents
    description: Job documents and photos management
  - name: Health
    description: API health check endpoints
  - name: Job Invoices
    description: Job invoice management endpoints
  - name: Payments
    description: Payment processing endpoints
  - name: Subscription Plans
    description: Subscription plan management endpoints
  - name: Subscriptions
    description: Subscription management endpoints
  - name: User Management
    description: User management endpoints for company administrators
  - name: Users
    description: User management endpoints
