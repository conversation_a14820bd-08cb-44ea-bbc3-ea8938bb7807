openapi: '3.0.0'
info:
  title: Trades Api
  description: Provides an API for Trade App
  version: v3
security:
  - bearerAuth: []
paths:
  '/api/availability/{companyId}/{tradeId}':
    get:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
        - in: path
          name: tradeId
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
  '/api/profile-description/{companyId}/{tradeId}':
    get:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
        - in: path
          name: tradeId
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
  '/api/zuora/request/{companyId}':
    get:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  invoices:
                    type: array
                    items:
                      type: object
        '404':
          description: Not Found
  '/api/zuora/request/{companyId}/{invoiceId}':
    get:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
        - in: path
          name: invoiceId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                type: string
                format: binary
        '404':
          description: Not Found
  '/api/zuora/request/{companyId}/{invoiceId}/items':
    get:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
        - in: path
          name: invoiceId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/json:
              schema:
                $ref: '#/components/schemas/InvoiceItemsType'
        '404':
          description: Not Found
  '/api/quotes-and-invoices/generate-pdf/{companyId}/{quoteId}/{type}':
    post:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
        - in: path
          name: quoteId
          required: true
          schema:
            type: string
        - in: path
          name: type
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
  '/api/quotes-and-invoices/share-quote/{companyId}/{quoteId}':
    post:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
        - in: path
          name: quoteId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
  '/api/zuora/account/{companyId}':
    get:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
  '/api/zuora/account/signature/{companyId}':
    get:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
  '/api/zuora/payment/{companyId}/{paymentMethodId}/{amount}':
    post:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
        - in: path
          name: paymentMethodId
          required: true
          schema:
            type: string
        - in: path
          name: amount
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
        '401':
          description: Unauthorized
  '/api/company-categories/{companyId}':
    get:
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
  '/api/campaign/{companyId}/{campaignId}/send-complete-campaign-setup-email':
    post:
      summary: 'Sends an email via the comms service asking the company to finish setting up their campaign. Email template ID is 9462'
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
        - in: path
          name: campaignId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendCompleteCampaignSetupEmailBodyType'
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
        '401':
          description: Unauthorized
  '/api/campaign/{companyId}/{campaignId}/send-accreditations-needed-email':
    post:
      summary: 'Sends an email via the comms service asking the company to upload their needed accreditations for categories selected in their campaign. Email template ID is 9652'
      security:
        - Bearer: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
            format: int32
        - in: path
          name: campaignId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendCampaignAccreditationsNeededEmailBodyType'
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
        '401':
          description: Unauthorized

components:
  schemas:
    SendCompleteCampaignSetupEmailBodyType:
      type: object
      required:
        - companyName
        - categoryName
      properties:
        companyName:
          type: string
        categoryName:
          type: string
    SendCampaignAccreditationsNeededEmailBodyType:
      type: object
      required:
        - companyName
        - categoryName
        - accreditations
      properties:
        companyName:
          type: string
        categoryName:
          type: string
        accreditations:
          type: array
          items:
            type: string
    InvoiceItemsType:
      type: array
      items:
        type: object
        properties:
          subscriptionId:
            type: string
          servicesStartDate:
            type: string
            format: date
          servicesEndDate:
            type: string
            format: date
          campaignId:
            type: string
        required:
          - servicesStartDate
          - servicesEndDate
