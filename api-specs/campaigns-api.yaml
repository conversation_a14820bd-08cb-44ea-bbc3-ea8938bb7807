openapi: 3.0.1
info:
  title: Cathex.Campaigns.Api
  contact:
    name: Enterprise Team
    url: https://github.com/orgs/cat-home-experts/teams/enterprise
  version: *******
paths:
  '/v1/trades/{companyId}/adjustments/{adjustmentId}':
    get:
      tags:
        - Adjustments
      summary: Get an Adjustment for the given companyId and adjustmentId. Returns the adjustmentDTO.
      operationId: GetAdjustment
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: adjustmentId
          in: path
          description: An Adjustment's ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetAdjustmentResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/GetAdjustmentResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/GetAdjustmentResponse'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  '/v1/trades/{companyId}/adjustments':
    get:
      tags:
        - Adjustments
      summary: Get the paged list of Adjustments for the given companyId. Returns a paged list of adjustments.
      operationId: GetAdjustments
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: top
          in: query
          description: Limit the number of results in this page. Defaults to returning "all" and there is no max value.
          schema:
            type: integer
            format: int32
        - name: skip
          in: query
          description: Skip this number of adjustments before returning results.
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/AdjustmentDtoPageResult'
            application/json:
              schema:
                $ref: '#/components/schemas/AdjustmentDtoPageResult'
            text/json:
              schema:
                $ref: '#/components/schemas/AdjustmentDtoPageResult'
  /v1/bulk/campaigns/recalculateBudgets:
    get:
      tags:
        - Bulk
      summary: Recalculate budgets in bulk
      operationId: RecalculateCampaignBudgets
      parameters:
        - name: correlationId
          in: query
          description: ''
          schema:
            type: string
        - name: limit
          in: query
          description: ''
          schema:
            type: integer
            format: int32
        - name: chunkSize
          in: query
          description: ''
          schema:
            type: integer
            format: int32
        - name: chunkInterval
          in: query
          description: ''
          schema:
            type: integer
            format: int32
        - name: companyId
          in: query
          description: for a specific company
          schema:
            type: string
        - name: budgetCycleDays
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CampaignBulkResponseMessage'
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignBulkResponseMessage'
            text/json:
              schema:
                $ref: '#/components/schemas/CampaignBulkResponseMessage'
  /v1/bulk/campaigns/refresh:
    get:
      tags:
        - Bulk
      summary: Trigger a "refresh" of all active campaigns by issuing a `CampaignUpdated` or `CampaignBalanceUpdated` message
      operationId: RefreshCampaigns
      parameters:
        - name: correlationId
          in: query
          description: A correlation ID to use for tracking this request
          schema:
            type: string
        - name: limit
          in: query
          description: 'When specified, limit the total number of Trades to update (not the number of campaigns)'
          schema:
            type: integer
            format: int32
        - name: chunkSize
          in: query
          description: ''
          schema:
            type: integer
            format: int32
        - name: chunkInterval
          in: query
          description: ''
          schema:
            type: integer
            format: int32
        - name: companyId
          in: query
          description: Request a refresh for a specific Trade
          schema:
            type: string
        - name: refreshType
          in: query
          description: The type of refresh to perform. Can be 'Balance' or 'Definition' (default).
          schema:
            enum:
              - Definition
              - Balance
            type: string
            default: Definition
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CampaignRefreshBulkResponseMessage'
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignRefreshBulkResponseMessage'
            text/json:
              schema:
                $ref: '#/components/schemas/CampaignRefreshBulkResponseMessage'
  /v1/bulk/campaigns/recalculate:
    get:
      tags:
        - Bulk
      summary: "Recalculates the Lead Count, Click Count, and Balance of all active or inactive (if updated in the last 2 months)\r\ncampaigns for all (or companyId) trades."
      operationId: RecalculateCampaigns
      parameters:
        - name: correlationId
          in: query
          description: A correlation ID to use for tracking this request
          schema:
            type: string
        - name: limit
          in: query
          description: 'When specified, limit the total number of Trades to update (not the number of campaigns)'
          schema:
            type: integer
            format: int32
        - name: chunkSize
          in: query
          description: Ignored
          schema:
            type: integer
            format: int32
        - name: chunkInterval
          in: query
          description: Ignored
          schema:
            type: integer
            format: int32
        - name: companyId
          in: query
          description: Request a recalculation for a specific Trade
          schema:
            type: string
        - name: budgetPeriod
          in: query
          description: Budget period to recalculate. Defaults to current budget period.
          schema:
            type: string
            format: date
        - name: budgetPeriodMonthDelta
          in: query
          description: The number of months +/- relative to budgetPeriod to recalculate.
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CampaignRefreshBulkResponseMessage'
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignRefreshBulkResponseMessage'
            text/json:
              schema:
                $ref: '#/components/schemas/CampaignRefreshBulkResponseMessage'
  '/v1/trades/{companyId}/campaigns':
    get:
      tags:
        - Campaign
      operationId: GetCampaigns
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: offset
          in: query
          description: 'Number of records to skip, defaults to 0'
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: 'Number of records to return, defaults to 50'
          schema:
            type: integer
            format: int32
        - name: campaignType
          in: query
          schema:
            enum:
              - Ppl
              - Alsp
              - Fixed
              - Sponsored
              - MdpSponsoredSearch
              - MdpSponsoredDisplay
            type: string
        - name: includeInactive
          in: query
          schema:
            type: boolean
            default: false
        - name: categoryId
          in: query
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: 'Sort expression. Allowed sort fields are: dateUpdated, isActive, pausedUntil'
          schema:
            pattern: dateUpdated:(asc|desc)|isActive:(asc|desc)|pausedUntil:(asc|desc)
            type: array
            items:
              type: string
          example: dateUpdated:asc
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CampaignDtoPageResult'
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignDtoPageResult'
            text/json:
              schema:
                $ref: '#/components/schemas/CampaignDtoPageResult'
    post:
      tags:
        - Campaign
      summary: Create a new Campaign for the given companyId. Returns the new campaign's unique identifier.
      operationId: CreateCampaign
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCampaignRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/CreateCampaignRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/CreateCampaignRequest'
      responses:
        '201':
          description: Created
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CreateCampaignResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCampaignResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/CreateCampaignResponse'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  '/v1/trades/{companyId}/campaigns/{campaignId}':
    get:
      tags:
        - Campaign
      operationId: GetCampaign
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CampaignDto'
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignDto'
            text/json:
              schema:
                $ref: '#/components/schemas/CampaignDto'
  '/v1/trades/{companyId}/campaigns/{campaignId}/statistics':
    get:
      tags:
        - Campaign
      summary: Retrieve summary statistics for this campaign including lead counts by channel
      operationId: GetCampaignStatistics
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          required: true
          schema:
            type: string
        - name: summaryLevel
          in: query
          schema:
            enum:
              - Last7Days
              - Last30Days
              - Last90Days
              - BudgetPeriod
              - LastBudgetPeriod
              - Lifetime
            type: string
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CampaignStatisticSummaryDto'
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignStatisticSummaryDto'
            text/json:
              schema:
                $ref: '#/components/schemas/CampaignStatisticSummaryDto'
  '/v1/trades/{companyId}/campaigns/{campaignId}/priceRanges':
    post:
      tags:
        - Campaign
      operationId: GetPriceRanges
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PriceRangesRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/PriceRangesRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/PriceRangesRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/PriceRangesDtoPageResult'
            application/json:
              schema:
                $ref: '#/components/schemas/PriceRangesDtoPageResult'
            text/json:
              schema:
                $ref: '#/components/schemas/PriceRangesDtoPageResult'
  '/v1/trades/{companyId}/campaigns/{campaignId}/confirm':
    post:
      tags:
        - Campaign
      summary: Record that a Trade has confirmed changes to a Campaign
      operationId: ConfirmCampaign
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfirmCampaignRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/ConfirmCampaignRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/ConfirmCampaignRequest'
      responses:
        '200':
          description: Success
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateSubCategories':
    post:
      tags:
        - Campaign
      summary: Update the sub-categories of an existing campaign. Note that you cannot change the campaign's category.
      operationId: UpdateSubCategories
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSubCategoriesRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateSubCategoriesRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateSubCategoriesRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '200':
          description: Success
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateMaxSpend':
    post:
      tags:
        - Campaign
      summary: "Update the Campaign's budget relative to the maximum amount the Trade is willing to spend in a given budget period. This\r\nendpoint does not require knowledge of Minimum Commitment or Budget Boost Amounts (beyond validation requirements)."
      description: 'When Role Based access filters are applied, this should be accessible to Trades (and UpdateBudget should not)'
      operationId: UpdateMaxSpend
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMaxSpendRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateMaxSpendRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateMaxSpendRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '204':
          description: No Content
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateRateBids':
    post:
      tags:
        - Campaign
      summary: Update the Campaign's manual bidding strategy's individual bids
      operationId: UpdateRateBids
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRateBidsRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateRateBidsRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateRateBidsRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '200':
          description: Success
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateGeographies':
    post:
      tags:
        - Campaign
      summary: Update the Campaign's assigned geographies
      operationId: UpdateGeographies
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateGeographiesRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateGeographiesRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateGeographiesRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '200':
          description: Success
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateAvailability':
    post:
      tags:
        - Campaign
      summary: "Update the Campaign's availability by pausing until a set date or un-pausing.\r\n            \r\nTo pause a campaign indefinitely, use UpdateStatus to make it inactive."
      operationId: UpdateAvailability
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAvailabilityRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateAvailabilityRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateAvailabilityRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '200':
          description: Success
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateStatus':
    post:
      tags:
        - Campaign
      summary: "Update the Campaign's active, directory opt-out, and RaQ opt-out statuses"
      operationId: UpdateStatus
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateStatusRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateStatusRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateStatusRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '200':
          description: Success
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateParentCategory':
    post:
      tags:
        - Campaign
      summary: Update the Campaign's parent category's attributes
      operationId: UpdateParentCategory
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateParentCategoryRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateParentCategoryRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateParentCategoryRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '200':
          description: Success
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateBiddingStrategy':
    post:
      tags:
        - Campaign
      summary: Update the Campaign's Bidding Strategy Type from Manual to Fully Automatic
      operationId: UpdateBiddingStrategy
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBiddingStrategyRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateBiddingStrategyRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateBiddingStrategyRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '200':
          description: Success
  '/v1/categories/{parentCategoryId}':
    get:
      tags:
        - Categories
      summary: Get a Parent Category by Id. Includes display names and all Sub-Categories.
      operationId: GetCategoryById
      parameters:
        - name: parentCategoryId
          in: path
          description: A parent category ID
          required: true
          schema:
            type: integer
            format: int32
      responses:
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ParentCategoryDto'
            application/json:
              schema:
                $ref: '#/components/schemas/ParentCategoryDto'
            text/json:
              schema:
                $ref: '#/components/schemas/ParentCategoryDto'
  /v1/auth/tradeApp:
    post:
      tags:
        - FirebaseAuthToken
      summary: Creates a token for trade app to read Campaigns firestore
      operationId: TradeAppFirebaseToken
      responses:
        '401':
          description: Unauthorized
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '201':
          description: Created
          content:
            text/plain:
              schema:
                type: string
            application/json:
              schema:
                type: string
            text/json:
              schema:
                type: string
  '/v1/trades/{companyId}/leadDisputes/{leadDisputeId}':
    get:
      tags:
        - LeadDisputes
      operationId: GetLeadDispute
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: leadDisputeId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/LeadDisputeDto'
            application/json:
              schema:
                $ref: '#/components/schemas/LeadDisputeDto'
            text/json:
              schema:
                $ref: '#/components/schemas/LeadDisputeDto'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
    post:
      tags:
        - LeadDisputes
      operationId: UpdateLeadDispute
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: leadDisputeId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLeadDisputeRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateLeadDisputeRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateLeadDisputeRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '200':
          description: Success
  '/v1/trades/{companyId}/leadDisputes':
    get:
      tags:
        - LeadDisputes
      operationId: GetLeadDisputes
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: leadDisputeStatusAsEnum
          in: query
          schema:
            type: string
        - name: adjustmentIdMaybe
          in: query
          schema:
            type: string
        - name: somePagination
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/LeadDisputeDtoPageResult'
            application/json:
              schema:
                $ref: '#/components/schemas/LeadDisputeDtoPageResult'
            text/json:
              schema:
                $ref: '#/components/schemas/LeadDisputeDtoPageResult'
  '/v1/trades/{companyId}/leadDisputes/batch':
    post:
      tags:
        - LeadDisputes
      operationId: CreateLeadDisputes
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: leadId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateLeadDisputeBatchRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/CreateLeadDisputeBatchRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/CreateLeadDisputeBatchRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '201':
          description: Created
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CreateLeadDisputeBatchResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/CreateLeadDisputeBatchResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/CreateLeadDisputeBatchResponse'
  '/v1/trades/{companyId}/leads/{leadId}':
    get:
      tags:
        - Leads
      operationId: GetLeadById
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: leadId
          in: path
          required: true
          schema:
            type: string
        - name: leadType
          in: query
          schema:
            enum:
              - Lead
              - Click
            type: string
            default: Lead
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/LeadDto'
            application/json:
              schema:
                $ref: '#/components/schemas/LeadDto'
            text/json:
              schema:
                $ref: '#/components/schemas/LeadDto'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  '/v1/trades/{companyId}/leads':
    get:
      tags:
        - Leads
      operationId: GetLeads
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: offset
          in: query
          description: 'Number of records to skip, defaults to 0'
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: 'Number of records to return, defaults to 50'
          schema:
            type: integer
            format: int32
        - name: budgetPeriod
          in: query
          schema:
            type: string
            format: date
        - name: dateGeneratedFrom
          in: query
          schema:
            type: string
            format: date-time
        - name: dateGeneratedTo
          in: query
          schema:
            type: string
            format: date-time
        - name: campaignId
          in: query
          schema:
            type: string
        - name: sort
          in: query
          description: 'Sort expression. Allowed sort fields are: dateGenerated'
          schema:
            pattern: dateGenerated:(asc|desc)
            type: array
            items:
              type: string
          example: dateGenerated:asc
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/LeadDtoPageResult'
            application/json:
              schema:
                $ref: '#/components/schemas/LeadDtoPageResult'
            text/json:
              schema:
                $ref: '#/components/schemas/LeadDtoPageResult'
  '/v1/trades/{companyId}/leads/export':
    post:
      tags:
        - Leads
      operationId: ExportLeads
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: budgetPeriod
          in: query
          schema:
            type: string
            format: date
        - name: campaignId
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
            text/csv:
              schema:
                type: string
                format: binary
            text/tab-separated-values:
              schema:
                type: string
                format: binary
        '415':
          description: Client Error
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/csv:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/tab-separated-values:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  /v1/quoteRequests:
    post:
      tags:
        - QuoteRequests
      summary: 'Creates a new Quote Request. If a Previous Quote Request Id is specified, it will be set as "Superseded."'
      operationId: CreateQuoteRequest
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQuoteRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/CreateQuoteRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/CreateQuoteRequest'
      responses:
        '201':
          description: Created
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
            text/json:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  '/v1/quoteRequests/{quoteRequestId}/refresh':
    post:
      tags:
        - QuoteRequests
      summary: "After a Quote Request has expired, refresh it using all the same inputs as the original. This will create a new\r\nQuote Request with a \"Previous Quote Request ID\" that links to the expired one.\r\n            \r\nIf the provided Quote Request isn't already expired, this will set it to \"Superseded\" (thus preventing it from being used).\r\n            \r\nAny previously \"selected\" or re-forecasted budget will not be persisted."
      operationId: RefreshRequestForQuote
      parameters:
        - name: quoteRequestId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshQuoteRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/RefreshQuoteRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/RefreshQuoteRequest'
      responses:
        '201':
          description: Created
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
            text/json:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  '/v1/quoteRequests/{quoteRequestId}/selectQuote':
    post:
      tags:
        - QuoteRequests
      summary: "Register which Quote on a Quote Request a Trade selected if the selection happens before converting. This can be\r\nchanged multiple times."
      operationId: SelectQuote
      parameters:
        - name: quoteRequestId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SelectQuoteRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/SelectQuoteRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/SelectQuoteRequest'
      responses:
        '204':
          description: No Content
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  '/v1/quoteRequests/{quoteRequestId}':
    get:
      tags:
        - QuoteRequests
      summary: "Gets an existing Quote Request by ID. This can be used to get the details of the Quote Request, check whether\r\nit is expired, or see if a quote has been \"selected.\""
      operationId: GetQuoteRequest
      parameters:
        - name: quoteRequestId
          in: path
          description: ''
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
            text/json:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  '/v1/quoteRequests/{quoteRequestId}/quotes/{quoteId}/forecasts':
    get:
      tags:
        - QuoteRequests
      summary: "Gets all the lead forecast(s) generated for a specific Quote of a Quote Request. Non-PPL Quotes will\r\nonly have one forecast."
      operationId: GetQuoteLeadForecasts
      parameters:
        - name: quoteRequestId
          in: path
          required: true
          schema:
            type: string
        - name: quoteId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/QuoteLeadForecastsDto'
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteLeadForecastsDto'
            text/json:
              schema:
                $ref: '#/components/schemas/QuoteLeadForecastsDto'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  '/v1/quoteRequests/{quoteRequestId}/quotes/{quoteId}/convert':
    post:
      tags:
        - QuoteRequests
      summary: "Converts a Quote Request's Quote to an actual campaign. The Quote Request's status becomes \"Converted\" and can no longer\r\nbe refreshed, forecasted, selected, etc."
      operationId: ConvertQuote
      parameters:
        - name: quoteRequestId
          in: path
          required: true
          schema:
            type: string
        - name: quoteId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConvertQuoteRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/ConvertQuoteRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/ConvertQuoteRequest'
      responses:
        '201':
          description: Created
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CampaignDto'
            application/json:
              schema:
                $ref: '#/components/schemas/CampaignDto'
            text/json:
              schema:
                $ref: '#/components/schemas/CampaignDto'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '409':
          description: Conflict
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
  '/v1/quoteRequests/{quoteRequestId}/quotes/{quoteId}/reforecast':
    post:
      tags:
        - QuoteRequests
      summary: "For a PPL Quote, get a new lead forecast for the given budget selection. When selecting or convert a PPL quote, the\r\nselected budget must have been forecast, rounding up to 10 (and thus presented to the user)."
      operationId: ReforecastQuote
      parameters:
        - name: quoteRequestId
          in: path
          required: true
          schema:
            type: string
        - name: quoteId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReforecastQuoteRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/ReforecastQuoteRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/ReforecastQuoteRequest'
      responses:
        '201':
          description: Created
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/QuoteLeadReforecastDto'
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteLeadReforecastDto'
            text/json:
              schema:
                $ref: '#/components/schemas/QuoteLeadReforecastDto'
        '404':
          description: Not Found
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  /v1/regions:
    get:
      tags:
        - Regions
      summary: 'Get a paged list of Regions, which contain Postcode Areas and Postcode Districts'
      operationId: GetRegions
      parameters:
        - name: top
          in: query
          description: Limit the number of results in this page. Defaults to returning "all" and there is no max value.
          schema:
            type: integer
            format: int32
        - name: skip
          in: query
          description: Skip this number of regions before returning results.
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/RegionDtoPageResult'
            application/json:
              schema:
                $ref: '#/components/schemas/RegionDtoPageResult'
            text/json:
              schema:
                $ref: '#/components/schemas/RegionDtoPageResult'
  '/v1/regions/recommendations':
    post:
      tags:
        - Regions
      summary: "Get insights of recommended locations for specific geographies (supports both Postcode Districts and Postcode Areas). Currently\r\nreturns up to 10 Postcode Districts, ordered by highest Demand Score."
      description: Currently returns all districts of provided area and all but provided district from the same area.
      operationId: CreateRegionalRecommendations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRegionalRecommendationsRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/CreateRegionalRecommendationsRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/CreateRegionalRecommendationsRequest'
      responses:
        '201':
          description: Created
          schema:
            $ref: '#/components/schemas/GeographyInsightDtoPageResult'
  '/v1/trades/{companyId}/campaigns/{campaignId}/upgradeOptions':
    post:
      tags:
        - Campaign
      summary: Create an upgrade Quote Request based on the given Fixed campaign
      operationId: CreateUpgradeOptions
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Fixed Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUpgradeOptionsRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/CreateUpgradeOptionsRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/CreateUpgradeOptionsRequest'
      responses:
        '201':
          description: Created
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
            text/json:
              schema:
                $ref: '#/components/schemas/QuoteRequestDto'
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
  '/v1/trades/{companyId}/campaigns/{campaignId}/upgrade':
    post:
      tags:
        - Campaign
      summary: Upgrade the Fixed campaign based on the given upgrade Quote Request
      operationId: UpgradeCampaign
      parameters:
        - name: companyId
          in: path
          description: A Trade's Company ID
          required: true
          schema:
            type: string
        - name: campaignId
          in: path
          description: An existing Campaign
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpgradeCampaignRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpgradeCampaignRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpgradeCampaignRequest'
      responses:
        '400':
          description: Bad Request
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidationProblemDetails'
        '201':
          description: Created
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/UpgradeCampaignResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/UpgradeCampaignResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/UpgradeCampaignResponse'
components:
  schemas:
    AdjustmentDto:
      type: object
      properties:
        adjustmentId:
          type: string
          nullable: true
        companyId:
          type: string
          nullable: true
        amount:
          type: number
          format: double
        adjustmentType:
          $ref: '#/components/schemas/AdjustmentTypeDto'
        comment:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        dateCreated:
          type: string
          format: date-time
        dateUpdated:
          type: string
          format: date-time
      additionalProperties: false
    AdjustmentDtoPageResult:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/AdjustmentDto'
          description: Gets the collection of entities for this page.
          nullable: true
        skip:
          type: integer
          format: int32
          nullable: true
        top:
          type: integer
          format: int32
          nullable: true
        count:
          type: integer
          description: Gets the total count of items in the result set
          format: int32
      additionalProperties: false
    AdjustmentTypeDto:
      enum:
        - LeadDisputes
      type: string
    BiddingStrategyTypeDto:
      enum:
        - FullyAutomatic
        - SemiAutomatic
        - Manual
      type: string
    BudgetAndBalanceDto:
      type: object
      properties:
        period:
          type: string
          format: date
        periodStartDate:
          type: string
          format: date
        periodEndDate:
          type: string
          format: date
        maxBudget:
          type: number
          format: double
        maxSpend:
          type: number
          format: double
        balance:
          type: number
          format: double
        threshold:
          type: number
          format: double
        leadCount:
          type: integer
          format: int32
        clickCount:
          type: integer
          format: int32
        cumulativeLeadCount:
          type: integer
          format: int32
        daysRemainingInPeriod:
          type: integer
          format: int32
        leadCommitmentThreshold:
          type: number
          format: double
        budgetBoostAmount:
          type: number
          format: double
        isMinimumCommitmentActive:
          type: boolean
        minimumCommitmentAmount:
          type: number
          format: double
        minimumCommitmentUntil:
          type: string
          format: date
      additionalProperties: false
    CampaignAmendmentDto:
      required:
        - category
        - geographies
        - subCategories
      type: object
      properties:
        geographies:
          type: array
          items:
            $ref: '#/components/schemas/GeographyDto'
          description: The geographies this campaign should be updated to use
        category:
          $ref: '#/components/schemas/CategoryDto'
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
          description: One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Dtos.CampaignAmendmentDto.Category) this campaign should be updated to use
    CampaignAttributeUpdateTypeDto:
      enum:
        - Add
        - Replace
        - Remove
      type: string
    CampaignBulkResponseMessage:
      type: object
      properties:
        correlationId:
          type: string
          nullable: true
        originator:
          type: string
          nullable: true
        statusCode:
          $ref: '#/components/schemas/PubSubResponseStatusCode'
        count:
          type: integer
          format: int32
      description: Boilerplate bulk handling message for Cathex.Campaigns.Contracts.PubSub.CampaignRefreshBulkRequestMessage.
    CampaignCategoryDto:
      type: object
      properties:
        categoryId:
          type: integer
          description: The unique identifier for the category
          format: int32
        isSearchable:
          type: boolean
          description: Should the trade show in search results for this category?
        name:
          type: string
          description: The name of this parent category
          nullable: true
      description: Information for a Campaign's Parent Category
    CampaignCountsDto:
      type: object
      properties:
        totalLeads:
          type: integer
          description: Total count of Leads (which is the sum of all channels except for click)
          format: int32
        click:
          type: integer
          description: Total count of Clicks
          format: int32
        call:
          type: integer
          description: Total call leads
          format: int32
        directory:
          type: integer
          description: Total directory call leads
          format: int32
        message:
          type: integer
          description: Total direct message leads
          format: int32
        requestAQuote:
          type: integer
          description: Total Request a Quote leads
          format: int32
    CampaignDto:
      type: object
      properties:
        campaignId:
          type: string
          nullable: true
        companyId:
          type: string
          nullable: true
        campaignType:
          enum:
            - Ppl
            - Alsp
            - Fixed
            - Sponsored
            - MdpSponsoredSearch
            - MdpSponsoredDisplay
          type: string
          description: Describes the type of campaign. Different campaign types have different traits.
        isActive:
          type: boolean
        isPaused:
          type: boolean
        pausedUntil:
          type: string
          format: date
          nullable: true
        startDate:
          type: string
          format: date
        category:
          $ref: '#/components/schemas/CategoryDto'
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
          nullable: true
        geographies:
          type: array
          items:
            $ref: '#/components/schemas/GeographyDto'
          nullable: true
        budgetsAndBalances:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/BudgetAndBalanceDto'
          nullable: true
        currentBudgetPeriod:
          type: string
          format: date
        budgetCycleDay:
          type: integer
          format: int32
        currentBudgetAndBalance:
          $ref: '#/components/schemas/BudgetAndBalanceDto'
        futureMonthMaxBudget:
          type: number
          format: double
        fixed:
          $ref: '#/components/schemas/FixedCampaignDetailsDto'
        sponsorship:
          $ref: '#/components/schemas/SponsorshipCampaignDetailsDto'
        ppl:
          $ref: '#/components/schemas/PplCampaignDetailsDto'
        dateCreated:
          type: string
          format: date-time
        dateUpdated:
          type: string
          format: date-time
        dateBalanceLastUpdated:
          type: string
          format: date-time
        createdBy:
          type: string
          nullable: true
        updatedBy:
          type: string
          nullable: true
      additionalProperties: false
    CampaignDtoPageResult:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/CampaignDto'
          description: Gets the collection of entities for this page.
          nullable: true
        skip:
          type: integer
          format: int32
          nullable: true
        top:
          type: integer
          format: int32
          nullable: true
        count:
          type: integer
          description: Gets the total count of items in the result set
          format: int32
      additionalProperties: false
    CampaignRefreshBulkResponseMessage:
      type: object
      properties:
        correlationId:
          type: string
          nullable: true
        originator:
          type: string
          nullable: true
        statusCode:
          $ref: '#/components/schemas/PubSubResponseStatusCode'
        count:
          type: integer
          format: int32
      additionalProperties: false
      description: Boilerplate bulk handling message for Cathex.Campaigns.Contracts.PubSub.CampaignRefreshBulkRequestMessage.
    CampaignStatisticSummaryDto:
      type: object
      properties:
        summaryLevel:
          enum:
            - Last7Days
            - Last30Days
            - Last90Days
            - BudgetPeriod
            - LastBudgetPeriod
            - Lifetime
          type: string
          description: Aggregation level of this summary
        days:
          type: integer
          description: Total days in this summary period
          format: int32
        startDate:
          type: string
          description: Earliest day in this summary period
          format: date
        endDate:
          type: string
          description: Last day (inclusive) in this summary period
          format: date
        budgetPeriod:
          type: string
          description: 'If the summary is by budget period, this is the budget period summarised'
          format: date
          nullable: true
        balance:
          type: number
          description: Total balance for this summary period
          format: double
        counts:
          $ref: '#/components/schemas/CampaignCountsDto'
    CategoryDto:
      required:
        - categoryId
      type: object
      properties:
        categoryId:
          type: integer
          description: The unique identifier for the category or sub-category
          format: int32
        parentCategoryId:
          type: integer
          description: The sub-category's parent category (`0` is not a valid category in this API)
          format: int32
          nullable: true
        isSearchable:
          type: boolean
          description: "Should the trade show in search results for this category.\r\nCan only be set for the parent category, will be `true` for any child categories and if null."
          nullable: true
      additionalProperties: false
      description: "A Cathex.Campaigns.Contracts.Dtos.CategoryDto represents either a Trade Category or a Trade Sub-Category. Sub-Categories\r\nshould be indicated by setting the Cathex.Campaigns.Contracts.Dtos.CategoryDto.ParentCategoryId value."
    CompanySizeDto:
      enum:
        - One
        - TwoToFive
        - FiveToTen
        - TenOrMore
      type: string
      description: 'General size of the requesting company, in number of employees'
    ConfirmCampaignRequest:
      type: object
      properties:
        isConfirmed:
          type: boolean
          description: 'Flag for confirming the campaign, defaults to true'
          nullable: true
      additionalProperties: false
      description: Represents a Campaign Confirmation Request
    ConvertQuoteRequest:
      required:
        - budgetCycleDay
        - companyId
      type: object
      properties:
        companyId:
          minLength: 1
          type: string
        selectedBudget:
          type: number
          description: "If the Quote is for a PPL Campaign, this must be a budget that has been forecasted.\r\n            \r\nThis is represented as a decimal, like all currency, but should actually be a whole number.\r\n            \r\nBudgets will be rounded up to the nearest increment of 10."
          format: double
          nullable: true
        selectedDiscountOffer:
          $ref: '#/components/schemas/DiscountOfferDto'
        budgetCycleDay:
          type: integer
          description: This trade's budget cycle day (the day of the month their invoice/budget period starts)
          format: int32
        deactivateExistingCampaign:
          type: boolean
          description: "If an active campaign already exists in this quote's Category, de-activate it. Otherwise, throw a validation error."
          nullable: true
    CreateCampaignRequest:
      required:
        - biddingStrategyType
        - budgetPeriod
        - category
        - geographies
        - maxBudget
        - subCategories
        - type
      type: object
      properties:
        type:
          enum:
            - Ppl
            - Alsp
            - Fixed
            - Sponsored
            - MdpSponsoredSearch
            - MdpSponsoredDisplay
          type: string
          description: The type of a campaign (Cathex.Campaigns.Contracts.Dtos.Enums.CampaignTypeDto.Ppl is the only supported now)
        biddingStrategyType:
          $ref: '#/components/schemas/BiddingStrategyTypeDto'
        category:
          $ref: '#/components/schemas/CategoryDto'
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
          description: One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.Category)
        maxBudget:
          type: number
          description: The maximum budget for the budget month of Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.BudgetPeriod
          format: double
        budgetPeriod:
          type: string
          description: "The first of the month that this campaign should start.\r\nMust be a date on the first of this month or first of next month."
          format: date
        geographies:
          type: array
          items:
            $ref: '#/components/schemas/GeographyDto'
          description: Geographies for this campaign.
        rateBids:
          type: array
          items:
            $ref: '#/components/schemas/RateBidDto'
          description: "Rates for each Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.SubCategories and for the Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.Category level\r\nwhen the Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.BiddingStrategyType is Cathex.Campaigns.Contracts.Dtos.Enums.BiddingStrategyTypeDto.Manual."
          nullable: true
        isActive:
          type: boolean
          description: 'A campaign can be created in an inactive state if, for example, the budget is not yet set. Defaults to Active.'
          nullable: true
      additionalProperties: false
      description: Represents all of the parameters needed to create a Campaign
    CreateCampaignResponse:
      required:
        - campaignId
        - companyId
      type: object
      properties:
        campaignId:
          minLength: 1
          type: string
        companyId:
          minLength: 1
          type: string
      additionalProperties: false
    CreateLeadDisputeBatchItem:
      required:
        - leadId
        - leadDisputeType
      type: object
      properties:
        leadId:
          type: string
        leadDisputeType:
          $ref: '#/components/schemas/LeadDisputeTypeDto'
        disputeReason:
          type: string
          nullable: true
      additionalProperties: false
    CreateLeadDisputeBatchRequest:
      required:
        - items
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/CreateLeadDisputeBatchItem'
      additionalProperties: false
    CreateLeadDisputeBatchResponse:
      type: object
      properties:
        companyId:
          type: string
          nullable: true
        count:
          type: integer
          format: int32
      additionalProperties: false
    CreateRegionalRecommendationsRequest:
      required:
        - category
        - geographies
        - subCategories
      type: object
      properties:
        geographies:
          description: The geographies covered by this request
          type: array
          items:
            $ref: '#/components/schemas/GeographyDto'
        category:
          $ref: '#/components/schemas/CategoryDto'
        subCategories:
          description: One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Messages.Regions.CreateRegionalRecommendationsRequest.Category)
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
    CreateQuoteRequest:
      required:
        - category
        - geographies
        - source
        - subCategories
      type: object
      properties:
        companyId:
          type: string
          description: "The trade's Company ID, if already known"
          nullable: true
        source:
          $ref: '#/components/schemas/QuoteRequestSourceDto'
        geographies:
          type: array
          items:
            $ref: '#/components/schemas/GeographyDto'
          description: The geographies covered by this request
        category:
          $ref: '#/components/schemas/CategoryDto'
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
          description: One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Messages.QuoteRequests.CreateQuoteRequest.Category)
        companySize:
          $ref: '#/components/schemas/CompanySizeDto'
        utmParameters:
          type: object
          additionalProperties:
            type: string
          description: Any query/cookie tracking information used with this request
          nullable: true
        previousQuoteRequestId:
          type: string
          description: "If this is a follow-up quote request (e.g., a minor variation of a previous one from the same session), indicate\r\nit here. This is only used for tracking and grouping purposes."
          nullable: true
        experimentGroup:
          type: string
          description: Influence Product Catalog's business rules by setting an experiment group
          nullable: true
        quoteRequestType:
          $ref: '#/components/schemas/QuoteRequestTypeDto'
        sourceCampaignId:
          type: string
          description: 'If this is an upgrade or a modification, this is the ID of the campaign that is being upgraded'
          nullable: true
        sourceReferenceId:
          type: string
          description: An arbitrary reference ID from the Cathex.Campaigns.Contracts.Messages.QuoteRequests.CreateQuoteRequest.Source of this quote request
          nullable: true
    DiscountDto:
      type: object
      properties:
        fixed:
          type: number
          format: double
          nullable: true
        percentage:
          type: number
          format: double
          nullable: true
        numberOfMonths:
          type: integer
          format: int32
        lastDiscountedBudgetPeriod:
          type: string
          format: date
      additionalProperties: false
    DiscountOfferDto:
      type: object
      properties:
        fixedAmount:
          type: number
          description: 'If specified, a set amount that can be discounted each month. Either this or Cathex.Campaigns.Contracts.Dtos.QuoteRequests.DiscountOfferDto.Percentage will be set.'
          format: double
          nullable: true
        percentage:
          type: number
          description: 'If specified, a percentage that can be discounted each month. Either this or Cathex.Campaigns.Contracts.Dtos.QuoteRequests.DiscountOfferDto.FixedAmount will be set.'
          format: double
          nullable: true
        numberOfMonths:
          type: integer
          description: 'Length of the discount commitment (e.g., 3 months)'
          format: int32
      additionalProperties: false
      description: Discount an agent can provide. Initially this is just for fixed but theoretically could be for PPL eventually.
    FixedCampaignDetailsDto:
      type: object
      properties:
        price:
          type: number
          format: double
        monthsCommitted:
          type: integer
          format: int32
        commitmentStartDate:
          type: string
          format: date
        commitmentRenewalDate:
          type: string
          format: date
        paymentCadence:
          $ref: '#/components/schemas/PaymentCadenceDto'
        discount:
          $ref: '#/components/schemas/DiscountDto'
        leadCommitment:
          $ref: '#/components/schemas/LeadCommitmentDto'
        expectedLeadCount:
          $ref: '#/components/schemas/LeadCommitmentDto'
      additionalProperties: false
    FixedQuoteDetailsDto:
      type: object
      properties:
        pricePerMonth:
          type: number
          description: Monthly price of this fixed plan
          format: double
        commitmentPeriodMonths:
          type: integer
          description: 'Length of this commitment period (e.g., 12 months)'
          format: int32
      additionalProperties: false
    GeographyDto:
      required:
        - type
        - value
      type: object
      properties:
        value:
          minLength: 1
          type: string
          description: 'An alphanumeric value that maps to a real location (e.g., `SO4`)'
        type:
          $ref: '#/components/schemas/GeographyTypeDto'
        name:
          type: string
          description: The name of a location
          nullable: true
        centroid:
          $ref: '#/components/schemas/GeoPointDto'
      additionalProperties: false
      description: A geography identifies some known territory
    GeoPointDto:
      type: object
      properties:
        latitude:
          type: number
          description: 'The latitude of the location, represented as a double. Ranges from -90 to 90.'
          format: double
        longitude:
          type: number
          description: 'The longitude of the location, represented as a double. Ranges from -180 to 180.'
          format: double
      description: Represents a geographical point with latitude and longitude coordinates.
    GeographyDemandDto:
      enum:
        - Low
        - Middle
        - High
      type: string
    GeographyTypeDto:
      enum:
        - PostcodeArea
        - PostcodeDistrict
        - PostcodeSector
        - DirectoryArea
      type: string
    GeographyInsightDto:
      type: object
      properties:
        geography:
          $ref: '#/components/schemas/GeographyDto'
        averageHouseholdIncome:
          type: integer
          description: The average household income for the given geography
          format: int32
        households:
          type: integer
          description: The number of households for the given geography
          format: int32
        leadsAvailable:
          type: integer
          description: The number of leads available for the given geography
          format: int32
        leadsConsumed:
          type: integer
          description: The number of leads consumed for the given geography
          format: int32
        population:
          type: integer
          description: The population for the given geography
          format: int32
        tradeCount:
          type: integer
          description: The number of trades for the given geography
          format: int32
    GeographyInsightDtoPageResult:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/GeographyInsightDto'
          description: Gets the collection of entities for this page.
          nullable: true
        skip:
          type: integer
          format: int32
          nullable: true
        top:
          type: integer
          format: int32
          nullable: true
        count:
          type: integer
          description: Gets the total count of items in the result set
          format: int32
    GetAdjustmentResponse:
      type: object
      properties:
        adjustment:
          $ref: '#/components/schemas/AdjustmentDto'
      additionalProperties: false
    LeadChannelDto:
      enum:
        - Call
        - Click
        - Directory
        - Message
        - RequestAQuote
      type: string
    LeadCommitmentDto:
      type: object
      properties:
        min:
          type: integer
          format: int32
        max:
          type: integer
          format: int32
        monthlyDistribution:
          type: object
          additionalProperties:
            type: number
            format: double
          nullable: true
      additionalProperties: false
      description: "The forecasted commitment when the campaign was created. Depending on campaign type, this may be yearly\r\nor monthly."
    LeadDisputeDto:
      type: object
      properties:
        leadId:
          type: string
          nullable: true
        dateCreated:
          type: string
          format: date-time
        leadDisputeType:
          $ref: '#/components/schemas/LeadDisputeTypeDto'
        disputeReason:
          type: string
          nullable: true
        price:
          type: number
          format: double
        adjustedPrice:
          type: number
          format: double
        leadDisputeStatus:
          $ref: '#/components/schemas/LeadDisputeStatusDto'
        adjustedCategoryId:
          type: integer
          format: int32
          nullable: true
        adjustedSubCategoryId:
          type: integer
          format: int32
          nullable: true
        disputedLead:
          $ref: '#/components/schemas/LeadDto'
        budgetPeriodAdjusted:
          type: string
          format: date
          nullable: true
      additionalProperties: false
    LeadDisputeDtoPageResult:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/LeadDisputeDto'
          description: Gets the collection of entities for this page.
          nullable: true
        skip:
          type: integer
          format: int32
          nullable: true
        top:
          type: integer
          format: int32
          nullable: true
        count:
          type: integer
          description: Gets the total count of items in the result set
          format: int32
      additionalProperties: false
    LeadDisputeStatusDto:
      enum:
        - Pending
        - Approved
        - Rejected
        - Adjusted
      type: string
    LeadDisputeTypeDto:
      enum:
        - Spam
        - OutOfArea
        - WrongCategory
        - ContactDetails
        - Duplicate
        - Other
      type: string
    LeadDto:
      type: object
      properties:
        leadId:
          type: string
          nullable: true
        leadType:
          enum:
            - Lead
            - Click
          type: string
        isDisputed:
          type: boolean
          readOnly: true
        dateGenerated:
          type: string
          format: date-time
        channel:
          $ref: '#/components/schemas/LeadChannelDto'
        categoryId:
          type: integer
          format: int32
          nullable: true
        subCategoryId:
          type: integer
          format: int32
          nullable: true
        campaignId:
          type: string
          nullable: true
        disputeStatus:
          $ref: '#/components/schemas/LeadDisputeStatusDto'
      additionalProperties: false
    LeadDtoPageResult:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/LeadDto'
          description: Gets the collection of entities for this page.
          nullable: true
        skip:
          type: integer
          format: int32
          nullable: true
        top:
          type: integer
          format: int32
          nullable: true
        count:
          type: integer
          description: Gets the total count of items in the result set
          format: int32
      additionalProperties: false
    OfferingTypeDto:
      enum:
        - NewProduct
        - Upgrade
        - Downgrade
        - Amendment
        - BudgetRecalculation
        - Renewal
        - RenewalAmendment
      type: string
    ParentCategoryDto:
      type: object
      properties:
        parentCategoryId:
          type: integer
          description: Unique identifier for this parent category
          format: int32
        name:
          type: string
          description: The name of this parent category
          nullable: true
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/SubCategoryDto'
          description: The sub categories of this parent category
          nullable: true
      additionalProperties: false
      description: Parent Category for use with metadata endpoints; these represent CategoryDocuments from Trade Experience
    PaymentCadenceDto:
      enum:
        - Monthly
        - Quarterly
        - Annually
      type: string
    PostcodeAreaDto:
      type: object
      properties:
        postcodeArea:
          type: string
          description: Postcode Area
          nullable: true
        postcodeAreaName:
          type: string
          description: The most identifiable or largest town or city in this Postcode Area
          nullable: true
        region:
          type: string
          description: Parent Region
          nullable: true
        regionName:
          type: string
          description: Parent Region Name
          nullable: true
        postcodeDistricts:
          type: array
          items:
            $ref: '#/components/schemas/PostcodeDistrictDto'
          description: One or more Cathex.Campaigns.Contracts.Dtos.Internal.PostcodeDistrictDto that make up this Postcode Area
          nullable: true
      additionalProperties: false
      description: "A Postcode Area is the largest postcode-based geography in the UK. It's the first letter(s) of a Postcode.\r\n            \r\nSee <a href=\"https://github.com/cat-home-experts/gcp-trade-experience-contracts/blob/main/source/Cathex.Gcp.TradeExperience.Contracts/Documents/PostcodeAreaDocument.cs\">PostcodeAreaDocument.cs</a>"
    PostcodeDistrictDto:
      type: object
      properties:
        postcodeDistrict:
          type: string
          description: Postcode Outcode
          nullable: true
        sortOrder:
          type: integer
          description: This provides a stable value to use for ordering Postcode Districts including ones that end with letters.
          format: int32
        postcodeArea:
          type: string
          description: Parent Postcode Area
          nullable: true
        postcodeAreaName:
          type: string
          description: Parent Postcode Area Name
          nullable: true
        region:
          type: string
          description: Parent Postcode Area's Region
          nullable: true
        regionName:
          type: string
          description: Parent Postcode Area's Region Name
          nullable: true
        postcodeDistrictName:
          type: string
          description: Postcode District's name
          nullable: true
        postcodeDistrictCentroid:
          $ref: '#/components/schemas/GeoPointDto'
      additionalProperties: false
      description: "A Postcode District is the second largest postcode-based geography. It's the first half of a Postcode (the \"outcode\").\r\n            \r\nSee <a href=\"https://github.com/cat-home-experts/gcp-trade-experience-contracts/blob/main/source/Cathex.Gcp.TradeExperience.Contracts/Documents/PostcodeDistrictDocument.cs\">PostcodeDistrictDocument.cs</a>"
    PplCampaignDetailsDto:
      type: object
      properties:
        biddingStrategyType:
          $ref: '#/components/schemas/BiddingStrategyTypeDto'
        isDirectoryOptOut:
          type: boolean
        discount:
          $ref: '#/components/schemas/DiscountDto'
        leadCommitment:
          $ref: '#/components/schemas/LeadCommitmentDto'
      additionalProperties: false
    PplQuoteDetailsDto:
      type: object
      properties:
        selectedBudget:
          type: number
          description: "If a trade has selected this quote, this is the budget they picked. It must have a corresponding\r\nlead forecast for this budget amount.\r\n            \r\nThis is represented as a decimal, like all currency, but should actually be a whole number.\r\n            \r\nBudgets will be rounded up to the nearest increment of 10."
          format: double
          nullable: true
        recommendedBudget:
          type: number
          description: "The budget a UI might want to start with\r\n            \r\nThis is represented as a decimal, like all currency, but should actually be a whole number.\r\n            \r\nBudgets will be rounded up to the nearest increment of 10."
          format: double
        maxBudget:
          type: number
          description: "Max budget to allow them to select. e.g., Default to 2 x largest bundle.\r\n            \r\nThis is represented as a decimal, like all currency, but should actually be a whole number.\r\n            \r\nBudgets will be rounded up to the nearest increment of 10."
          format: double
        minBudget:
          type: number
          description: "Min budget to allow them to select. e.g., £100\r\n            \r\nThis is represented as a decimal, like all currency, but should actually be a whole number.\r\n            \r\nBudgets will be rounded up to the nearest increment of 10."
          format: double
        maxAverageLeadPrice:
          type: number
          description: Estimated maximum average lead price
          format: double
        minAverageLeadPrice:
          type: number
          description: Estimated minimum average lead price
          format: double
      additionalProperties: false
    ProblemDetails:
      type: object
      properties:
        type:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        status:
          type: integer
          format: int32
          nullable: true
        detail:
          type: string
          nullable: true
        instance:
          type: string
          nullable: true
      additionalProperties: {}
    PubSubResponseStatusCode:
      enum:
        - OK
        - BadRequest
        - Unauthenticated
        - PermissionDenied
        - NotFound
        - AlreadyExists
        - ResourceExhausted
        - Cancelled
        - InternalServerError
        - BadGateway
        - Unavailable
        - DeadlineExceeded
      type: string
    QuoteDto:
      type: object
      properties:
        quoteId:
          type: string
          description: Unique ID of this quote
          nullable: true
        quoteType:
          $ref: '#/components/schemas/QuoteTypeDto'
        offeringType:
          $ref: '#/components/schemas/OfferingTypeDto'
        leadForecast:
          $ref: '#/components/schemas/QuoteLeadForecastDto'
        leadForecastUpgradeDelta:
          $ref: '#/components/schemas/QuoteLeadForecastDto'
        fixedDetails:
          $ref: '#/components/schemas/FixedQuoteDetailsDto'
        pplDetails:
          $ref: '#/components/schemas/PplQuoteDetailsDto'
        tierLabel:
          type: string
          description: 'Display label for this quote (e.g., Lite, Standard, Pro)'
          nullable: true
        order:
          type: integer
          description: Field indicating a relative order against all quotes in a Quote Set
          format: int32
        isRecommended:
          type: boolean
          description: "Indicates that this quote, among quotes in a Quote Set, is the recommended one. Only one quote in a Quote Set\r\nwill be recommended."
        isSpecialOffer:
          type: boolean
          description: Indicates that this quote is a special offer
        agentDiscounts:
          type: array
          items:
            $ref: '#/components/schemas/DiscountOfferDto'
          description: 'Discounts that an agent can offer to a trade. Initially only against a bundle campaign, and probably hard-coded.'
          nullable: true
        isSelected:
          type: boolean
          description: True if the trade has already selected this quote
      additionalProperties: false
    QuoteLeadForecastDto:
      type: object
      properties:
        min:
          type: integer
          description: Predicted fewest number of leads per month
          format: int32
        max:
          type: integer
          description: Predicted most number of leads per month
          format: int32
        monthlyDistribution:
          type: object
          additionalProperties:
            type: number
            format: double
          description: Map of Budget Period to multiplier that represents seasonality
          nullable: true
        selectedBudget:
          type: number
          description: "The budget amount used for this forecast, if it is a PPL quote\r\n            \r\nThis is represented as a decimal, like all currency, but should actually be a whole number.\r\n            \r\nBudgets will be rounded up to the nearest increment of 10."
          format: double
          nullable: true
      additionalProperties: false
      description: "Min and Max leads forecasted for the following month. For a PPL campaign, the budget used is the\r\nselected (if already selected), most recently re-forecasted, or recommended budget."
    QuoteLeadForecastsDto:
      type: object
      properties:
        forecasts:
          type: array
          items:
            $ref: '#/components/schemas/QuoteLeadForecastDto'
          nullable: true
      additionalProperties: false
      description: Set of forecasts for a quote
    QuoteLeadReforecastDto:
      type: object
      properties:
        selectedBudget:
          type: number
          description: "The budget used for this forecast\r\n            \r\nThis is represented as a decimal, like all currency, but should actually be a whole number.\r\n            \r\nBudgets will be rounded up to the nearest increment of 10."
          format: double
        min:
          type: integer
          description: Predicted fewest number of leads per month
          format: int32
        max:
          type: integer
          description: Predicted most number of leads per month
          format: int32
        monthlyDistribution:
          type: object
          additionalProperties:
            type: number
            format: double
          description: Map of Budget Period to multiplier that represents seasonality
          nullable: true
      additionalProperties: false
    QuoteRequestBehaviorDto:
      enum:
        - OfferCampaignQuotes
        - CallSupport
        - Alsp
      type: string
    QuoteRequestDto:
      type: object
      properties:
        quoteRequestId:
          type: string
          description: Unique ID of this Quote Request
          nullable: true
        behavior:
          $ref: '#/components/schemas/QuoteRequestBehaviorDto'
        dateCreated:
          type: string
          description: Date this quote request was created/requested
          format: date-time
        previousQuoteRequestId:
          type: string
          description: "Refreshing a quote request creates a new one, but hangs on to the old one. Can also store previous\r\nquote request IDs if, for example, an agent updates the parameters of a quote."
          nullable: true
        supersedingQuoteRequestId:
          type: string
          description: "If this Quote Request is superseded by a new one (specified as Cathex.Campaigns.Contracts.Dtos.QuoteRequests.QuoteRequestDto.PreviousQuoteRequestId in a\r\n\"Create\" or \"Refresh\" action), this is the newer ID"
          nullable: true
        companyId:
          type: string
          description: "The trade's Company ID, if already known"
          nullable: true
        status:
          $ref: '#/components/schemas/QuoteRequestStatusDto'
        selectedQuoteId:
          type: string
          description: Indicates which quote is already selected
          nullable: true
        summary:
          $ref: '#/components/schemas/QuoteSetSummaryDto'
        quoteSet:
          type: array
          items:
            $ref: '#/components/schemas/QuoteDto'
          description: "Individual quotes (options) that respond to this quote request. May be empty if the Cathex.Campaigns.Contracts.Dtos.QuoteRequests.QuoteRequestDto.Behavior does\r\nnot support quotes."
          nullable: true
        validTo:
          type: string
          description: Expiration date of the quotes in this quote request.
          format: date-time
        source:
          $ref: '#/components/schemas/QuoteRequestSourceDto'
        geographies:
          type: array
          items:
            $ref: '#/components/schemas/GeographyDto'
          description: The geographies originally included in the request
          nullable: true
        category:
          $ref: '#/components/schemas/CategoryDto'
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
          description: One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Dtos.QuoteRequests.QuoteRequestDto.Category)
          nullable: true
        companySize:
          $ref: '#/components/schemas/CompanySizeDto'
        sourceCampaignId:
          type: string
          description: 'In the case of an upgrade (or a modification), this is the Campaign that is being upgraded'
          nullable: true
      additionalProperties: false
      description: A Quote Request is a recommend offering to a trade along with the original inputs of the request
    QuoteRequestSourceDto:
      enum:
        - Dsu
        - Gsf
        - TradeApp
        - Migration
        - AlspRenewal
        - AlspRenewalAmendment
        - CampaignAdmin
        - Unattended
      type: string
      description: The source of this quote request
    QuoteRequestStatusDto:
      enum:
        - Active
        - Expired
        - Converted
      type: string
    QuoteRequestTypeDto:
      enum:
        - Acquisition
        - Renewal
        - RenewalAmendment
        - Upgrade
        - AdditionalProduct
        - Amendment
        - Lifecycle
      type: string
    QuoteSetSummaryAmendmentDto:
      enum:
        - NotApplicable
        - AvailableLeadCommitmentChanged
        - AvailableLeadCommitmentUnchanged
        - Unavailable
        - NotEnoughLeads
      type: string
    QuoteSetSummaryBudgetRecalculationDto:
      enum:
        - NotApplicable
        - Recommended
      type: string
    QuoteSetSummaryDto:
      required:
        - amendment
        - budgetRecalculation
        - newProduct
        - ppl
        - upgrade
      type: object
      properties:
        amendment:
          $ref: '#/components/schemas/QuoteSetSummaryAmendmentDto'
        budgetRecalculation:
          $ref: '#/components/schemas/QuoteSetSummaryBudgetRecalculationDto'
        newProduct:
          $ref: '#/components/schemas/QuoteSetSummaryNewProductDto'
        ppl:
          $ref: '#/components/schemas/QuoteSetSummaryPplDto'
        upgrade:
          $ref: '#/components/schemas/QuoteSetSummaryUpgradeDto'
    QuoteSetSummaryNewProductDto:
      enum:
        - NotApplicable
        - Available
        - Unavailable
      type: string
    QuoteSetSummaryPplDto:
      enum:
        - NotApplicable
        - Available
        - Unavailable
      type: string
    QuoteSetSummaryUpgradeDto:
      enum:
        - NotApplicable
        - Available
        - Unavailable
        - OffTarget
        - NotEnoughLeads
        - NotEnoughTimeRemaining
      type: string
    QuoteTypeDto:
      enum:
        - Fixed
        - Ppl
      type: string
    RateBidDto:
      required:
        - call
        - categoryId
        - click
        - directory
        - message
        - requestAQuote
      type: object
      properties:
        call:
          type: number
          description: Agreed price of a lead for calls to phone numbers listed in search results
          format: double
        click:
          type: number
          description: Agreed price of a click delivered to clients' websites from search results
          format: double
        directory:
          type: number
          description: Agreed price of a lead for calls made to phone numbers listed in printed directories
          format: double
        message:
          type: number
          description: Agreed price of a lead for direct messages to a client
          format: double
        requestAQuote:
          type: number
          description: Agreed price of a lead where our Request-a-Quote option has picked this client
          format: double
        categoryId:
          type: integer
          description: The Category this Rate applies to
          format: int32
        subCategoryId:
          type: integer
          description: The sub-category the rate applies to. This can only be null if it's the one rate at the Category level.
          format: int32
          nullable: true
        dateAgreed:
          type: string
          description: Date this rate agreement was made with the client. Defaults to now.
          format: date-time
          nullable: true
      additionalProperties: false
      description: "The agreed upon rates by channel for the associated campaign's category (or sub-categories).\r\nThere will always be at least one agreed rate per campaign at the category level and at least\r\none at the sub-category level (because campaigns have at least one sub-category).\r\n            \r\nWhen a new rate is agreed to in a given sub-category, it can be stored without having to also agree\r\nto new rates in the other sub-categories. The previously agreed rates will continue to apply."
    ReforecastQuoteRequest:
      required:
        - selectedBudget
      type: object
      properties:
        selectedBudget:
          type: number
          description: "Selected budget. Must be between Min and Max budget of the PPL quote\r\n            \r\nThis is represented as a decimal, like all currency, but should actually be a whole number.\r\n            \r\nBudgets will be rounded up to the nearest increment of 10."
          format: double
    RefreshQuoteRequest:
      type: object
      additionalProperties: false
    RegionDto:
      type: object
      properties:
        region:
          type: string
          description: Slugified version of Cathex.Campaigns.Contracts.Dtos.Internal.RegionDto.RegionName to unique identify the region
          nullable: true
        regionName:
          type: string
          description: Name of this Region
          nullable: true
        postcodeAreas:
          type: array
          items:
            $ref: '#/components/schemas/PostcodeAreaDto'
          description: One or more Cathex.Campaigns.Contracts.Dtos.Internal.PostcodeAreaDto that make up this Region
          nullable: true
      additionalProperties: false
      description: "A Region is a higher level division of the UK than Postcode Area. It is loosely based on the former Government\r\nOffice Regions (GOR) in England with pseudo regions for the other countries in the UK. Examples include East Midlands,\r\nWales (non-GOR region), Greater London (non-GOR region but Checkatrade convention), etc. It has many Cathex.Campaigns.Contracts.Dtos.Internal.PostcodeAreaDto.\r\n            \r\nAt present, Campaigns cannot be assigned Regions.\r\n            \r\nSee <a href=\"https://github.com/cat-home-experts/gcp-trade-experience-contracts/blob/main/source/Cathex.Gcp.TradeExperience.Contracts/Documents/RegionDocument.cs\">RegionDocument.cs</a>"
    RegionDtoPageResult:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/RegionDto'
          description: Gets the collection of entities for this page.
          nullable: true
        skip:
          type: integer
          format: int32
          nullable: true
        top:
          type: integer
          format: int32
          nullable: true
        count:
          type: integer
          description: Gets the total count of items in the result set
          format: int32
      additionalProperties: false
    SelectQuoteRequest:
      required:
        - quoteId
      type: object
      properties:
        quoteId:
          minLength: 1
          type: string
        selectedBudget:
          type: number
          description: "If the Quote is for a PPL Campaign, this must be a budget that has been forecasted.\r\n            \r\nThis is represented as a decimal, like all currency, but should actually be a whole number.\r\n            \r\nBudgets will be rounded up to the nearest increment of 10."
          format: double
          nullable: true
      additionalProperties: false
    SponsorshipCampaignDetailsDto:
      type: object
      properties:
        isSearchSponsored:
          type: boolean
          description: True if this sponsored listing campaign is enabled in Search
        isRequestAQuoteSponsored:
          type: boolean
          description: True if this sponsored listing campaign is enabled in Request a Quote
        searchRateMultiplier:
          type: number
          description: 'Price multiplier used for Search leads, if search sponsored'
          format: double
          nullable: true
        requestAQuoteRateMultiplier:
          type: number
          description: 'Price multiplier used for Request a Quote leads, if RaQ sponsored'
          format: double
          nullable: true
      additionalProperties: false
    SubCategoryDto:
      type: object
      properties:
        categoryId:
          type: integer
          description: Sub-category ID
          format: int32
        name:
          type: string
          description: The name of the sub-category
          nullable: true
        isSelectedByDefault:
          type: boolean
          description: Whether this sub-category is selected by default when creating a new campaign
      additionalProperties: false
      description: Sub-Category for use with metadata endpoints; these represent CategoryDocuments' Sub-Category from Trade Experience
    UpdateAvailabilityRequest:
      type: object
      properties:
        pausedUntil:
          type: string
          description: "Date this campaign should be paused until, exclusive of this date. (If this value is set,\r\nit is set to the date that the campaign should be understood to be \"unpaused.\")\r\n            \r\nClients can pause their campaigns for a limited period of time, and we can pause a campaign until any date in the future."
          format: date
          nullable: true
      additionalProperties: false
      description: Updates the paused date for a campaign
    UpdateBiddingStrategyRequest:
      type: object
      properties:
        biddingStrategyType:
          $ref: '#/components/schemas/BiddingStrategyTypeDto'
      additionalProperties: false
    UpdateMaxSpendRequest:
      required:
        - budgetPeriod
        - maxSpend
      type: object
      properties:
        budgetPeriod:
          type: string
          format: date
        isOneTimeTopUp:
          type: boolean
          description: "If true and the following month's budget isn't explicitly set, then we need to take this month's budget and then\r\nclone it for next month. If this is false, it's just their way of saying \"here's the new max spend starting\r\nin this period and applying into the future until said otherwise.\""
          nullable: true
        maxSpend:
          type: number
          description: "Maximum amount the Trade is willing to pay for a campaign in a given budget period regardless of\r\nminimum commitment budget boosts"
          format: double
    UpdateGeographiesRequest:
      type: object
      properties:
        geographies:
          type: array
          items:
            $ref: '#/components/schemas/GeographyDto'
          nullable: true
      additionalProperties: false
    UpdateLeadDisputeRequest:
      type: object
      properties:
        adjustedLeadPrice:
          type: number
          format: double
          nullable: true
        leadDisputeStatus:
          $ref: '#/components/schemas/LeadDisputeStatusDto'
        adjustedCategoryId:
          type: integer
          format: int32
          nullable: true
        adjustedSubCategoryId:
          type: integer
          format: int32
          nullable: true
        comments:
          type: string
          nullable: true
      additionalProperties: false
    UpdateParentCategoryRequest:
      required:
        - category
      type: object
      properties:
        category:
          $ref: '#/components/schemas/CategoryDto'
      additionalProperties: false
      description: Updates the parent category of a campaign
    UpdateRateBidsRequest:
      type: object
      properties:
        rateBids:
          type: array
          items:
            $ref: '#/components/schemas/RateBidDto'
          nullable: true
      additionalProperties: false
    UpdateStatusRequest:
      type: object
      properties:
        isActive:
          type: boolean
          nullable: true
        isRequestAQuoteOptOut:
          type: boolean
          nullable: true
        isDirectoryOptOut:
          type: boolean
          nullable: true
      additionalProperties: false
      description: 'Updates the active, directory opt-out and RaQ opt-out flags for a campaign'
    UpdateSubCategoriesRequest:
      type: object
      properties:
        category:
          $ref: '#/components/schemas/CategoryDto'
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
          nullable: true
        updateType:
          $ref: '#/components/schemas/CampaignAttributeUpdateTypeDto'
      additionalProperties: false
    ValidationProblemDetails:
      type: object
      properties:
        type:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        status:
          type: integer
          format: int32
          nullable: true
        detail:
          type: string
          nullable: true
        instance:
          type: string
          nullable: true
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          nullable: true
          readOnly: true
      additionalProperties: {}
    CreateUpgradeOptionsRequest:
      required:
        - source
      type: object
      additionalProperties: false
      description: Fetch a Quote for a Campaign Upgrade
      properties:
        source:
          $ref: '#/components/schemas/QuoteRequestSourceDto'
        amendment:
          $ref: '#/components/schemas/CampaignAmendmentDto'
    UpgradeCampaignRequest:
      required:
        - quoteId
        - quoteRequestId
      type: object
      properties:
        quoteRequestId:
          minLength: 1
          type: string
          nullable: true
          description: An Upgrade typed Quote Request
        quoteId:
          minLength: 1
          type: string
          nullable: true
          description: A Quote in Cathex.Campaigns.Contracts.Messages.Campaign.UpgradeCampaignRequest.QuoteRequestId's Quote Set
      additionalProperties: false
      description: Upgrade a campaign based on a new quote request
    UpgradeCampaignResponse:
      required:
        - campaignId
        - companyId
        - upgradeId
      type: object
      properties:
        upgradeId:
          minLength: 1
          type: string
        campaignId:
          minLength: 1
          type: string
        companyId:
          minLength: 1
          type: string
    PriceRangesDtoPageResult:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/PriceRangeItem'
    PriceRangeItem:
      type: object
      properties:
        categoryId:
          type: integer
        subCategoryId:
          type: integer
        call:
          $ref: '#/components/schemas/PriceDetail'
        click:
          $ref: '#/components/schemas/PriceDetail'
        directory:
          $ref: '#/components/schemas/PriceDetail'
        message:
          $ref: '#/components/schemas/PriceDetail'
        requestAQuote:
          $ref: '#/components/schemas/PriceDetail'
        averageRate:
          type: number
          format: float
    PriceDetail:
      type: object
      properties:
        maxPrice:
          type: number
          format: float
        minPrice:
          type: number
          format: float
    PriceRangesRequest:
      type: object
      additionalProperties: false
