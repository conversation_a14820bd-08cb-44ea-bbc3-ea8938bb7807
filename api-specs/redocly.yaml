apis:
  campaigns-api@v1:
    root: ./campaigns-api.yaml
    x-openapi-ts:
      output: ../app/src/data/api-specs/campaigns-api.ts
  communication-api@v1:
    root: ./communication-api.yaml
    x-openapi-ts:
      output: ../app/src/data/api-specs/communication-api.ts
  member-data-api@v1:
    root: ./member-data-api.yaml
    x-openapi-ts:
      output: ../app/src/data/api-specs/member-data-api.ts
  trade-app-bff@v1:
    root: ./trade-app-bff.yaml
    x-openapi-ts:
      output: ../app/src/data/api-specs/trade-app-bff.ts
  trades-api@v1:
    root: ./trades-api.yaml
    x-openapi-ts:
      output: ../app/src/data/api-specs/trades-api.ts
  cat-for-business-api@v1:
    root: ./cat-business-api.yaml
    x-openapi-ts:
      output: ../app/src/data/api-specs/cat-business-api.ts
