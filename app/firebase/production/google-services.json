{"project_info": {"project_number": "263011460521", "firebase_url": "https://cat-trades.firebaseio.com", "project_id": "cat-trades", "storage_bucket": "cat-trades.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:263011460521:android:f3f4be28816f84163750b8", "android_client_info": {"package_name": "com.checkatrade.tradeapp"}}, "oauth_client": [{"client_id": "263011460521-u0p2s45j7tlh4i2cjdlgg4k43os8dd5u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCPAQT_JQzpaLv_feSCxMq8WydzIIqKx30"}, {"current_key": "AIzaSyDFfka1Of3MeN99LcQaYIXo0Txj0PcZ4dk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "263011460521-6rpb33dripnvvvahbqi21ehvt0d7s9rm.apps.googleusercontent.com", "client_type": 3}, {"client_id": "263011460521-9dt08ipimltile8g33406h9gp47df4j8.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.cat.trade"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:263011460521:android:b2e6d4936517a6593750b8", "android_client_info": {"package_name": "com.checkatrade.tradeapp.prod"}}, "oauth_client": [{"client_id": "263011460521-u0p2s45j7tlh4i2cjdlgg4k43os8dd5u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCPAQT_JQzpaLv_feSCxMq8WydzIIqKx30"}, {"current_key": "AIzaSyDFfka1Of3MeN99LcQaYIXo0Txj0PcZ4dk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "263011460521-6rpb33dripnvvvahbqi21ehvt0d7s9rm.apps.googleusercontent.com", "client_type": 3}, {"client_id": "263011460521-9dt08ipimltile8g33406h9gp47df4j8.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.cat.trade"}}]}}}], "configuration_version": "1"}