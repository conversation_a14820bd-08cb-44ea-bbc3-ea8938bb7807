#!/bin/bash
set -e

# https://docs.expo.dev/build-reference/variables/#built-in-environment-variables
# https://docs.expo.dev/build-reference/npm-hooks/

if [[ "$IS_APP_STORE_BUILD" != true && "$EAS_BUILD_PROFILE" != "dev-client-simulator" ]]; then
    SHORT_GIT_SHA="${EAS_BUILD_GIT_COMMIT_HASH:0:7}"

    # Upload to Browserstack App Automate
    if [[ "$IS_DEV_CLIENT" != true ]]; then
        ./scripts/upload-to-browserstack-app-automate.sh $EAS_BUILD_PLATFORM $EXPO_PUBLIC_ENVIRONMENT $SHORT_GIT_SHA
    fi

    # Upload to Browserstack App Live
    ./scripts/upload-to-browserstack-app-live.sh $EAS_BUILD_PLATFORM $EXPO_PUBLIC_ENVIRONMENT $SHORT_GIT_SHA $IS_DEV_CLIENT
else
    echo "Skipping Browserstack App Automate and App Live upload for App Store build"
fi
