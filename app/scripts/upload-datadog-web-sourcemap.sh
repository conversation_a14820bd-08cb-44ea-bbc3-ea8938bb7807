#!/bin/bash
set -e

ENVIRONMENT=$1

DIST_PATH="web-build/static/js"
DATADOG_SERVICE="trade-experience-web"
DATADOG_RELEASE_VERSION=$(git rev-parse --short HEAD)

if [[ $ENVIRONMENT == "production" ]]; then
    DATADOG_MINIFIED_PATH_PREFIX="https://membersapp.checkatrade.com/static/js"
elif [[ $ENVIRONMENT == "staging" ]]; then
    DATADOG_MINIFIED_PATH_PREFIX="https://membersapp-staging.checkatrade.com/static/js"
else
    DATADOG_MINIFIED_PATH_PREFIX="https://membersapp-development.checkatrade.com/static/js"
fi

npx datadog-ci sourcemaps upload $DIST_PATH \
    --service=$DATADOG_SERVICE \
    --release-version=$DATADOG_RELEASE_VERSION \
    --minified-path-prefix=$DATADOG_MINIFIED_PATH_PREFIX

# Delete sourcemaps after upload
rm -f $DIST_PATH/*.map
rm -f $DIST_PATH/*.LICENSE.txt
