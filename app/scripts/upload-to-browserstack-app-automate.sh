#!/bin/bash
set -e

PLATFORM=$1
ENVIRONMENT=$2
SHORT_GIT_SHA=$3

BRANCH=$(git rev-parse --abbrev-ref HEAD)

if [[ $BRANCH == "main" ]]; then
  BUILD_NAME="$SHORT_GIT_SHA-$ENVIRONMENT-main"
else
  BUILD_NAME="$SHORT_GIT_SHA-$ENVIRONMENT"
fi

echo "rev-parse: $(git rev-parse --abbrev-ref HEAD)"
echo "branch: $(git branch --show-current)"

function upload_app() {
  local platform=$1
  local src_path=$2
  local dest_path=$3

  cp $src_path $dest_path

  local upload_response=$(
    curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_TOKEN" \
      -X POST "https://api-cloud.browserstack.com/app-automate/upload" \
      -F "file=@$dest_path" \
      -F "data={\"custom_id\": \"$BUILD_NAME\"}" \
      --max-time 450
  )

  echo $upload_response
}

function trigger_webhook() {
  local platform=$1
  local browserstack_build_id=$2

  curl -X POST \
    -H "Accept: application/vnd.github.everest-preview+json" \
    -H "Authorization: token $GITHUB_WEBHOOK_TOKEN" \
    --data "{\"event_type\": \"expo_eas_${platform}_build_success\", \"client_payload\": {\"browserstack_build_id\": \"$browserstack_app\"} }" \
    https://api.github.com/repos/cat-home-experts/gcp-trade-experience/dispatches
}

function deploy_and_trigger_webhook() {
  local platform=$1
  local src_path=$2
  local dest_path=$3

  echo "starting AppAutomate deployment ($platform)"

  local upload_response=$(upload_app $platform $src_path $dest_path)
  local browserstack_app=$(echo $upload_response | jq -r ".app_url")

  if [ $browserstack_app != null ]; then
    echo "$BUILD_NAME $platform app deployed to BrowserStack with id: $browserstack_app"
    if [[ $BRANCH == "main" ]]; then
      echo "Executing $BUILD_NAME $platform GitHub Actions webhook with BrowserStack ID: $browserstack_app"
      if trigger_webhook $platform $browserstack_app; then
        echo "$BUILD_NAME $platform GitHub Actions webhook with BrowserStack ID: $browserstack_app successfuly executed"
      fi
    fi
  else
    local upload_error_message=$(echo $upload_response | jq -r ".error")
    echo "$BUILD_NAME app deployment failed, reason: ",$upload_error_message
    exit 1
  fi
}

if [[ $PLATFORM == "android" ]]; then
  APK_SRC_PATH=$(pwd)/android/app/build/outputs/apk/release/app-release.apk
  APK_DEST_PATH=$(pwd)/$BUILD_NAME.apk

  deploy_and_trigger_webhook $PLATFORM $APK_SRC_PATH $APK_DEST_PATH
fi

if [[ $PLATFORM == "ios" ]]; then
  IPA_SRC_PATH=$(pwd)/ios/build/Checkatrade.ipa
  IPA_DEST_PATH=$(pwd)/$BUILD_NAME.ipa

  deploy_and_trigger_webhook $PLATFORM $IPA_SRC_PATH $IPA_DEST_PATH
fi
