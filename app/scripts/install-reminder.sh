#!/bin/zsh
set -e

# GitHub Actions, GCP Cloud build, Expo EAS or other CI
if [[ -n "$CI" ]]; then exit 0; fi

NO_FORMAT="\033[0m"
F_BOLD="\033[1m"
C_YELLOW="\033[38;5;11m"
F_DIM="\033[2m"

FILE="$1"
CACHE_FILE_NAME="$2"

if [ -f "$CACHE_FILE_NAME" ]; then
  EXISTING_SHA1="$(cat "$CACHE_FILE_NAME")"
  if [[ "$EXISTING_SHA1" == "$(git ls-files -s "$FILE")" ]]; then exit 0; fi
  MESSAGE="\`${FILE}\` has changed. Remember to run \`npm i\` in the app directory."
else
  MESSAGE="\`$CACHE_FILE_NAME\` does not exist. Please run \`npm i\` in app directory."
fi

STRING_LENGTH=$((${#MESSAGE} + 2))
DIVIDER=$(printf '*%.0s' {1..$STRING_LENGTH})

echo "${F_DIM}**$DIVIDER**"
echo -e "${F_DIM}** ${NO_FORMAT}${F_BOLD}${C_YELLOW}${MESSAGE}${NO_FORMAT}${F_DIM} **"
echo "${F_DIM}**$DIVIDER**"
