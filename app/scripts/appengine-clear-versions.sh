#!/bin/bash
#
# Deletes old App Engine versions.

readonly MIN_VERSIONS_TO_KEEP=40
readonly MAX_GLOBAL_APPENGINE_VERSIONS=200

SERVICES="$(gcloud app services list --format 'value(id)')" ||\
  echo "Error fetching services list"
SERVICES_COUNT="$(gcloud app services list --format 'value(id)'|wc -l)" ||\
  echo "Error fetching services count"
readonly SERVICES
readonly SERVICES_COUNT

# Calculates the max number of versions per service to keep before deleting.
MAX_VERSIONS_PER_SERVICE=$((MAX_GLOBAL_APPENGINE_VERSIONS / SERVICES_COUNT))
MAX_VERSIONS_PER_SERVICE=$((MAX_VERSIONS_PER_SERVICE - MIN_VERSIONS_TO_KEEP))
MAX_VERSIONS_PER_SERVICE=$((MAX_VERSIONS_PER_SERVICE > 0 ? MAX_VERSIONS_PER_SERVICE : 0 ))
readonly MAX_VERSIONS_PER_SERVICE

for service in ${SERVICES}; do
  service_versions="$(
    gcloud app versions list \
		  --service "${service}" \
		  --sort-by '~version.createTime' \
		  --format 'table[no-heading](version.id)' \
		  --filter="TRAFFIC_SPLIT=0.00")" ||\
      echo "Error fetching app versions"

  count=0
  for version in ${service_versions}; do
     ((count++))
     if [ $count -gt ${MAX_VERSIONS_PER_SERVICE} ]; then
       echo "......... Deleting version ${version}(${service})"
       gcloud app versions delete --quiet --service="${service}" "${version}" ||\
         echo "Error deleting version ${version} (${service})"
     else
       echo "......... Keeping version ${version} (${service})"
     fi
  done
done
