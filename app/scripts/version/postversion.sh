#!/bin/bash
set -e

PACKAGE_VERSION=$(node ./scripts/version/getPackageVersion.js)
PKG_VERSION_DASHES="${PACKAGE_VERSION//./-}"

# Create final name
GIT_SHA=$(git rev-parse --short HEAD)
OLD_BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)
NEW_BRANCH_NAME="${OLD_BRANCH_NAME//$GIT_SHA/$PKG_VERSION_DASHES}"
git branch -m $OLD_BRANCH_NAME $NEW_BRANCH_NAME

# Commit changes
git add package.json package-lock.json
git commit -m "Release v$PACKAGE_VERSION" --no-verify

git push --set-upstream origin $NEW_BRANCH_NAME --no-verify
