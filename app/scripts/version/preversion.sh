#!/bin/bash
set -e

if [[ "$OSTYPE" != "darwin"* ]]; then
  echo "npm version scripts only work on macos"
  exit 1
fi

# check clean status
GIT_STATUS=$(git status --porcelain)
if [[ ! -z $GIT_STATUS ]]; then
  echo "Error: git status not clean"
  exit 1
fi

# git up to date
git fetch

if [[ $(git rev-parse HEAD) != $(git rev-parse @{u}) ]]; then
  echo "Error: git not up to date, use git pull"
  exit 1
fi

# warn if not on main branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [[ $CURRENT_BRANCH != "main" ]]; then
  read -p "You are not on the main branch. Do you want to continue? (y/n)" -n 1 -r
  echo

  if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo 'continuing with release'
  else
    exit 0
  fi
fi

# Prompt for JIRA ticket number with regex validation
JIRA_TICKET_REGEX="^[A-Z]{2,5}-[0-9]+$"
while true; do
  read -p "Enter JIRA ticket number (e.g. TEX-123): " JIRA_TICKET
  if [[ $JIRA_TICKET =~ $JIRA_TICKET_REGEX ]]; then
    break
  else
    echo "Invalid ticket format. Please use format like TEX-123"
  fi
done

# Create temp branch
git checkout -b next/${JIRA_TICKET}_release-$(git rev-parse --short HEAD)
