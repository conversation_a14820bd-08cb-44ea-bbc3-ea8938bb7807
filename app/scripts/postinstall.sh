#!/bin/bash
set -e

IS_CI=false

# GitHub Actions, GCP Cloud build, Expo EAS or other CI
if [[ ! -z "$CI" ]]; then
    IS_CI=true
fi

echo "IS_CI: ${IS_CI}"

echo "patching npm packages"
npx patch-package

if [[ ! -z "$EAS_BUILD" && "$EAS_BUILD_PLATFORM" = "ios" ]]; then
  npm run set-netrc
  npm run fetch-adyen-libs
fi

if [[ "$IS_CI" != true ]]; then
    git ls-files -s package-lock.json >".lock-sha"
fi

if [[ "$IS_CI" != true ]]; then
    echo "Installing root directory npm packages"
    cd ..
    npm install
    cd app
fi



if [[ "$IS_CI" == true ]]; then
    ./scripts/generate-ci-env-file.sh
fi
