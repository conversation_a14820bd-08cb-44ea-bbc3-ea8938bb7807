#!/bin/bash
set -e

echo "Generating CI environment file"

ENV_FILE_PATH="./.env"

if [[ "$EAS_BUILD" == "true" ]]; then
    COMMIT_SHA="${EAS_BUILD_GIT_COMMIT_HASH:0:7}"
    echo "EXPO_PUBLIC_GIT_COMMIT_SHA=$COMMIT_SHA" >$ENV_FILE_PATH
    echo "EXPO_PUBLIC_ENVIRONMENT=$EXPO_PUBLIC_ENVIRONMENT" >>$ENV_FILE_PATH
else
    COMMIT_SHA=$(git rev-parse --short HEAD)
    echo "EXPO_PUBLIC_GIT_COMMIT_SHA=$COMMIT_SHA" >$ENV_FILE_PATH
fi

# Append if you need any additional environment variables
# echo "ADDITIONAL=test" >> $ENV_FILE_PATH
