#!/bin/bash
set -e

# This script uploads sourcemaps to Datadog for React Native apps. PRODUCTION ONLY
# https://github.com/DataDog/datadog-ci/tree/master/src/commands/react-native

PACKAGE_VERSION=$(node ./scripts/version/getPackageVersion.js)
GIT_SHA=$(git rev-parse --short HEAD)

DIST_PATH="./dist/_expo/static/js"
DATADOG_RELEASE_VERSION="$PACKAGE_VERSION-$GIT_SHA"

# The build version is hardcoded for now. We can fetch using the expo eas cli in the future.
# https://github.com/expo/eas-cli?tab=readme-ov-file#eas-buildversionget
# e.g. eas build:version:get --json --non-interactive -p android -e production
BUILD_VERSION_IOS=17730
BUILD_VERSION_ANDROID=17730

ls -R $DIST_PATH

# Upload iOS sourcemaps
echo "Uploading iOS sourcemaps"
DATADOG_SERVICE_IOS="com.cat.trade"
IOS_BUNDLE_PATH=$(find "$DIST_PATH/ios" -type f -name "index-*.hbc")
IOS_SOURCEMAP_PATH=$(find "$DIST_PATH/ios" -type f -name "index-*.hbc.map")

echo "IOS_BUNDLE_PATH: $IOS_BUNDLE_PATH"
echo "IOS_SOURCEMAP_PATH: $IOS_SOURCEMAP_PATH"

npx datadog-ci react-native upload \
    --platform=ios \
    --service=$DATADOG_SERVICE_IOS \
    --sourcemap=$IOS_SOURCEMAP_PATH \
    --bundle=$IOS_BUNDLE_PATH \
    --release-version=$DATADOG_RELEASE_VERSION \
    --build-version=$BUILD_VERSION_IOS

# Upload Android sourcemaps
echo "Uploading Android sourcemaps"
DATADOG_SERVICE_ANDROID="com.checktrade.tradeapp"
ANDROID_BUNDLE_PATH=$(find "$DIST_PATH/android" -type f -name "index-*.hbc")
ANDROID_SOURCEMAP_PATH=$(find "$DIST_PATH/android" -type f -name "index-*.hbc.map")

echo "ANDROID_BUNDLE_PATH: $ANDROID_BUNDLE_PATH"
echo "ANDROID_SOURCEMAP_PATH: $ANDROID_SOURCEMAP_PATH"

npx datadog-ci react-native upload \
    --platform=android \
    --service=$DATADOG_SERVICE_ANDROID \
    --bundle=$ANDROID_BUNDLE_PATH \
    --sourcemap=$ANDROID_SOURCEMAP_PATH \
    --release-version=$DATADOG_RELEASE_VERSION \
    --build-version=$BUILD_VERSION_ANDROID
