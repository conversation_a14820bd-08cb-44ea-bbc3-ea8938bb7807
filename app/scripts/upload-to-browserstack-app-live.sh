#!/bin/bash
set -e

PLATFROM=$1
ENVIRONMENT=$2
SHORT_GIT_SHA=$3
IS_DEV_CLIENT=$4

BRANCH=$(git rev-parse --abbrev-ref HEAD)

if [[ $IS_DEV_CLIENT == true ]]; then
    BUILD_NAME="$SHORT_GIT_SHA-dev-client"
elif [[ $BRANCH == "main" ]]; then
    BUILD_NAME="$SHORT_GIT_SHA-$ENVIRONMENT-main"
else
    BUILD_NAME="$SHORT_GIT_SHA-$ENVIRONMENT"
fi

echo "rev-parse: $(git rev-parse --abbrev-ref HEAD)"
echo "branch: $(git branch --show-current)"

if [[ $PLATFROM == "android" ]]; then
    echo "starting AppLive deployment (android)"

    APK_SRC_BASE_PATH=$(pwd)/android/app/build/outputs/apk
    if [[ $IS_DEV_CLIENT == true ]]; then
        APK_SRC_PATH=$APK_SRC_BASE_PATH/debug/app-debug.apk
    else
        APK_SRC_PATH=$APK_SRC_BASE_PATH/release/app-release.apk
    fi
    APK_DEST_PATH=$(pwd)/$BUILD_NAME.apk

    cp $APK_SRC_PATH $APK_DEST_PATH

    curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_TOKEN" -X POST "https://api-cloud.browserstack.com/app-live/upload" -F "file=@$APK_DEST_PATH" -F "data={\"custom_id\": \"$BUILD_NAME\"}" --max-time 450

    echo "completed AppLive deployment (android)"
fi

if [[ $PLATFROM == "ios" ]]; then
    echo "starting AppLive deployment (ios)"

    IPA_SRC_PATH=$(pwd)/ios/build/Checkatrade.ipa
    IPA_DEST_PATH=$(pwd)/$BUILD_NAME.ipa

    cp $IPA_SRC_PATH $IPA_DEST_PATH

    curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_TOKEN" -X POST "https://api-cloud.browserstack.com/app-live/upload" -F "file=@$IPA_DEST_PATH" -F "data={\"custom_id\": \"$BUILD_NAME\"}" --max-time 450

    echo "completed AppLive deployment (ios)"
fi
