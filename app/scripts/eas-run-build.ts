#!/usr/bin/env tsx
/* eslint-disable no-console */

import { execSync } from 'node:child_process';
import * as fs from 'node:fs';
import * as path from 'node:path';

interface Build {
  id: string;
  createdAt: string;
  runtimeVersion: string;
  platform: 'ios' | 'android';
}

/**
 * Ensure the EAS CLI is installed and accessible
 */
function validateEasCli(): void {
  try {
    execSync('eas --version', { stdio: 'ignore' });
  } catch {
    throw new Error(
      'expo eas-cli not found. Please install it globally with `npm install -g eas-cli`',
    );
  }
}

/**
 * Ensure the user is authenticated with EAS CLI
 */
function validateAuthentication(): void {
  try {
    const user = execSync('eas whoami', { encoding: 'utf-8' }).trim();
    console.log(`👤 Logged in as ${user}`);
  } catch {
    throw new Error(
      'Not authenticated with EAS CLI. Please run `eas login --sso` to authenticate.\n' +
        'See https://checkatrade.atlassian.net/wiki/spaces/TRADE/pages/3560013825/Sign-up+or+sign-in+to+Expo for more details.',
    );
  }
}

/**
 * Read and return the runtimeVersion from package.json
 */
function getRuntimeVersion(): string {
  const pkgPath = path.resolve(process.cwd(), 'package.json');
  if (!fs.existsSync(pkgPath)) {
    throw new Error('package.json not found in current directory');
  }

  const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'));
  const version = pkg.version;
  if (!version) {
    throw new Error('runtimeVersion not defined in package.json');
  }

  return version;
}

/**
 * Fetch builds filtered by runtimeVersion and platform
 */
function fetchBuilds(runtimeVersion: string, platform: string): Build[] {
  const args = [
    'build:list',
    '--json',
    '--non-interactive',
    '--status',
    'finished',
    '--profile',
    platform === 'ios' ? 'dev-client-simulator' : 'dev-client',
    '--runtimeVersion',
    runtimeVersion,
    '--platform',
    platform,
  ];

  // limit to the most recent
  args.push('--limit', '1');

  const result = execSync(`eas ${args.join(' ')}`, { encoding: 'utf-8' });
  const builds: Build[] = JSON.parse(result);
  if (!Array.isArray(builds) || builds.length === 0) {
    throw new Error(`No builds found for runtimeVersion=${runtimeVersion}`);
  }

  return builds;
}

/**
 * Select the build with the latest createdAt timestamp
 */
function getLatestBuildId(builds: Build[]): string {
  return builds
    .slice()
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    )[0].id;
}

/**
 * Trigger a new run of the specified build
 */
function runBuild(buildId: string, platform: string): void {
  const args = ['build:run', '--id', buildId, '--platform', platform];
  console.log(`🚀 Running build ${buildId}...`);
  execSync(`eas ${args.join(' ')}`, { stdio: 'inherit' });
}

/**
 * Main entrypoint
 */
function main() {
  try {
    const [, , platformArg] = process.argv;
    if (!platformArg || !['ios', 'android'].includes(platformArg)) {
      console.error('❌ Platform must be either "ios" or "android"');
      console.error('Usage: tsx ./scripts/eas-run-build.ts <platform>');
      process.exit(1);
    }

    const platform = platformArg as 'ios' | 'android';

    console.log('🔧 Checking Expo EAS CLI installation...');
    validateEasCli();
    console.log('✅ EAS CLI is installed');

    console.log('🔐 Checking EAS authentication...');
    validateAuthentication();
    console.log('✅ Authenticated with EAS CLI');

    console.log('⚙️  Detecting runtimeVersion from package.json...');
    const runtimeVersion = getRuntimeVersion();
    console.log(`🔍 runtimeVersion=${runtimeVersion}`);

    console.log('⏳ Fetching builds...');
    const builds = fetchBuilds(runtimeVersion, platform);

    console.log('🔎 Selecting latest build...');
    const latestId = getLatestBuildId(builds);
    console.log(`✅ Latest build ID: ${latestId}`);

    runBuild(latestId, platform);
  } catch (error: unknown) {
    console.error(`❌ Error: ${(error as Error).message}`);
    process.exit(1);
  }
}

main();
