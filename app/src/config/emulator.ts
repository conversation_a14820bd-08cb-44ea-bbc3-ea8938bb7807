/* eslint-disable import/no-default-export */
import * as Linking from 'expo-linking';
import stagingConfig from './staging';
import { Config } from './types';
import { LOCAL_EMULATOR_HOSTNAME } from '../constants';

export default {
  ...stagingConfig,
  webAppUrl: Linking.createURL('/'),
  apiBaseUrl: `${LOCAL_EMULATOR_HOSTNAME}:28475`,
  environmentName: 'emulator',
  firebaseConfig: {
    apiKey: stagingConfig.firebaseConfig.apiKey,
    authDomain: 'trade-experience-local-emulator.firebaseapp.com',
    projectId: 'trade-experience-local-emulator',
    storageBucket: 'trade-experience-local-emulator.appspot.com',
    databaseURL: '',
    messagingSenderId: stagingConfig.firebaseConfig.messagingSenderId,
    appId: stagingConfig.firebaseConfig.appId,
  },
  useLocalFirebaseEmulator: true,
} satisfies Config;
