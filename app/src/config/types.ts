type FirebaseConfig = {
  apiKey: string | undefined;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  databaseURL: string;
  authDomain?: string;
  measurementId?: string;
};

export type DatadogConfig = {
  applicationId: string;
  clientToken: string;
};

type AuthServiceConfig = {
  baseUrl: string;
  clientId: string;
  redirectPath: string;
};

type PayByPhoneConfig = {
  adyenClientKey: string;
  environment: 'test' | 'live';
};

export interface Config {
  webAppUrl: string;
  checkatradeSiteUrl: string;
  apiBaseUrl: string;
  environmentName: 'staging' | 'production' | 'emulator';
  datadogConfig: DatadogConfig;
  firebaseConfig: FirebaseConfig;
  campaignsBaseUrl: string;
  vettingUrl: string;
  communityUrl: string;
  checkatradeIdentityConfig: AuthServiceConfig;
  memberDataApiBaseUrl: string;
  communicationsApiBaseUrl: string;
  zuoraBaseUrl: string;
  hostedPageId: string;
  DDHostedPageId: string;
  paymentGateway: string;
  DDPaymentGateway: string;
  useLocalFirebaseEmulator: boolean;
  kinesisConfig: {
    region: string;
    identityPoolId: string;
    streamName: string;
    environment: string;
  };
  mapBoxConfig: {
    apiKey: string | undefined;
    baseUrl: string;
  };
  brazeWebApiKey: string;
  capiUrl: string;
  accreditationLogoBaseUrl: string;
  tapEducationalVideoUrl: string;
  creditecPartnerUrl: string;
  thinkLoansPartnerUrl: string;
  thinkLoansMainUrl: string;
  subcontractorOnboardingUrl: string;
  catForBusinessApiBaseUrl: string;
  payByPhoneConfig: PayByPhoneConfig;
}
