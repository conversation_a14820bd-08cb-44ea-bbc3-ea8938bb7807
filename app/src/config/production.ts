/* eslint-disable import/no-default-export */
import { Platform } from 'react-native';
import { datadogConfig } from './datadog';
import type { Config } from './types';

export default {
  webAppUrl: 'https://membersapp.checkatrade.com',
  checkatradeSiteUrl: 'https://www.checkatrade.com',
  apiBaseUrl: 'https://trades.checkatrade.com',
  communicationsApiBaseUrl: 'https://communication.checkatrade.com',
  environmentName: 'production',
  datadogConfig,
  firebaseConfig: {
    apiKey: Platform.select({
      android: 'AIzaSyCPAQT_JQzpaLv_feSCxMq8WydzIIqKx30',
      ios: 'AIzaSyAvwSSaVibIacN9RBXuThQGa7VDW312sX0',
      web: 'AIzaSyBfmS7jjbO-J9sA0q1egHwFNmQB_ZPox0M',
    }),
    projectId: 'cat-trades',
    storageBucket: 'cat-trades.appspot.com',
    messagingSenderId: '263011460521',
    databaseURL: 'https://cat-trades.firebaseio.com',
    appId: Platform.select({
      web: '1:263011460521:web:c023e80fec99ff373750b8',
      ios: '1:263011460521:ios:d177d98813833c4b3750b8',
      android: '1:263011460521:android:f3f4be28816f84163750b8',
      default: '',
    }),
    ...Platform.select({
      web: {
        measurementId: 'G-S3N9DQ3LKK',
        authDomain: 'cat-trades.firebaseapp.com',
      },
      default: {},
    }),
  },
  campaignsBaseUrl: 'https://campaigns.checkatrade.com',
  vettingUrl: 'https://applicants.checkatrade.com/s/',
  communityUrl: 'https://community.checkatrade.com',
  checkatradeIdentityConfig: {
    baseUrl: 'https://api.checkatrade.com/v1/identity/auth/realms/trade',
    clientId: Platform.select({
      android: '0664f2b6-8d00-77ba-a700-f06c7a7fdc80',
      ios: '0664f2b6-ab99-7934-9a00-ff14496bad34',
      default: '0664f301-7c4c-7b0e-b900-a9da4226fa2f',
    }),
    redirectPath: Platform.select({
      native: 'login',
      default: '/login',
    }),
  },
  memberDataApiBaseUrl: 'https://memberdata-api.checkatrade.com',
  zuoraBaseUrl: 'https://eu.zuora.com',
  hostedPageId: '8a28a9d584c8c2c40184cd75588866d7',
  DDHostedPageId: '8a28fd5694dac8a40194db09ce6d1c2c',
  paymentGateway: 'ADYEN LIVE MOTO',
  DDPaymentGateway: 'GC LIVE',
  useLocalFirebaseEmulator: false,
  kinesisConfig: {
    region: 'eu-west-2',
    identityPoolId: 'eu-west-2:b33be2dd-64b5-4cdb-a54c-92799dd93a5e',
    streamName: 'cathex-data-datalake-trader-app',
    environment: 'prod',
  },
  mapBoxConfig: {
    apiKey: Platform.select({
      native:
        'pk.eyJ1IjoiY2hlY2thdHJhZGUiLCJhIjoiY2x5ZWwwcmRuMDIzZzJpczF0d3I4MGZvbyJ9.b8Se5zj6WtqYVm7OtHDCLg',
      web: 'pk.eyJ1IjoiY2hlY2thdHJhZGUiLCJhIjoiY2xrd2JtaHBzMDFpdDNwdXNsMDBmNnVxNSJ9.PD4aojqyxPUpzBdgwcTmMA',
    }),
    baseUrl: 'https://api.mapbox.com',
  },
  brazeWebApiKey: '0b9d384c-a31b-433b-a653-d2d8c5fc6c57',
  capiUrl: 'https://api.checkatrade.com',
  accreditationLogoBaseUrl:
    'https://storage.googleapis.com/frontend-prod-37565-accred-images/',
  tapEducationalVideoUrl:
    'https://storage.googleapis.com/cat-trades-education-apple-tap-to-pay/iphone-tap-to-pay-explainer-video.mp4',
  creditecPartnerUrl: 'https://checkatrade.creditec.co.uk',
  thinkLoansPartnerUrl: 'https://checkatrade.thinkbusinessloans.com',
  thinkLoansMainUrl: 'https://thinkbusinessloans.com',
  subcontractorOnboardingUrl:
    'https://member-onboarding.checkatrade.com/subcontractor-signup',
  catForBusinessApiBaseUrl:
    'https://cat4b-backend-prod-832860789255.europe-west1.run.app',
  payByPhoneConfig: {
    adyenClientKey: 'live_5SGN456FLJAZRMAQICYH7DTJ4YZW3YSQ',
    environment: 'live' as const,
  },
} satisfies Config;
