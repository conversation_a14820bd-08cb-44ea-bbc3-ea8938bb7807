/* eslint-disable import/no-default-export */
import { Platform } from 'react-native';
import { datadogConfig } from './datadog';
import type { Config } from './types';

export default {
  webAppUrl: 'https://membersapp-staging.checkatrade.com',
  checkatradeSiteUrl: 'https://frontend-staging.checkatrade.com',
  apiBaseUrl: 'https://trades-staging.checkatrade.net',
  communicationsApiBaseUrl: 'https://communication-stg.checkatrade.com',
  environmentName: 'staging',
  datadogConfig,
  firebaseConfig: {
    apiKey: Platform.select({
      android: 'AIzaSyBitFFElPOZgTrF3vMHFbx9PatKUUhnJNA',
      ios: 'AIzaSyDV31VtquHzZyQ1U4uDwHp4wLm3GBn_cl0',
      web: 'AIzaSyCuXSxe5M7C-IiIruoqd-hs7WtN87wsJyI',
    }),
    projectId: 'cat-trades-preview',
    storageBucket: 'cat-trades-preview.appspot.com',
    messagingSenderId: '92579811864',
    databaseURL: 'https://cat-trades-preview.firebaseio.com',
    appId: Platform.select({
      web: '1:92579811864:web:4b950e4d704de3685c296d',
      ios: '1:92579811864:ios:c43b184ebb843d065c296d',
      android: '1:92579811864:android:d83a0e8cd67768a95c296d',
      default: '',
    }),
    ...Platform.select({
      web: {
        measurementId: 'G-7HXX2RG4X3',
        authDomain: 'cat-trades-preview.firebaseapp.com',
      },
      default: {},
    }),
  },
  campaignsBaseUrl: 'https://campaigns-stg.checkatrade.net',
  vettingUrl: 'https://pfix-catdev.cs126.force.com/s/',
  communityUrl: 'https://community.checkatrade.com',
  checkatradeIdentityConfig: {
    baseUrl:
      'https://api.staging.checkatrade.com/v1/identity/auth/realms/trade',
    clientId: Platform.select({
      android: '0664f2a7-8004-7091-8e00-ce9b6311e3c0',
      ios: '0664f2ab-70fc-718c-9500-4c70154a14fc',
      default: '0664f308-a8e7-77fd-a600-f6bfa96764cd',
    }),
    redirectPath: Platform.select({
      native: 'login',
      default: '/login',
    }),
  },
  memberDataApiBaseUrl: 'https://memberdata-api-stg.checkatrade.com',
  zuoraBaseUrl: 'https://test.eu.zuora.com',
  hostedPageId: '8acce165848a0e9101848aa5207824ea',
  DDHostedPageId: '8acce27194b0ea310194b1ed076302e0',
  paymentGateway: 'Adyen Card Payments',
  DDPaymentGateway: 'Go Cardless Test Gateway',
  useLocalFirebaseEmulator: false,
  kinesisConfig: {
    region: 'eu-west-2',
    identityPoolId: 'eu-west-2:b33be2dd-64b5-4cdb-a54c-92799dd93a5e',
    streamName: 'cathex-data-datalake-trader-app',
    environment: 'staging',
  },
  mapBoxConfig: {
    apiKey: Platform.select({
      native:
        'pk.eyJ1IjoiY2hlY2thdHJhZGUiLCJhIjoiY2x5ZWwwcmRuMDIzZzJpczF0d3I4MGZvbyJ9.b8Se5zj6WtqYVm7OtHDCLg',
      web: 'pk.eyJ1IjoiY2hlY2thdHJhZGUiLCJhIjoiY2xrd2JtaHBzMDFpdDNwdXNsMDBmNnVxNSJ9.PD4aojqyxPUpzBdgwcTmMA',
    }),
    baseUrl: 'https://api.mapbox.com',
  },
  brazeWebApiKey: 'b3d80ed1-d77c-4c07-b978-bce205387623',
  capiUrl: 'https://api.staging.checkatrade.com',
  accreditationLogoBaseUrl:
    'https://storage.googleapis.com/frontend-stg-32330-accred-images/',
  tapEducationalVideoUrl:
    'https://storage.googleapis.com/cat-trades-preview-education-apple-tap-to-pay/iphone-tap-to-pay-explainer-video.mp4',
  creditecPartnerUrl: 'https://checkatrade.creditec.dev',
  thinkLoansPartnerUrl: 'https://checkatrade.test.thinkbusinessloans.com',
  thinkLoansMainUrl: 'https://thinkbusinessloans.com',
  subcontractorOnboardingUrl:
    'https://member-onboarding.checkatrade.dev/subcontractor-signup',
  catForBusinessApiBaseUrl:
    'https://cat4b-backend-staging-468113931306.europe-west1.run.app',
  payByPhoneConfig: {
    adyenClientKey: 'test_MCSEMR56M5GH7HGM7N32CZLPIMUH7BKE',
    environment: 'test' as const,
  },
} satisfies Config;
