import AsyncStorage from '@react-native-async-storage/async-storage';
import { differenceInMinutes } from 'date-fns';
import {
  getFirebaseUser,
  signInWithCustomToken,
} from 'src/services/firebase/auth';
import { STORAGE_KEY_FIREBASE_TOKEN_LAST_UPDATED } from 'src/constants';
import { createCustomFirebaseToken } from './createCustomAuthTokenV2';
import type { PartialFirebaseUser } from '../../service/AuthService';

const isFirebaseCustomTokenValid = async (): Promise<boolean> => {
  const lastUpdatedTimestampStr = await AsyncStorage.getItem(
    STORAGE_KEY_FIREBASE_TOKEN_LAST_UPDATED,
  );
  if (lastUpdatedTimestampStr == null) {
    return false;
  }

  const differenceMins = differenceInMinutes(
    new Date(),
    new Date(lastUpdatedTimestampStr),
  );

  const EXPIRY_THRESHOLD_MINS = 15;
  return differenceMins < EXPIRY_THRESHOLD_MINS;
};

const setLastUpdatedCustomTokenTimestamp = () =>
  AsyncStorage.setItem(
    STORAGE_KEY_FIREBASE_TOKEN_LAST_UPDATED,
    new Date().toISOString(),
  );

/**
 * Sign in to Firebase and return valid user information
 *
 * @param {string} companyId to generate a custom token for
 * @returns {User} Firebase User object
 */
export const signInToFirebaseAuth = async (
  companyId: number,
  accessToken: string,
): Promise<PartialFirebaseUser> => {
  if (await isFirebaseCustomTokenValid()) {
    // If user is signed in, return user information
    const currentUser = getFirebaseUser();
    if (currentUser?.uid) {
      const { uid } = currentUser;
      return { uid };
    }
  }

  // If no user is signed in, sign in with custom token
  const customToken = await createCustomFirebaseToken(companyId, accessToken);

  // Update custom token last updated timestamp
  await setLastUpdatedCustomTokenTimestamp();

  const { user } = await signInWithCustomToken(customToken);
  const { uid } = user;
  return { uid };
};
