import AsyncStorage from '@react-native-async-storage/async-storage';
import { subMinutes } from 'date-fns';
import mockAxios, { AxiosError, AxiosResponse } from 'axios';
import {
  getFirebaseUser,
  signInWithCustomToken,
} from 'src/services/firebase/auth';
import { signInToFirebaseAuth } from './signInToFirebaseAuth';

jest.mock('../decodeJWT');
jest.mock('src/services/firebase/auth', () => ({
  getFirebaseUser: jest.fn(),
  signInWithCustomToken: jest.fn(),
}));

describe('auth | utils | firebaseAuth | signInToFirebaseAuth', () => {
  const mockCompanyId = 1234;
  const stubAuthToken = 'stub-auth-token';
  beforeEach(() => {
    (getFirebaseUser as jest.Mock).mockReturnValue(null);
    (signInWithCustomToken as jest.Mock).mockResolvedValue({
      user: { uid: 'uid' },
    });
    (mockAxios.get as jest.Mock).mockResolvedValue({ data: 'token' });
  });

  afterEach(() => {
    jest.resetModules();
    jest.resetAllMocks();
  });

  test('Should sign in to Firebase and return valid user information for first time', async () => {
    // Act
    const result = await signInToFirebaseAuth(mockCompanyId, stubAuthToken);

    // Assert
    expect(result).toEqual({ uid: 'uid' });
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  test('Should refresh custom token if refresh timestamp is null', async () => {
    // Arrange
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
    (getFirebaseUser as jest.Mock).mockReturnValue({
      uid: 'uid',
    });

    // Act
    const result = await signInToFirebaseAuth(mockCompanyId, stubAuthToken);

    // Assert
    expect(result).toEqual({ uid: 'uid' });
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  test('Should return the current user if already signed in', async () => {
    // Arrange
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
      new Date().toISOString(),
    );
    (getFirebaseUser as jest.Mock).mockReturnValue({
      uid: 'uid',
    });

    // Act
    const result = await signInToFirebaseAuth(mockCompanyId, stubAuthToken);

    // Assert
    expect(result).toEqual({ uid: 'uid' });
    expect(mockAxios.get).not.toHaveBeenCalled();
  });

  test('Should return the current user if within the timeout threshold', async () => {
    // Arrange
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
      subMinutes(new Date(), 5).toISOString(),
    );
    (getFirebaseUser as jest.Mock).mockReturnValue({
      uid: 'uid',
    });

    // Act
    const result = await signInToFirebaseAuth(mockCompanyId, stubAuthToken);

    // Assert
    expect(result).toEqual({ uid: 'uid' });
    expect(mockAxios.get).not.toHaveBeenCalled();
  });

  test('Should refresh custom token if outside the timeout threshold', async () => {
    // Arrange
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
      subMinutes(new Date(), 30).toISOString(),
    );
    (getFirebaseUser as jest.Mock).mockReturnValue({
      uid: 'uid',
    });

    // Act
    const result = await signInToFirebaseAuth(mockCompanyId, stubAuthToken);

    // Assert
    expect(result).toEqual({ uid: 'uid' });
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  test('Should throw error if request fails', async () => {
    // Arrange
    const axiosError = new AxiosError(
      'API Error',
      '500',
      undefined,
      undefined,
      { status: 500 } as AxiosResponse,
    );
    (mockAxios.get as jest.Mock).mockRejectedValue(axiosError);

    // Act/Assert
    await expect(() =>
      signInToFirebaseAuth(mockCompanyId, stubAuthToken),
    ).rejects.toThrow();

    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });
});
