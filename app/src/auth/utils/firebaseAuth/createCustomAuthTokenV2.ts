import { tradeAppBff } from 'src/data/api/trade-app-bff';

/**
 * Takes an ID server originating access token and converts it to a
 * Firebase-compatible JWT
 */
export async function createCustomFirebaseToken(
  companyId: number,
  accessToken: string,
): Promise<string> {
  const response = await tradeAppBff.auth.getCustomFirebaseToken(
    companyId,
    accessToken,
  );
  return response.data;
}
