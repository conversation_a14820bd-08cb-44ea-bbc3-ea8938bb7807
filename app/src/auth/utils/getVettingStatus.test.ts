import { UserAccessType } from '../auth.types';
import { getVettingStatus } from './getVettingStatus';

describe('utilities | auth | getVettingStatus', () => {
  test.each([
    { staCode: 0, expected: UserAccessType.Active },
    { staCode: 1, expected: UserAccessType.Suspend },
    { staCode: 2, expected: UserAccessType.Active },
    { staCode: 3, expected: UserAccessType.Pending },
    { staCode: 4, expected: UserAccessType.Suspend },
  ])(
    'should return $expected for staCode: $staCode',
    ({ staCode, expected }) => {
      expect(getVettingStatus(staCode)).toEqual(expected);
    },
  );
});
