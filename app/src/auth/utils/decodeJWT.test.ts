import { decodeCheckatradeAuthJwt, getExpiryFromJWT } from './decodeJWT';

describe('Utilities | token | decodeJWT', () => {
  test('should return the expiry from a jwt token', () => {
    const accessToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.4Adcj3UFYzPUVaVF43FmMab6RlaQD8A9V8wFzzht-KQ';
    const got = getExpiryFromJWT(accessToken);
    expect(got).toBe(1516239022);
  });

  test('should return a decoded token for a jwt token', () => {
    const accessToken =
      'eyJ0eXAiOiJqd3QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjdNR2FxdTZhM0NuNXBETEdjczNXSlFzOXFDbnI1bXJLR3BYYXN2M0xFTTQifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Mtq79IplAnDdTI6MF083qiLCCYXDDdW2FlIz47LZ2wkDORo_gIlkl52l2ImTVg_8cdXGVmTnJpJhet5Fk2WW4bFS8ob6L_OneYrZqE8KlnuZ6nFmvGF3-t6zQaRq5YWdaI0emGa7sYOq89RZyK73y2F30c3D_5u02YeEpHbwqH77CL69_PW2f73amEedLGOj5mDvq1_V-cE6cJMh2SO3CvUmYcMRUKUXazp0Ja6IE9Sbw0ZfV49otDdfk4pKuFdxPRyEsSTxL-HuaFy6gnDWNC5gSCMt-KHgwChprCC_kpfcz5RNbU7-324mWJMcTlwGoYfoXmXnV8AnN_jCeIuVlA';

    const got = decodeCheckatradeAuthJwt(accessToken);

    expect(got).toEqual({
      email: '<EMAIL>',
      email_verified: true,
      exp: **********,
      iat: **********,
      uid: '066cdb76-d49e-70db-8120-56ad6097149f',
      accounts: [
        {
          company_id: '337953',
          sf_vetting_status: 'active',
          user_id: '5cfab0a3-f3c0-4f2c-894e-22a4a45a599c',
          vetting_status: 0,
        },
        {
          company_id: '1052396',
          sf_vetting_status: 'active',
          user_id: '8112a22e-bf81-4b56-83f4-6f3da4fc5eea',
          vetting_status: 0,
        },
        {
          company_id: '1052401',
          sf_vetting_status: 'active',
          user_id: '771bc73b-1d10-4b7c-ad8e-6a1a519b1eeb',
          vetting_status: 0,
        },
        {
          company_id: '1052402',
          sf_vetting_status: 'active',
          user_id: '6e7c546a-e6cf-457f-984c-463d4f42921f',
          vetting_status: 0,
        },
        {
          company_id: '1052403',
          sf_vetting_status: 'active',
          user_id: 'edb71827-e685-4983-80d1-2dbd97c461b8',
          vetting_status: 0,
        },
      ],
    });
  });
});
