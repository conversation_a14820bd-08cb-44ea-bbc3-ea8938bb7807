import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEY_AUTH_SERVICE_TYPE } from 'src/constants';
import { AuthServiceName } from '../../service/AuthService';

export async function getStoredAuthServiceType(): Promise<AuthServiceName> {
  const authService = await AsyncStorage.getItem(STORAGE_KEY_AUTH_SERVICE_TYPE);
  return authService === AuthServiceName.Checkatrade
    ? AuthServiceName.Checkatrade
    : AuthServiceName.Legacy;
}

export function setStoredAuthServiceType(
  authService: AuthServiceName,
): Promise<void> {
  return AsyncStorage.setItem(STORAGE_KEY_AUTH_SERVICE_TYPE, authService);
}
