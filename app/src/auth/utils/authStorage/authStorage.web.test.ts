import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  STORAGE_KEY_ACCESS_TOKEN,
  STORAGE_KEY_ID_TOKEN,
  STORAGE_KEY_REFRESH_TOKEN,
} from 'src/constants';
import {
  deleteStoredAccessToken,
  deleteStoredIdToken,
  deleteStoredRefreshToken,
  getStoredAccessToken,
  getStoredIdToken,
  getStoredRefreshToken,
  hasAccessToken,
  hasIdToken,
  hasRefreshToken,
  setStoredAccessToken,
  setStoredIdToken,
  setStoredRefreshToken,
} from './authStorage.web';

const MOCK_ACCESS_TOKEN = 'access-token';
const MOCK_REFRESH_TOKEN = 'refresh-token';
const MOCK_ID_TOKEN = 'id-token';

afterEach(async () => {
  jest.clearAllMocks();
  await AsyncStorage.clear();
});

describe('utilities | auth | authStorage | accessToken', () => {
  test('getStoredAccessToken returns the access token from AsyncStorage', async () => {
    // Arrange
    await AsyncStorage.setItem(STORAGE_KEY_ACCESS_TOKEN, MOCK_ACCESS_TOKEN);

    // Act
    const result = await getStoredAccessToken();

    // Assert
    expect(result).toEqual(MOCK_ACCESS_TOKEN);
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(STORAGE_KEY_ACCESS_TOKEN);
  });

  test('getStoredAccessToken returns null if not stored in AsyncStorage', async () => {
    // Act
    const result = await getStoredAccessToken();

    // Assert
    expect(result).toBeNull();
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(STORAGE_KEY_ACCESS_TOKEN);
  });

  test('hasAccessToken returns true if access token is stored in AsyncStorage', async () => {
    // Arrange
    await AsyncStorage.setItem(STORAGE_KEY_ACCESS_TOKEN, MOCK_ACCESS_TOKEN);

    // Act
    const result = await hasAccessToken();

    // Assert
    expect(result).toBeTruthy();
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(STORAGE_KEY_ACCESS_TOKEN);
  });

  test('hasAccessToken returns false if access token is not stored in AsyncStorage', async () => {
    // Act
    const result = await hasAccessToken();

    // Assert
    expect(result).toBeFalsy();
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(STORAGE_KEY_ACCESS_TOKEN);
  });

  test('setStoredAccessToken stores the access token in AsyncStorage', async () => {
    // Act
    await setStoredAccessToken(MOCK_ACCESS_TOKEN);

    // Assert
    expect(AsyncStorage.setItem).toHaveBeenCalledWith(
      STORAGE_KEY_ACCESS_TOKEN,
      MOCK_ACCESS_TOKEN,
    );
  });

  test('deleteStoredAccessToken removes the access token from AsyncStorage', async () => {
    // Arrange
    await AsyncStorage.setItem(STORAGE_KEY_ACCESS_TOKEN, MOCK_ACCESS_TOKEN);

    // Act
    await deleteStoredAccessToken();

    // Assert
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith(
      STORAGE_KEY_ACCESS_TOKEN,
    );
  });
});

describe('utilities | auth | authStorage | refreshToken', () => {
  test('getStoredRefreshToken returns the refresh token from AsyncStorage', async () => {
    // Arrange
    await AsyncStorage.setItem(STORAGE_KEY_REFRESH_TOKEN, MOCK_REFRESH_TOKEN);

    // Act
    const result = await getStoredRefreshToken();

    // Assert
    expect(result).toEqual(MOCK_REFRESH_TOKEN);
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(
      STORAGE_KEY_REFRESH_TOKEN,
    );
  });

  test('getStoredRefreshToken returns null if not stored in AsyncStorage', async () => {
    // Act
    const result = await getStoredRefreshToken();

    // Assert
    expect(result).toBeNull();
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(
      STORAGE_KEY_REFRESH_TOKEN,
    );
  });

  test('hasRefreshToken returns true if refresh token is stored in AsyncStorage', async () => {
    // Arrange
    await AsyncStorage.setItem(STORAGE_KEY_REFRESH_TOKEN, MOCK_REFRESH_TOKEN);

    // Act
    const result = await hasRefreshToken();

    // Assert
    expect(result).toBeTruthy();
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(
      STORAGE_KEY_REFRESH_TOKEN,
    );
  });

  test('hasRefreshToken returns false if refresh token is not stored in AsyncStorage', async () => {
    // Act
    const result = await hasRefreshToken();

    // Assert
    expect(result).toBeFalsy();
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(
      STORAGE_KEY_REFRESH_TOKEN,
    );
  });

  test('setStoredRefreshToken stores the refresh token in AsyncStorage', async () => {
    // Act
    await setStoredRefreshToken(MOCK_REFRESH_TOKEN);

    // Assert
    expect(AsyncStorage.setItem).toHaveBeenCalledWith(
      STORAGE_KEY_REFRESH_TOKEN,
      MOCK_REFRESH_TOKEN,
    );
  });

  test('deleteStoredRefreshToken removes the refresh token from AsyncStorage', async () => {
    // Arrange
    await AsyncStorage.setItem(STORAGE_KEY_REFRESH_TOKEN, MOCK_REFRESH_TOKEN);

    // Act
    await deleteStoredRefreshToken();

    // Assert
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith(
      STORAGE_KEY_REFRESH_TOKEN,
    );
  });
});

describe('utilities | auth | authStorage | idToken', () => {
  test('getStoredIdToken returns the id token from AsyncStorage', async () => {
    // Arrange
    await AsyncStorage.setItem(STORAGE_KEY_ID_TOKEN, MOCK_ID_TOKEN);

    // Act
    const result = await getStoredIdToken();

    // Assert
    expect(result).toEqual(MOCK_ID_TOKEN);
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(STORAGE_KEY_ID_TOKEN);
  });

  test('getStoredIdToken returns null if not stored in AsyncStorage', async () => {
    // Act
    const result = await getStoredIdToken();

    // Assert
    expect(result).toBeNull();
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(STORAGE_KEY_ID_TOKEN);
  });

  test('hasIdToken returns true if id token is stored in AsyncStorage', async () => {
    // Arrange
    await AsyncStorage.setItem(STORAGE_KEY_ID_TOKEN, MOCK_ID_TOKEN);

    // Act
    const result = await hasIdToken();

    // Assert
    expect(result).toBeTruthy();
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(STORAGE_KEY_ID_TOKEN);
  });

  test('hasIdToken returns false if id token is not stored in AsyncStorage', async () => {
    // Act
    const result = await hasIdToken();

    // Assert
    expect(result).toBeFalsy();
    expect(AsyncStorage.getItem).toHaveBeenCalledWith(STORAGE_KEY_ID_TOKEN);
  });

  test('setStoredIdToken stores the id token in AsyncStorage', async () => {
    // Act
    await setStoredIdToken(MOCK_ID_TOKEN);

    // Assert
    expect(AsyncStorage.setItem).toHaveBeenCalledWith(
      STORAGE_KEY_ID_TOKEN,
      MOCK_ID_TOKEN,
    );
  });

  test('deleteStoredIdToken removes the id token from AsyncStorage', async () => {
    // Arrange
    await AsyncStorage.setItem(STORAGE_KEY_ID_TOKEN, MOCK_ID_TOKEN);

    // Act
    await deleteStoredIdToken();

    // Assert
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith(STORAGE_KEY_ID_TOKEN);
  });
});
