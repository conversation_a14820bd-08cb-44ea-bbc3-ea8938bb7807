import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import {
  STORAGE_KEY_ACCESS_TOKEN,
  STORAGE_KEY_REFRESH_TOKEN,
} from 'src/constants';
import {
  SECURE_KEY_ACCESS_TOKEN,
  hasAccessToken,
  getStoredAccessToken,
  setStoredAccessToken,
  deleteStoredAccessToken,
  getStoredRefreshToken,
  SECURE_KEY_REFRESH_TOKEN,
  hasRefreshToken,
  setStoredRefreshToken,
  deleteStoredRefreshToken,
  getStoredIdToken,
  hasIdToken,
  setStoredIdToken,
  SECURE_KEY_ID_TOKEN,
  deleteStoredIdToken,
} from './authStorage';

jest.mock('expo-secure-store', () => {
  const secureStore = jest.requireActual('expo-secure-store');
  return {
    ...secureStore,
    isAvailableAsync: () => Promise.resolve(true),
    getItemAsync: jest.fn(),
    setItemAsync: jest.fn(),
    deleteItemAsync: jest.fn(),
  };
});

const MOCK_ACCESS_TOKEN = 'access-token';
const MOCK_REFRESH_TOKEN = 'refresh-token';
const MOCK_ID_TOKEN = 'id-token';

const secureStoreOptions = {
  keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY,
};

afterEach(async () => {
  jest.resetAllMocks();
});

describe('utilities | auth | authStorage.native | accessToken', () => {
  describe('getStoredAccessToken', () => {
    test('getStoredAccessToken migrates the stored access token from AsyncStorage to SecureStore', async () => {
      // Arrange
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(MOCK_ACCESS_TOKEN);

      // Act
      const result = await getStoredAccessToken();

      // Assert
      expect(result).toEqual(MOCK_ACCESS_TOKEN);

      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_ACCESS_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        STORAGE_KEY_ACCESS_TOKEN,
      );
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith(
        STORAGE_KEY_ACCESS_TOKEN,
      );
    });

    test('getStoredAccessToken returns the access token from SecureStore if stored in SecureStore', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(
        MOCK_ACCESS_TOKEN,
      );

      // Act
      const result = await getStoredAccessToken();

      // Assert
      expect(result).toEqual(MOCK_ACCESS_TOKEN);
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_ACCESS_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).not.toHaveBeenCalled();
    });

    test('getStoredAccessToken returns null if no access token is stored', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await getStoredAccessToken();

      // Assert
      expect(result).toBeNull();
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_ACCESS_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        STORAGE_KEY_ACCESS_TOKEN,
      );
    });

    test('getStoredAccessToken returns async-storage value if an error occurs', async () => {
      // Arrange
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(MOCK_ACCESS_TOKEN);
      (SecureStore.getItemAsync as jest.Mock).mockRejectedValue(
        new Error('Mock Error'),
      );

      // Act
      const result = await getStoredAccessToken();

      // Assert
      expect(result).toEqual(MOCK_ACCESS_TOKEN);
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_ACCESS_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        STORAGE_KEY_ACCESS_TOKEN,
      );
    });
  });

  describe('hasAccessToken', () => {
    test('hasAccessToken returns true if an access token is stored', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(
        MOCK_ACCESS_TOKEN,
      );

      // Act
      const result = await hasAccessToken();

      // Assert
      expect(result).toEqual(true);
    });

    test('hasAccessToken returns false if no access token is stored', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await hasAccessToken();

      // Assert
      expect(result).toEqual(false);
    });

    test('hasAccessToken returns true if async-storage contains token, but getItemAsync returns error', async () => {
      // Arrange
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(MOCK_ACCESS_TOKEN);
      (SecureStore.getItemAsync as jest.Mock).mockRejectedValue(
        new Error('Mock Error'),
      );

      // Act
      const result = await hasAccessToken();

      // Assert
      expect(result).toEqual(true);
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_ACCESS_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        STORAGE_KEY_ACCESS_TOKEN,
      );
    });
  });

  test('setStoredAccessToken stores the access token in SecureStore', async () => {
    // Act
    await setStoredAccessToken(MOCK_ACCESS_TOKEN);

    // Assert
    expect(SecureStore.setItemAsync).toHaveBeenCalledWith(
      SECURE_KEY_ACCESS_TOKEN,
      MOCK_ACCESS_TOKEN,
      secureStoreOptions,
    );
  });

  test('deleteStoredAccessToken deletes the access token from SecureStore', async () => {
    // Act
    await deleteStoredAccessToken();

    // Assert
    expect(SecureStore.deleteItemAsync).toHaveBeenCalledWith(
      SECURE_KEY_ACCESS_TOKEN,
      secureStoreOptions,
    );
  });
});

describe('utilities | auth | authStorage.native | refreshToken', () => {
  describe('getStoredRefreshToken', () => {
    test('getStoredRefreshToken migrates the stored refresh token from AsyncStorage to SecureStore', async () => {
      // Arrange
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(MOCK_REFRESH_TOKEN);

      // Act
      const result = await getStoredRefreshToken();

      // Assert
      expect(result).toEqual(MOCK_REFRESH_TOKEN);

      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_REFRESH_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        STORAGE_KEY_REFRESH_TOKEN,
      );
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith(
        STORAGE_KEY_REFRESH_TOKEN,
      );
    });

    test('getStoredRefreshToken returns the refresh token from SecureStore if stored in SecureStore', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(
        MOCK_REFRESH_TOKEN,
      );

      // Act
      const result = await getStoredRefreshToken();

      // Assert
      expect(result).toEqual(MOCK_REFRESH_TOKEN);
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_REFRESH_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).not.toHaveBeenCalled();
    });

    test('getStoredRefreshToken returns null if no refresh token is stored', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await getStoredRefreshToken();

      // Assert
      expect(result).toBeNull();
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_REFRESH_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        STORAGE_KEY_REFRESH_TOKEN,
      );
    });

    test('getStoredRefreshToken returns async-storage value if an error occurs', async () => {
      // Arrange
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(MOCK_REFRESH_TOKEN);
      (SecureStore.getItemAsync as jest.Mock).mockRejectedValue(
        new Error('Mock Error'),
      );

      // Act
      const result = await getStoredRefreshToken();

      // Assert
      expect(result).toEqual(MOCK_REFRESH_TOKEN);
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_REFRESH_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        STORAGE_KEY_REFRESH_TOKEN,
      );
    });
  });

  describe('hasRefreshToken', () => {
    test('hasRefreshToken returns true if a refresh token is stored', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(
        MOCK_REFRESH_TOKEN,
      );

      // Act
      const result = await hasRefreshToken();

      // Assert
      expect(result).toEqual(true);
    });

    test('hasRefreshToken returns false if no refresh token is stored', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await hasRefreshToken();

      // Assert
      expect(result).toEqual(false);
    });

    test('hasRefreshToken returns true if async-storage contains token, but getItemAsync (secure-store) returns error', async () => {
      // Arrange
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(MOCK_REFRESH_TOKEN);
      (SecureStore.getItemAsync as jest.Mock).mockRejectedValue(
        new Error('Mock Error'),
      );

      // Act
      const result = await hasRefreshToken();

      // Assert
      expect(result).toEqual(true);
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_REFRESH_TOKEN,
        secureStoreOptions,
      );

      expect(AsyncStorage.getItem).toHaveBeenCalledWith(
        STORAGE_KEY_REFRESH_TOKEN,
      );
    });
  });

  test('setStoredRefreshToken stores the refresh token in SecureStore', async () => {
    // Act
    await setStoredRefreshToken(MOCK_REFRESH_TOKEN);

    // Assert
    expect(SecureStore.setItemAsync).toHaveBeenCalledWith(
      SECURE_KEY_REFRESH_TOKEN,
      MOCK_REFRESH_TOKEN,
      secureStoreOptions,
    );
  });

  test('deleteStoredRefreshToken deletes the refresh token from SecureStore', async () => {
    // Act
    await deleteStoredRefreshToken();

    // Assert
    expect(SecureStore.deleteItemAsync).toHaveBeenCalledWith(
      SECURE_KEY_REFRESH_TOKEN,
      secureStoreOptions,
    );
  });
});

describe('utilities | auth | authStorage.native | idToken', () => {
  describe('getStoredIdToken', () => {
    test('returns the id token from SecureStore if stored in SecureStore', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(MOCK_ID_TOKEN);

      // Act
      const result = await getStoredIdToken();

      // Assert
      expect(result).toEqual(MOCK_ID_TOKEN);
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_ID_TOKEN,
        secureStoreOptions,
      );
    });

    test('returns null if no id token is stored', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await getStoredIdToken();

      // Assert
      expect(result).toBeNull();
      expect(SecureStore.getItemAsync).toHaveBeenCalledWith(
        SECURE_KEY_ID_TOKEN,
        secureStoreOptions,
      );
    });
  });

  describe('hasIdToken', () => {
    test('returns true if a id token is stored', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(MOCK_ID_TOKEN);

      // Act
      const result = await hasIdToken();

      // Assert
      expect(result).toEqual(true);
    });

    test('returns false if no id token is stored', async () => {
      // Arrange
      (SecureStore.getItemAsync as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await hasIdToken();

      // Assert
      expect(result).toEqual(false);
    });
  });

  test('setStoredIdToken stores the id token in SecureStore', async () => {
    // Act
    await setStoredIdToken(MOCK_ID_TOKEN);

    // Assert
    expect(SecureStore.setItemAsync).toHaveBeenCalledWith(
      SECURE_KEY_ID_TOKEN,
      MOCK_ID_TOKEN,
      secureStoreOptions,
    );
    expect(AsyncStorage.setItem).not.toHaveBeenCalled();
  });

  test('deleteStoredIdToken deletes the id token from SecureStore', async () => {
    // Act
    await deleteStoredIdToken();

    // Assert
    expect(SecureStore.deleteItemAsync).toHaveBeenCalledWith(
      SECURE_KEY_ID_TOKEN,
      secureStoreOptions,
    );
  });
});
