/** iOS and Android only file */
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import type { SecureStoreOptions } from 'expo-secure-store';
import {
  STORAGE_KEY_ACCESS_TOKEN as LEGACY_KEY_ACCESS_TOKEN,
  STORAGE_KEY_REFRESH_TOKEN as LEGACY_KEY_REFRESH_TOKEN,
} from 'src/constants';
import { captureException } from 'src/services/datadog';
import { isError } from 'src/utilities/errors/isError';

// #region constants
/**
 * @private - only used for tests
 */
export const SECURE_KEY_ACCESS_TOKEN = 'cat_trade_access_token';
/**
 * @private - only used for tests
 */
export const SECURE_KEY_REFRESH_TOKEN = 'cat_trade_refresh_token';
/**
 * @private - only used for tests
 */
export const SECURE_KEY_ID_TOKEN = 'cat_trade_id_token';

const secureStoreOptions: SecureStoreOptions = {
  /**
   * iOS only
   * The data in the keychain item cannot be accessed after a restart until the device has been
   * unlocked once by the user.
   * The entry is not migrated to a new device when restoring from a backup.
   */
  keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY,
};
// #endregion

// #region common functions
const getSecureItemAsync = async (secureStoreKey: string) =>
  SecureStore.getItemAsync(secureStoreKey, secureStoreOptions);

const setSecureItemAsync = async (secureStoreKey: string, item: string) =>
  SecureStore.setItemAsync(secureStoreKey, item, secureStoreOptions);

const deleteSecureItemAsync = async (secureStoreKey: string) =>
  SecureStore.deleteItemAsync(secureStoreKey, secureStoreOptions);

export async function fetchAndMigrateStoredItem(
  secureStoreKey: string,
  legacyKey: string,
): Promise<string | null> {
  try {
    const secureValue = await getSecureItemAsync(secureStoreKey);
    if (secureValue) {
      return secureValue;
    }
  } catch (error) {
    if (isError(error)) {
      captureException(
        new Error(
          `fetchAndMigrateStoredItem getItemAsync failed: ${error?.message}`,
        ),
      );
    }
  }

  const asyncValue = await AsyncStorage.getItem(legacyKey);
  if (asyncValue) {
    try {
      await setSecureItemAsync(secureStoreKey, asyncValue);
      await AsyncStorage.removeItem(legacyKey);
    } catch (error) {
      if (isError(error)) {
        captureException(
          new Error(
            `fetchAndMigrateStoredItem setItemAsync failed: ${error?.message}`,
          ),
        );
      }
    }

    return asyncValue;
  }

  return null;
}

// #endregion

// #region Access Token
export function getStoredAccessToken(): Promise<string | null> {
  return fetchAndMigrateStoredItem(
    SECURE_KEY_ACCESS_TOKEN,
    LEGACY_KEY_ACCESS_TOKEN,
  );
}

export async function hasAccessToken(): Promise<boolean> {
  return Boolean(await getStoredAccessToken());
}

export function setStoredAccessToken(accessToken: string): Promise<void> {
  return setSecureItemAsync(SECURE_KEY_ACCESS_TOKEN, accessToken);
}

export function deleteStoredAccessToken(): Promise<void> {
  return deleteSecureItemAsync(SECURE_KEY_ACCESS_TOKEN);
}
// #endregion

// #region Refresh Token
export function getStoredRefreshToken(): Promise<string | null> {
  return fetchAndMigrateStoredItem(
    SECURE_KEY_REFRESH_TOKEN,
    LEGACY_KEY_REFRESH_TOKEN,
  );
}

export async function hasRefreshToken(): Promise<boolean> {
  return Boolean(await getStoredRefreshToken());
}

export function setStoredRefreshToken(refreshToken: string): Promise<void> {
  return setSecureItemAsync(SECURE_KEY_REFRESH_TOKEN, refreshToken);
}

export function deleteStoredRefreshToken(): Promise<void> {
  return deleteSecureItemAsync(SECURE_KEY_REFRESH_TOKEN);
}
// #endregion

// #region Id Token
export function getStoredIdToken(): Promise<string | null> {
  return getSecureItemAsync(SECURE_KEY_ID_TOKEN);
}

export async function hasIdToken(): Promise<boolean> {
  return Boolean(await getStoredIdToken());
}

export function setStoredIdToken(refreshToken: string): Promise<void> {
  return setSecureItemAsync(SECURE_KEY_ID_TOKEN, refreshToken);
}

export function deleteStoredIdToken(): Promise<void> {
  return deleteSecureItemAsync(SECURE_KEY_ID_TOKEN);
}
// #endregion
