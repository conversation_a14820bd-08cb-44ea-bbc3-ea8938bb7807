/** WEB only file */
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  STORAGE_KEY_ACCESS_TOKEN,
  STORAGE_KEY_ID_TOKEN,
  STORAGE_KEY_REFRESH_TOKEN,
} from 'src/constants';

// #region Access Token
export function getStoredAccessToken(): Promise<string | null> {
  return AsyncStorage.getItem(STORAGE_KEY_ACCESS_TOKEN);
}

export async function hasAccessToken(): Promise<boolean> {
  return Boolean(await getStoredAccessToken());
}

export function setStoredAccessToken(accessToken: string): Promise<void> {
  return AsyncStorage.setItem(STORAGE_KEY_ACCESS_TOKEN, accessToken);
}

export function deleteStoredAccessToken(): Promise<void> {
  return AsyncStorage.removeItem(STORAGE_KEY_ACCESS_TOKEN);
}
// #endregion

// #region Refresh Token
export function getStoredRefreshToken(): Promise<string | null> {
  return AsyncStorage.getItem(STORAGE_KEY_REFRESH_TOKEN);
}

export function setStoredRefreshToken(refreshToken: string): Promise<void> {
  return AsyncStorage.setItem(STORAGE_KEY_REFRESH_TOKEN, refreshToken);
}

export async function hasRefreshToken(): Promise<boolean> {
  return Boolean(await getStoredRefreshToken());
}

export function deleteStoredRefreshToken(): Promise<void> {
  return AsyncStorage.removeItem(STORAGE_KEY_REFRESH_TOKEN);
}
// #endregion

// #region Id Token
export function getStoredIdToken(): Promise<string | null> {
  return AsyncStorage.getItem(STORAGE_KEY_ID_TOKEN);
}

export async function hasIdToken(): Promise<boolean> {
  return Boolean(await getStoredIdToken());
}

export function setStoredIdToken(refreshToken: string): Promise<void> {
  return AsyncStorage.setItem(STORAGE_KEY_ID_TOKEN, refreshToken);
}

export function deleteStoredIdToken(): Promise<void> {
  return AsyncStorage.removeItem(STORAGE_KEY_ID_TOKEN);
}
// #endregion
