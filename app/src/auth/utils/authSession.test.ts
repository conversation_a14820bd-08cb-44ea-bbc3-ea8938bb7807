import {
  fetchDiscoveryAsync,
  refreshAsync,
  revokeAsync,
} from 'expo-auth-session';
import type { IdentityServerConfig } from '../auth.types';
import {
  deleteStoredAuthTokens,
  getValidAccessToken,
  revokeAuthToken,
} from './authSession';
import {
  deleteStoredAccessToken,
  deleteStoredRefreshToken,
  getStoredAccessToken,
  getStoredIdToken,
  getStoredRefreshToken,
  hasAccessToken,
  hasRefreshToken,
  getStoredAuthServiceType,
} from './authStorage';
import { getExpiryFromJWT } from './decodeJWT';
import { AuthServiceName } from '../service';

jest.mock('./decodeJWT');
jest.mock('./authStorage');
jest.mock('expo-auth-session', () => {
  const actual = jest.requireActual('expo-auth-session');
  return {
    ...actual,
    fetchDiscoveryAsync: jest.fn(),
    refreshAsync: jest.fn(),
    revokeAsync: jest.fn(),
  };
});

describe('auth | utils | authSession', () => {
  const idSvrConfig: IdentityServerConfig = {
    discoveryUrl: 'https://example.com',
    clientId: 'test',
    scopes: ['test'],
    authServiceName: AuthServiceName.Checkatrade,
  };

  const STORED_ACCESS_TOKEN = 'originalAccessToken';
  const STORED_REFRESH_TOKEN = 'originalRefreshToken';
  const STORED_ID_TOKEN = 'originalIdToken';
  const NEW_ACCESS_TOKEN = 'newAccessToken';
  const NEW_REFRESH_TOKEN = 'newRefreshToken';
  const NEW_ID_TOKEN = 'newIdToken';

  beforeEach(() => {
    (getExpiryFromJWT as jest.Mock).mockReturnValue(9999999999);
    (refreshAsync as jest.Mock).mockResolvedValue({
      accessToken: NEW_ACCESS_TOKEN,
      refreshToken: NEW_REFRESH_TOKEN,
      idToken: NEW_ID_TOKEN,
    });
    (hasRefreshToken as jest.Mock).mockResolvedValue(false);
    (hasAccessToken as jest.Mock).mockResolvedValue(false);
    (getStoredAccessToken as jest.Mock).mockResolvedValue(STORED_ACCESS_TOKEN);
    (getStoredRefreshToken as jest.Mock).mockResolvedValue(
      STORED_REFRESH_TOKEN,
    );
    (getStoredIdToken as jest.Mock).mockResolvedValue(STORED_ID_TOKEN);
    (getStoredAuthServiceType as jest.Mock).mockResolvedValue(
      AuthServiceName.Checkatrade,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('getValidAccessToken', () => {
    test('returns accessToken after refreshing', async () => {
      // Arrange
      (getExpiryFromJWT as jest.Mock).mockReturnValue(1704067200);
      (hasAccessToken as jest.Mock).mockResolvedValue(true);
      (hasRefreshToken as jest.Mock).mockResolvedValue(true);

      // Act
      const result = await getValidAccessToken(idSvrConfig);

      // Assert
      expect(result).toEqual({
        hadAccessToken: true,
        hadRefreshToken: true,
        accessToken: NEW_ACCESS_TOKEN,
        idToken: NEW_ID_TOKEN,
      });
    });

    test('returns stored accessToken after checking expiry', async () => {
      // Arrange
      (getExpiryFromJWT as jest.Mock).mockReturnValue(9999999999);
      (hasAccessToken as jest.Mock).mockResolvedValue(true);
      (hasRefreshToken as jest.Mock).mockResolvedValue(true);

      // Act
      const result = await getValidAccessToken(idSvrConfig);

      // Assert
      expect(result).toEqual({
        hadAccessToken: true,
        hadRefreshToken: true,
        accessToken: STORED_ACCESS_TOKEN,
        idToken: STORED_ID_TOKEN,
      });
    });

    test('returns new accessToken when switching auth service', async () => {
      // Arrange
      (getExpiryFromJWT as jest.Mock).mockReturnValue(1704067200);
      (hasAccessToken as jest.Mock).mockResolvedValue(true);
      (hasRefreshToken as jest.Mock).mockResolvedValue(true);
      (getStoredAuthServiceType as jest.Mock).mockResolvedValue(
        AuthServiceName.Legacy,
      );

      // Act
      const result = await getValidAccessToken(idSvrConfig);

      // Assert
      expect(result).toEqual({
        hadAccessToken: true,
        hadRefreshToken: true,
        accessToken: NEW_ACCESS_TOKEN,
        idToken: NEW_ID_TOKEN,
      });
    });

    test('does NOT return accessToken if none stored', async () => {
      // Arrange
      (hasAccessToken as jest.Mock).mockResolvedValue(false);
      (hasRefreshToken as jest.Mock).mockResolvedValue(false);
      (getStoredAccessToken as jest.Mock).mockResolvedValue(null);
      (getStoredRefreshToken as jest.Mock).mockResolvedValue(null);
      (getStoredIdToken as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await getValidAccessToken(idSvrConfig);

      // Assert
      expect(result).toEqual({
        hadAccessToken: false,
        hadRefreshToken: false,
        accessToken: null,
        idToken: null,
      });
    });

    test('returns stored valid accessToken if auth discovery fails', async () => {
      // Arrange
      (hasAccessToken as jest.Mock).mockResolvedValue(true);
      (hasRefreshToken as jest.Mock).mockResolvedValue(true);
      (fetchDiscoveryAsync as jest.Mock).mockRejectedValue(
        new Error('TEST: Discovery failed'),
      );

      // Act
      const result = await getValidAccessToken(idSvrConfig);

      // Assert
      expect(result).toEqual({
        hadAccessToken: true,
        hadRefreshToken: true,
        accessToken: STORED_ACCESS_TOKEN,
        idToken: STORED_ID_TOKEN,
      });
    });

    test('returns stored valid accessToken if refresh fails', async () => {
      // Arrange
      (hasAccessToken as jest.Mock).mockResolvedValue(true);
      (hasRefreshToken as jest.Mock).mockResolvedValue(true);
      (refreshAsync as jest.Mock).mockRejectedValue(
        new Error('TEST: Refresh failed'),
      );

      // Act
      const result = await getValidAccessToken(idSvrConfig);

      // Assert
      expect(result).toEqual({
        hadAccessToken: true,
        hadRefreshToken: true,
        accessToken: STORED_ACCESS_TOKEN,
        idToken: STORED_ID_TOKEN,
      });
    });

    test('returns valid accessToken (without refreshToken)', async () => {
      // Arrange
      (refreshAsync as jest.Mock).mockResolvedValue({
        accessToken: NEW_ACCESS_TOKEN,
        refreshToken: null,
      });
      (hasAccessToken as jest.Mock).mockResolvedValue(true);
      (hasRefreshToken as jest.Mock).mockResolvedValue(false);
      (getStoredRefreshToken as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await getValidAccessToken(idSvrConfig);

      // Assert
      expect(result).toEqual({
        hadAccessToken: true,
        hadRefreshToken: false,
        accessToken: STORED_ACCESS_TOKEN,
        idToken: STORED_ID_TOKEN,
      });
    });

    test('does NOT return invalid accessToken (without refreshToken)', async () => {
      // Arrange
      (refreshAsync as jest.Mock).mockResolvedValue({
        accessToken: NEW_ACCESS_TOKEN,
        refreshToken: null,
      });
      (hasAccessToken as jest.Mock).mockResolvedValue(true);
      (hasRefreshToken as jest.Mock).mockResolvedValue(false);
      (getStoredRefreshToken as jest.Mock).mockResolvedValue(null);
      (getStoredIdToken as jest.Mock).mockResolvedValue(null);
      (getExpiryFromJWT as jest.Mock).mockReturnValue(1900000);

      // Act
      const result = await getValidAccessToken(idSvrConfig);

      // Assert
      expect(result).toEqual({
        hadAccessToken: true,
        hadRefreshToken: false,
        accessToken: null,
        idToken: null,
      });
    });
  });

  describe('revokeAuthToken', () => {
    test('token is revoked', async () => {
      // Act
      await revokeAuthToken(idSvrConfig);

      // Assert
      expect(revokeAsync).toHaveBeenCalledTimes(1);
    });

    test('token is NOT revoked if no stored accessToken', async () => {
      // Arrange
      (getStoredAccessToken as jest.Mock).mockResolvedValue(null);

      // Act
      await revokeAuthToken(idSvrConfig);

      // Assert
      expect(revokeAsync).not.toHaveBeenCalled();
    });

    test('token is NOT revoked if revoke fails', async () => {
      // Arrange
      const revokeError = new Error('TEST: Revoke failed');
      (revokeAsync as jest.Mock).mockRejectedValue(revokeError);

      // Act/Assert
      await expect(() => revokeAuthToken(idSvrConfig)).rejects.toThrow(
        revokeError,
      );
    });
  });

  describe('deleteStoredAuthTokens', () => {
    test('stored tokens are deleted', async () => {
      // Act
      await deleteStoredAuthTokens();

      // Assert
      expect(deleteStoredAccessToken).toHaveBeenCalledTimes(1);
      expect(deleteStoredRefreshToken).toHaveBeenCalledTimes(1);
    });

    test('stored tokens are NOT deleted if delete fails', async () => {
      // Arrange
      const deleteError = new Error('TEST: Delete failed');
      (deleteStoredAccessToken as jest.Mock).mockRejectedValue(deleteError);

      // Act/Assert
      await expect(() => deleteStoredAuthTokens()).rejects.toThrow(deleteError);
      expect(deleteStoredRefreshToken).not.toHaveBeenCalled();
    });
  });
});
