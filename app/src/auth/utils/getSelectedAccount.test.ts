import { CompanyAccount } from 'src/context/UserContext';
import { UserAccessType } from '../auth.types';
import {
  getActiveAccounts,
  getActiveOrPendingAccounts,
  getSelectedAccount,
} from './getSelectedAccount';

const activeAccountOne: CompanyAccount = {
  companyId: 1,
  vettingStatus: UserAccessType.Active,
  userId: '1',
  isActive: true,
};

const pendingAccountTwo: CompanyAccount = {
  companyId: 2,
  vettingStatus: UserAccessType.Pending,
  userId: '2',
  isActive: false,
};

const suspendAccountThree: CompanyAccount = {
  companyId: 3,
  vettingStatus: UserAccessType.Suspend,
  userId: '3',
  isActive: false,
};

describe('auth | utils | getSelectedAccount', () => {
  test('should return the selected account', () => {
    // Arrange
    const accounts = [activeAccountOne, pendingAccountTwo];

    // Act
    const selectedCompanyId = 1;
    const result = getSelectedAccount(accounts, selectedCompanyId);

    // Assert
    expect(result).toEqual(activeAccountOne);
  });

  test('should return the first account if selected is null', () => {
    // Arrange
    const accounts = [activeAccountOne];

    // Act
    const result = getSelectedAccount(accounts, null);

    // Assert
    expect(result).toEqual(activeAccountOne);
  });

  test('should return first account if company not found', () => {
    // Arrange
    const accounts = [activeAccountOne, pendingAccountTwo];

    // Act
    const selectedCompanyId = 3;
    const result = getSelectedAccount(accounts, selectedCompanyId);

    // Assert
    expect(result).toEqual(activeAccountOne);
  });

  test('should return first account of any pending status if company not found', () => {
    // Arrange
    const accounts = [pendingAccountTwo, activeAccountOne];

    // Act
    const selectedCompanyId = 3;
    const result = getSelectedAccount(accounts, selectedCompanyId);

    // Assert
    expect(result).toEqual(pendingAccountTwo);
  });

  test('should return first account of suspend status if company not found', () => {
    // Arrange
    const accounts = [suspendAccountThree, activeAccountOne];

    // Act
    const selectedCompanyId = 3;
    const result = getSelectedAccount(accounts, selectedCompanyId);

    // Assert
    expect(result).toEqual(suspendAccountThree);
  });

  test('should return undefined if no accounts', () => {
    // Arrange
    const accounts = [] as CompanyAccount[];

    // Act
    const selectedCompanyId = 3;
    const result = getSelectedAccount(accounts, selectedCompanyId);

    // Assert
    expect(result).toBeUndefined();
  });

  test('should return a company if there are duplicates', () => {
    // Arrange
    const accounts = [activeAccountOne, activeAccountOne, pendingAccountTwo];

    // Act
    const selectedCompanyId = 1;
    const result = getSelectedAccount(accounts, selectedCompanyId);

    // Assert
    expect(result).toEqual(activeAccountOne);
  });
});

describe('auth | utils | getActiveAccounts', () => {
  test('should return only active accounts', () => {
    // Arrange
    const accounts = [activeAccountOne, pendingAccountTwo, suspendAccountThree];

    // Act
    const result = getActiveAccounts(accounts);

    // Assert
    expect(result).toEqual([activeAccountOne]);
  });

  test('should return only active accounts & filter out duplicates', () => {
    // Arrange
    const accounts = [activeAccountOne, activeAccountOne, pendingAccountTwo];

    // Act
    const result = getActiveAccounts(accounts);

    // Assert
    expect(result).toEqual([activeAccountOne]);
  });
});

describe('auth | utils | getActiveOrPendingAccounts', () => {
  test('should return only active or pending accounts', () => {
    // Arrange
    const accounts = [activeAccountOne, pendingAccountTwo, suspendAccountThree];

    // Act
    const result = getActiveOrPendingAccounts(accounts);

    // Assert
    expect(result).toEqual([activeAccountOne, pendingAccountTwo]);
  });

  test('should return only active or pending accounts & filter out duplicates', () => {
    // Arrange
    const accounts = [activeAccountOne, activeAccountOne, pendingAccountTwo];

    // Act
    const result = getActiveOrPendingAccounts(accounts);

    // Assert
    expect(result).toEqual([activeAccountOne, pendingAccountTwo]);
  });
});
