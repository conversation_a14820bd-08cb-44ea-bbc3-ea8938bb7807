import { Alert } from 'react-native';

import { showSignOutConfirmation } from './signOutUtils';

const spyAlert = jest.spyOn(Alert, 'alert');

describe('showSignOutConfirmation', () => {
  const fn = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show the alert dialog', () => {
    showSignOutConfirmation(fn);

    expect(Alert.alert).toHaveBeenCalled();
  });

  it('should call the passed in function when pressing the cta', () => {
    showSignOutConfirmation(fn);
    /*
     * spy.mock.calls structure
     * [[title, message, [Object[]], {cancelable: true}]]
     */

    const buttons = spyAlert.mock.calls[0][2];
    const ctaButton = buttons ? buttons[1] : {};

    if (ctaButton.onPress) {
      ctaButton.onPress();
    }

    expect(fn).toHaveBeenCalled();
  });

  it('should not call the passed in function when pressing the cancel button', () => {
    showSignOutConfirmation(fn);
    /*
     * spy.mock.calls structure
     * [[title, message, [Object[]], {cancelable: true}]]
     */

    const buttons = spyAlert.mock.calls[0][2];
    const cancelButton = buttons ? buttons[0] : {};

    expect(cancelButton.onPress).toBeUndefined();
    expect(fn).not.toHaveBeenCalled();
  });
});
