import { <PERSON><PERSON><PERSON> } from 'buffer';
import { z } from 'zod';

const decodeJWT = (token: string): Record<string, unknown> => {
  const chunkedToken = token.split('.')[1];
  const base64 = chunkedToken.replace(/-/g, '+').replace(/_/g, '/');
  const buffer = Buffer.from(base64, 'base64').toString();

  return JSON.parse(buffer);
};

const CheckatradeAuthTokenSchema = z.object({
  email: z.string().nullish(),
  email_verified: z.coerce.boolean(),
  exp: z.number(),
  iat: z.number(),
  uid: z.string(),
  accounts: z.array(
    z.object({
      company_id: z.string(),
      user_id: z.string(),
      vetting_status: z.number(),
      sf_vetting_status: z.string(),
    }),
  ),
});

export type CheckatradeAuthToken = z.infer<typeof CheckatradeAuthTokenSchema>;

/**
 * Receives a jwt token and returns it decoded.
 *
 * @param {string} token
 * @returns {any} decoded token
 * */
export const decodeCheckatradeAuthJwt = (
  idToken: string,
): CheckatradeAuthToken => {
  const decodedJWT = decodeJWT(idToken);
  return CheckatradeAuthTokenSchema.parse(decodedJWT);
};

const ExpiryTokenSchema = z.object({ exp: z.number() });

export const getExpiryFromJWT = (token: string): number => {
  const decodedJWT = decodeJWT(token);
  return ExpiryTokenSchema.parse(decodedJWT).exp;
};
