import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  IS_WEB,
  STORAGE_KEY_BRAZE_EXTERNAL_ID,
  STORAGE_KEY_FIREBASE_TOKEN_LAST_UPDATED,
} from 'src/constants';
import { captureException } from 'src/services/datadog';
import { clearInLifeInsightsStorage } from 'src/utilities/storage/clearInLifeInsightsStorage';
import { storage } from 'src/utilities/storage/mmkv';
import { deleteStoredAuthTokens } from './authSession';

export async function resetUserStorage(): Promise<void> {
  try {
    await deleteStoredAuthTokens();
  } catch (error) {
    captureException(error, {
      tags: {
        module: 'useUserEvents',
        method: 'signOutUser',
        call: 'deleteStoredAuthTokens',
      },
    });
  }

  try {
    // Delete data from local storage
    storage.clearAll();
    await clearInLifeInsightsStorage();
    await AsyncStorage.multiRemove([
      STORAGE_KEY_BRAZE_EXTERNAL_ID,
      STORAGE_KEY_FIREBASE_TOKEN_LAST_UPDATED,
    ]);
  } catch (error) {
    captureException(error, {
      tags: {
        module: 'useUserEvents',
        method: 'signOutUser',
        call: 'clearLocalStorage',
      },
    });
  }
}

export function showSignOutConfirmation(onConfirm: () => void): void {
  if (IS_WEB) {
    onConfirm();
  } else {
    Alert.alert(
      'Are you sure?',
      "Please confirm that you'd like to log out of your account.",
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Log out',
          onPress: onConfirm,
          style: 'destructive',
        },
      ],
      {
        cancelable: true,
      },
    );
  }
}
