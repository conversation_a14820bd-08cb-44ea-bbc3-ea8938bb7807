import { UserAccessType } from '../auth.types';

enum StaCode {
  Member = 0,
  ExMember = 1,
  Staff = 2,
  PreRVM = 3,
  FailedVetting = 4,
}

// https://checkatrade.atlassian.net/wiki/spaces/S/pages/1442971649/New+Member+Claims+and+Identity+Endpoints
export const getVettingStatus = (staCode: number): UserAccessType => {
  switch (staCode) {
    case StaCode.Member:
      return UserAccessType.Active;
    case StaCode.ExMember:
      return UserAccessType.Suspend;
    case StaCode.Staff:
      return UserAccessType.Active;
    case StaCode.PreRVM:
      return UserAccessType.Pending;
    case StaCode.FailedVetting:
    default:
      return UserAccessType.Suspend;
  }
};
