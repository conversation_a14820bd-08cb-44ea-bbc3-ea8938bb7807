import { differenceInMinutes } from 'date-fns';
import {
  TokenTypeHint,
  fetchDiscoveryAsync,
  refreshAsync,
  revokeAsync,
} from 'expo-auth-session';
import { captureException } from 'src/services/datadog';
import { getExpiryFromJWT } from './decodeJWT';
import {
  deleteStoredAccessToken,
  deleteStoredIdToken,
  deleteStoredRefreshToken,
  getStoredAccessToken,
  getStoredAuthServiceType,
  getStoredIdToken,
  getStoredRefreshToken,
  hasAccessToken,
  hasRefreshToken,
  setStoredAccessToken,
  setStoredAuthServiceType,
  setStoredIdToken,
  setStoredRefreshToken,
} from './authStorage';
import type { IdentityServerConfig } from '../auth.types';
import { AuthServiceName } from '../service';

export interface Tokens {
  newAccessToken: string | null;
  newRefreshToken: string | null;
  newIdToken: string | null;
}

/**
 * Used to refresh the access token
 *
 * @returns {Tokens} The access token and refresh token issued by the authorization server.
 */
async function refreshAccessToken({
  clientId,
  discoveryUrl,
  scopes,
  authServiceName,
}: IdentityServerConfig): Promise<Tokens> {
  const refreshToken = await getStoredRefreshToken();

  if (refreshToken) {
    const discovery = await fetchDiscoveryAsync(discoveryUrl);

    const response = await refreshAsync(
      {
        clientId,
        refreshToken,
        scopes,
      },
      discovery,
    );

    if (response.refreshToken && response.idToken) {
      await setStoredAuthServiceType(authServiceName);
      await setStoredAccessToken(response.accessToken);
      await setStoredRefreshToken(response.refreshToken);
      await setStoredIdToken(response.idToken);
    }

    return {
      newAccessToken: response.accessToken,
      newRefreshToken: response.refreshToken ?? null,
      newIdToken: response.idToken ?? null,
    };
  }

  return {
    newAccessToken: null,
    newRefreshToken: null,
    newIdToken: null,
  };
}

const safeGetTokenExpiry = async (
  getToken: () => Promise<string | null>,
): Promise<number> => {
  try {
    const token = await getToken();
    if (!token) {
      return -1;
    }

    return getExpiryFromJWT(token) * 1000;
  } catch {
    // Ignore
  }

  return -1;
};

const getExpiryFromTokens = async (): Promise<number> => {
  const idTokenExpiry = await safeGetTokenExpiry(getStoredIdToken);
  return idTokenExpiry;
};

export async function shouldRefreshAccessToken(
  authServiceName: AuthServiceName,
): Promise<boolean> {
  if (authServiceName !== (await getStoredAuthServiceType())) {
    // If auth service has changed, then refresh token
    return true;
  }

  const expiryMs = await getExpiryFromTokens();
  if (expiryMs === -1) {
    return false;
  }

  const REFRESH_THRESHOLD_MINS = 15;
  const differenceMins = differenceInMinutes(expiryMs, Date.now());
  return differenceMins < REFRESH_THRESHOLD_MINS;
}

async function areTokensValid(): Promise<boolean> {
  const expiryMs = await getExpiryFromTokens();
  const EXPIRY_THRESHOLD_MINS = 5;
  const differenceMins = differenceInMinutes(expiryMs, Date.now());
  return differenceMins > EXPIRY_THRESHOLD_MINS;
}

export async function getAccessToken(): Promise<string | null> {
  const accessToken = await getStoredAccessToken();

  if (accessToken && (await areTokensValid())) {
    return accessToken;
  }

  return null;
}

async function maybeRefreshTokens(
  config: IdentityServerConfig,
): Promise<Tokens | null> {
  try {
    const hadRefreshToken = await hasRefreshToken();
    if (
      hadRefreshToken &&
      (await shouldRefreshAccessToken(config.authServiceName))
    ) {
      return await refreshAccessToken(config);
    }
  } catch (error) {
    captureException(error, {
      tags: {
        module: 'authSession',
        method: 'maybeRefreshTokens',
      },
    });
  }

  return null;
}

export async function getValidAccessToken(
  config: IdentityServerConfig,
): Promise<{
  hadAccessToken: boolean;
  hadRefreshToken: boolean;
  accessToken: string | null;
  idToken: string | null;
}> {
  const newTokens = await maybeRefreshTokens(config);
  if (
    !newTokens?.newAccessToken &&
    config.authServiceName !== (await getStoredAuthServiceType())
  ) {
    // If the auth service has changed, & token refresh fails
    // then return nulls to force logout
    return {
      hadAccessToken: false,
      hadRefreshToken: false,
      accessToken: null,
      idToken: null,
    };
  }

  return {
    hadAccessToken: await hasAccessToken(),
    hadRefreshToken: await hasRefreshToken(),
    // If token refresh fails, then use stored valid access token
    accessToken: newTokens?.newAccessToken || (await getAccessToken()),
    idToken: newTokens?.newIdToken || (await getStoredIdToken()),
  };
}

export async function revokeAuthToken(
  config: IdentityServerConfig,
): Promise<void> {
  const accessToken = await getStoredAccessToken();
  if (accessToken) {
    const discovery = await fetchDiscoveryAsync(config.discoveryUrl);

    await revokeAsync(
      {
        token: accessToken,
        tokenTypeHint: TokenTypeHint.AccessToken,
        clientId: config.clientId,
      },
      discovery,
    );
  }
}

export async function deleteStoredAuthTokens(): Promise<void> {
  // Remove stored auth tokens
  await deleteStoredAccessToken();
  await deleteStoredRefreshToken();
  await deleteStoredIdToken();
}
