import AsyncStorage from '@react-native-async-storage/async-storage';
import { sortBy, uniqWith } from 'lodash';
import { STORAGE_KEY_SELECTED_COMPANY_ID } from 'src/constants';
import type { CompanyAccount } from 'src/context/UserContext';
import { UserAccessType } from '../auth.types';

export const getStoredSelectedCompanyId = async (): Promise<number | null> => {
  const companyIdStr = await AsyncStorage.getItem(
    STORAGE_KEY_SELECTED_COMPANY_ID,
  );
  return companyIdStr ? Number(companyIdStr) : null;
};

export const setStoredSelectedCompanyId = (companyId: number): Promise<void> =>
  AsyncStorage.setItem(STORAGE_KEY_SELECTED_COMPANY_ID, String(companyId));

export const deleteStoredSelectedCompanyId = (): Promise<void> =>
  AsyncStorage.removeItem(STORAGE_KEY_SELECTED_COMPANY_ID);

const isDuplicateAccount = (
  accountA: CompanyAccount,
  accountB: CompanyAccount,
) =>
  accountA.companyId === accountB.companyId &&
  accountA.vettingStatus === accountB.vettingStatus;

const filterDuplicatedAccounts = (
  accounts: CompanyAccount[],
): CompanyAccount[] => {
  return uniqWith(
    sortBy(accounts, (account) => account.companyId),
    isDuplicateAccount,
  );
};

export const getActiveAccounts = (
  accounts: CompanyAccount[],
): CompanyAccount[] => {
  return filterDuplicatedAccounts(accounts).filter(
    (account) => account.vettingStatus === UserAccessType.Active,
  );
};

export const getActiveOrPendingAccounts = (
  accounts: CompanyAccount[],
): CompanyAccount[] => {
  return filterDuplicatedAccounts(accounts).filter(
    (account) =>
      account.vettingStatus === UserAccessType.Active ||
      account.vettingStatus === UserAccessType.Pending,
  );
};

export const getSelectedAccount = (
  accounts: CompanyAccount[],
  selectedCompanyId: number | null,
): CompanyAccount => {
  if (accounts.length === 1) {
    return accounts[0];
  }

  // Find the selected company in the active accounts
  const selectedCompany = selectedCompanyId
    ? accounts.find((account) => account.companyId === selectedCompanyId)
    : null;

  return selectedCompany || accounts[0];
};
