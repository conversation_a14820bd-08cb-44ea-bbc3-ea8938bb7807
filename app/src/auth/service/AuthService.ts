import type { CompanyAccount, User } from 'src/context/UserContext';
import type { User as FirebaseUser } from 'src/services/firebase/auth';
import type { IdentityServerConfig } from '../auth.types';

export type AuthResult = {
  accessToken: string;
  refreshToken: string;
  idToken: string;
};

export type AuthenticateUserReturnType = {
  user: User;
  selectedAccount: CompanyAccount;
};

export type RefreshAuthSessionReturnType =
  | {
      success: true;
      user: User;
      selectedAccount: CompanyAccount;
    }
  | {
      success: false;
    };

export type PartialFirebaseUser = Pick<FirebaseUser, 'uid'>;

export enum AuthServiceName {
  Legacy = 'legacy',
  Checkatrade = 'checkatrade',
}

export interface AuthService {
  serviceName: AuthServiceName;
  authServerConfig: IdentityServerConfig;
  redirectUri: string;
  authenticateUser(authResult: AuthResult): Promise<AuthenticateUserReturnType>;
  revokeAuthSession(): Promise<void>;
  hasStoredAuthSession(): Promise<boolean>;
  shouldRefreshAuthSession(): Promise<boolean>;
  getStoredAuthSession(): Promise<AuthenticateUserReturnType | null>;
  refreshAuthSession(): Promise<RefreshAuthSessionReturnType>;
  switchFirebaseCompanyAuth(companyId: number): Promise<PartialFirebaseUser>;
}
