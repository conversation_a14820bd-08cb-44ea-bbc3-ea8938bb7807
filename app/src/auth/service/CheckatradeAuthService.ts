import * as AuthSession from 'expo-auth-session';
import { config } from 'src/config';
import { DEEP_LINK_PREFIX } from 'src/navigation/Linking/prefixes';
import { isOfflineError } from 'src/services/datadog/datadogHelpers';
import { logEvent } from 'src/services/analytics';
import { captureException } from 'src/services/datadog';
import { signOutFirebase } from 'src/services/firebase/auth';
import { EVENT_TYPE } from 'src/constants.events';
import type { User } from 'src/context/UserContext';
import { UserAccessType, type IdentityServerConfig } from '../auth.types';
import {
  AuthServiceName,
  PartialFirebaseUser,
  type AuthResult,
  type AuthService,
  type AuthenticateUserReturnType,
  type RefreshAuthSessionReturnType,
} from './AuthService';
import { UID_NOT_EQUAL_ERROR, NO_ACCOUNTS_FOUND_ERROR } from '../constants';
import {
  getStoredAuthServiceType,
  getStoredIdToken,
  hasAccessToken,
  hasRefreshToken,
  setStoredAccessToken,
  setStoredAuthServiceType,
  setStoredIdToken,
  setStoredRefreshToken,
} from '../utils/authStorage';
import {
  getValidAccessToken,
  revokeAuthToken,
  shouldRefreshAccessToken,
} from '../utils/authSession';
import { decodeCheckatradeAuthJwt } from '../utils/decodeJWT';
import { signInToFirebaseAuth } from '../utils/firebaseAuth';
import { getVettingStatus } from '../utils/getVettingStatus';
import {
  getSelectedAccount,
  getStoredSelectedCompanyId,
} from '../utils/getSelectedAccount';

export class CheckatradeAuthService implements AuthService {
  serviceName = AuthServiceName.Checkatrade;

  authServerConfig: IdentityServerConfig = {
    clientId: config.checkatradeIdentityConfig.clientId,
    discoveryUrl: config.checkatradeIdentityConfig.baseUrl,
    scopes: ['email', 'openid', 'offline_access'],
    authServiceName: AuthServiceName.Checkatrade,
  };

  get redirectUri(): string {
    const nativeRedirectUri = AuthSession.makeRedirectUri({
      native: DEEP_LINK_PREFIX,
    });
    return `${nativeRedirectUri}${config.checkatradeIdentityConfig.redirectPath}`;
  }

  async authenticateUser({
    accessToken,
    refreshToken,
    idToken,
  }: AuthResult): Promise<AuthenticateUserReturnType> {
    if ((await getStoredAuthServiceType()) !== AuthServiceName.Checkatrade) {
      await signOutFirebase();
    }

    // Store access token & refresh token
    await setStoredAuthServiceType(AuthServiceName.Checkatrade);
    await setStoredAccessToken(accessToken);
    await setStoredIdToken(idToken);
    await setStoredRefreshToken(refreshToken);

    // Get user data from jwt
    const jwtUserData = getUserDataFromIdToken(idToken);

    validateUserAccounts(jwtUserData.catIdUid, jwtUserData.accounts);

    const selectedCompanyId = await getStoredSelectedCompanyId();
    const selectedAccount = getSelectedAccount(
      jwtUserData.accounts,
      selectedCompanyId,
    );

    // Authenticate with Firebase
    const firebaseUser = await this.signInFirebase(
      selectedAccount?.companyId,
      accessToken,
    );

    if (firebaseUser.uid !== selectedAccount.userId) {
      throw new Error(
        `${UID_NOT_EQUAL_ERROR}: ${firebaseUser.uid} does not match ${selectedAccount.userId}`,
      );
    }

    logEvent(EVENT_TYPE.SIGN_IN_SUCCESS);

    return {
      user: jwtUserData,
      selectedAccount,
    };
  }

  async revokeAuthSession(): Promise<void> {
    await revokeAuthToken(this.authServerConfig);
    await signOutFirebase();
  }

  async hasStoredAuthSession(): Promise<boolean> {
    return (await hasAccessToken()) || (await hasRefreshToken());
  }

  async getStoredAuthSession(): Promise<AuthenticateUserReturnType | null> {
    const idToken = await getStoredIdToken();
    if (!idToken) {
      return null;
    }

    const jwtUserData = getUserDataFromIdToken(idToken);
    validateUserAccounts(jwtUserData.catIdUid, jwtUserData.accounts);

    const selectedCompanyId = await getStoredSelectedCompanyId();
    const selectedAccount = getSelectedAccount(
      jwtUserData.accounts,
      selectedCompanyId,
    );

    return {
      user: jwtUserData,
      selectedAccount,
    };
  }

  async shouldRefreshAuthSession(): Promise<boolean> {
    return shouldRefreshAccessToken(AuthServiceName.Checkatrade);
  }

  async refreshAuthSession(): Promise<RefreshAuthSessionReturnType> {
    if ((await getStoredAuthServiceType()) !== AuthServiceName.Checkatrade) {
      await signOutFirebase();
    }

    const { hadAccessToken, hadRefreshToken, accessToken, idToken } =
      await getValidAccessToken(this.authServerConfig);

    if (!accessToken || !idToken) {
      if (hadAccessToken) {
        // Logout if we cannot resolve a valid access token
        logEvent(
          hadRefreshToken
            ? EVENT_TYPE.USER_SIGNOUT_AUTOMATIC
            : EVENT_TYPE.USER_SIGNOUT_EXPIRED_TOKEN,
        );
      }

      return { success: false };
    }

    // Get user data from jwt
    const jwtUserData = getUserDataFromIdToken(idToken);

    validateUserAccounts(jwtUserData.catIdUid, jwtUserData.accounts);

    const selectedCompanyId = await getStoredSelectedCompanyId();
    const selectedAccount = getSelectedAccount(
      jwtUserData.accounts,
      selectedCompanyId,
    );

    // Authenticate with Firebase Auth
    const firebaseUser = await this.signInFirebase(
      selectedAccount?.companyId,
      accessToken,
    );

    if (firebaseUser.uid !== selectedAccount.userId) {
      throw new Error(
        `${UID_NOT_EQUAL_ERROR}: ${firebaseUser.uid} does not match ${selectedAccount.userId}`,
      );
    }

    return {
      success: true,
      user: jwtUserData,
      selectedAccount,
    };
  }

  private async signInFirebase(
    companyId: number,
    accessToken: string,
  ): Promise<PartialFirebaseUser> {
    try {
      return await signInToFirebaseAuth(companyId, accessToken);
    } catch (error) {
      if (!isOfflineError(error)) {
        logEvent(EVENT_TYPE.SIGN_IN_FAILURE_FIREBASE_AUTH);
        captureException(error, {
          tags: {
            module: 'CheckatradeAuthService',
            method: 'signInFirebase',
            call: 'signInToFirebaseAuth',
          },
        });
      }

      throw error;
    }
  }

  async switchFirebaseCompanyAuth(
    companyId: number,
  ): Promise<PartialFirebaseUser> {
    await signOutFirebase();

    const { accessToken, idToken } = await getValidAccessToken(
      this.authServerConfig,
    );
    if (!accessToken) {
      throw new Error('Cannot find valid accessToken for switching company');
    }

    if (!idToken) {
      throw new Error('Cannot find valid idToken for switching company');
    }

    return await this.signInFirebase(companyId, accessToken);
  }
}

function getUserDataFromIdToken(
  idToken: string,
): Pick<User, 'email' | 'catIdUid' | 'accounts'> {
  const { uid, email, accounts } = decodeCheckatradeAuthJwt(idToken);
  return {
    catIdUid: uid,
    email: email ?? null,
    accounts: accounts.map((account) => {
      const vettingStatus = getVettingStatus(account.vetting_status);
      return {
        companyId: Number(account.company_id),
        userId: account.user_id,
        vettingStatus,
        isActive: vettingStatus === UserAccessType.Active,
      };
    }),
  };
}

function validateUserAccounts(
  catIdUid: string,
  accounts: User['accounts'],
): void {
  // throw if no accounts/companies are found
  if (accounts.length === 0) {
    logEvent(EVENT_TYPE.SIGN_IN_FAILURE_NO_ACCOUNTS, {
      catIdUid,
      accountsCount: 0,
      accountsActiveCount: 0,
    });
    throw new Error(NO_ACCOUNTS_FOUND_ERROR);
  }
}
