import {
  fetchDiscoveryAsync,
  refreshAsync,
  revokeAsync,
} from 'expo-auth-session';
import { subHours } from 'date-fns';
import { config } from 'src/config';
import { signOutFirebase, UserCredential } from 'src/services/firebase/auth';
import { User } from 'src/context/UserContext';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { CheckatradeAuthService } from './CheckatradeAuthService';
import { UserAccessType } from '../auth.types';
import {
  CheckatradeAuthToken,
  decodeCheckatradeAuthJwt,
  getExpiryFromJWT,
} from '../utils/decodeJWT';
import { signInToFirebaseAuth } from '../utils/firebaseAuth';
import {
  getStoredAccessToken,
  getStoredAuthServiceType,
  getStoredIdToken,
  getStoredRefreshToken,
  hasAccessToken,
  hasIdToken,
  hasRefreshToken,
  setStoredAccessToken,
  setStoredIdToken,
  setStoredRefreshToken,
} from '../utils/authStorage';
import { NO_ACCOUNTS_FOUND_ERROR } from '../constants';
import { AuthServiceName } from './AuthService';
import { getVettingStatus } from '../utils/getVettingStatus';

jest.mock('src/services/datadog', () => ({
  captureException: jest.fn(),
  setContext: jest.fn(),
}));
jest.mock('src/services/analytics', () => ({
  clearUserProperties: jest.fn(),
  logEvent: jest.fn(),
  setUserProperties: jest.fn(),
}));
jest.mock('expo-auth-session', () => ({
  ...jest.requireActual('expo-auth-session'),
  makeRedirectUri: () => 'https://example.com/',
  fetchDiscoveryAsync: jest.fn(),
  refreshAsync: jest.fn(),
  revokeAsync: jest.fn(),
}));
jest.mock('src/services/firebase/auth', () => ({
  signInToFirebaseAuth: jest.fn(),
  signOutFirebase: jest.fn(),
}));
jest.mock('src/services/analytics', () => ({
  logEvent: jest.fn(),
}));

jest.mock('src/constants');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const constantsOriginal = require('src/constants');

const Constants: {
  -readonly [Key in keyof typeof constantsOriginal]: (typeof constantsOriginal)[Key];
} = constantsOriginal;

jest.mock('src/auth/utils/decodeJWT', () => ({
  decodeCheckatradeAuthJwt: jest.fn(),
  getExpiryFromJWT: jest.fn(),
}));
jest.mock('src/auth/utils/firebaseAuth/signInToFirebaseAuth');
jest.mock('src/auth/utils/authStorage');

const mockJWTData: CheckatradeAuthToken = {
  email: '<EMAIL>',
  exp: **********,
  iat: **********,
  uid: 'catIdUid',
  accounts: [
    {
      company_id: '123',
      sf_vetting_status: 'Active',
      user_id: 'uid',
      vetting_status: 0,
    },
  ],
  email_verified: true,
};

const mockFirebaseAuthUser: Pick<UserCredential['user'], 'uid'> = {
  uid: 'uid',
};

const mockUser: User = {
  catIdUid: mockJWTData.uid,
  email: mockJWTData.email || null,
  accounts: mockJWTData.accounts.map((account) => ({
    companyId: Number(account.company_id),
    userId: account.user_id,
    vettingStatus: getVettingStatus(account.vetting_status),
    isActive:
      getVettingStatus(account.vetting_status) === UserAccessType.Active,
  })),
};

describe('auth | service | CheckatradeAuthService', () => {
  beforeEach(() => {
    (
      decodeCheckatradeAuthJwt as jest.Mock<CheckatradeAuthToken>
    ).mockReturnValue(mockJWTData);
    (signInToFirebaseAuth as jest.Mock).mockReturnValue(mockFirebaseAuthUser);
    (fetchDiscoveryAsync as jest.Mock).mockResolvedValue('discovery');
    (getStoredAuthServiceType as jest.Mock).mockResolvedValue(
      AuthServiceName.Checkatrade,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  test('has correct config', () => {
    // Arrange/Act
    const service = new CheckatradeAuthService();

    // Assert
    expect(service.serviceName).toBe('checkatrade');
    expect(service.authServerConfig.clientId).toBe(
      config.checkatradeIdentityConfig.clientId,
    );
    expect(service.authServerConfig.discoveryUrl).toBe(
      config.checkatradeIdentityConfig.baseUrl,
    );
  });

  test('has correct scopes for native', () => {
    // Arrange/Act
    const service = new CheckatradeAuthService();

    // Assert
    expect(service.authServerConfig.scopes).toEqual([
      'email',
      'openid',
      'offline_access',
    ]);
  });

  test('has correct scopes for web', () => {
    // Arrange
    Constants.IS_WEB = true;

    // Act
    const service = new CheckatradeAuthService();

    // Assert
    expect(service.authServerConfig.scopes).toEqual([
      'email',
      'openid',
      'offline_access',
    ]);
  });

  test('has correct redirectUri', () => {
    // Arrange/Act
    const service = new CheckatradeAuthService();

    // Assert
    expect(service.redirectUri).toEqual('https://example.com/login');
  });

  test('authenticateUser stores tokens and returns user data', async () => {
    // Arrange
    const service = new CheckatradeAuthService();

    // Act
    const result = await service.authenticateUser({
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      idToken: 'id-token',
    });

    // Assert
    expect(result).toEqual({
      user: mockUser,
      selectedAccount: mockUser.accounts[0],
    });
    expect(setStoredAccessToken).toHaveBeenCalledWith('access-token');
    expect(setStoredIdToken).toHaveBeenCalledWith('id-token');
    expect(setStoredRefreshToken).toHaveBeenCalledWith('refresh-token');
    expect(logEvent).toHaveBeenCalledWith(EVENT_TYPE.SIGN_IN_SUCCESS);
  });

  test('authenticateUser stores tokens and returns user data if user contains one pre-rvm company', async () => {
    // Arrange
    const service = new CheckatradeAuthService();
    (decodeCheckatradeAuthJwt as jest.Mock).mockReturnValue({
      ...mockJWTData,
      accounts: [
        {
          company_id: '123',
          sf_vetting_status: 'Suspend',
          user_id: 'uid',
          vetting_status: 3,
        },
      ],
    });

    // Act
    const result = await service.authenticateUser({
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      idToken: 'id-token',
    });

    // Assert
    const expectedUser: User = {
      ...mockUser,
      accounts: [
        {
          companyId: 123,
          userId: 'uid',
          vettingStatus: UserAccessType.Pending,
          isActive: false,
        },
      ],
    };
    expect(result).toEqual({
      user: expectedUser,
      selectedAccount: expectedUser.accounts[0],
    });
    expect(setStoredAccessToken).toHaveBeenCalledWith('access-token');
    expect(setStoredIdToken).toHaveBeenCalledWith('id-token');
    expect(setStoredRefreshToken).toHaveBeenCalledWith('refresh-token');
    expect(logEvent).toHaveBeenCalledWith(EVENT_TYPE.SIGN_IN_SUCCESS);
  });

  test('authenticateUser throws error if all companies are suspended', async () => {
    // Arrange
    const service = new CheckatradeAuthService();
    (decodeCheckatradeAuthJwt as jest.Mock).mockReturnValue({
      ...mockJWTData,
      accounts: [
        {
          company_id: '123',
          sf_vetting_status: 'Suspend',
          user_id: 'uid',
          vetting_status: 1,
        },
        {
          company_id: '456',
          sf_vetting_status: 'Active',
          user_id: 'uid2',
          vetting_status: 1,
        },
      ],
    });

    // Act
    const result = await service.authenticateUser({
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      idToken: 'id-token',
    });

    // Assert
    expect(result.user.catIdUid).toEqual(mockUser.catIdUid);
    expect(setStoredAccessToken).toHaveBeenCalledWith('access-token');
    expect(setStoredIdToken).toHaveBeenCalledWith('id-token');
    expect(setStoredRefreshToken).toHaveBeenCalledWith('refresh-token');
    expect(logEvent).toHaveBeenCalledWith(EVENT_TYPE.SIGN_IN_SUCCESS);
  });

  test('authenticateUser throws error if user has no accounts/companies', async () => {
    // Arrange
    const service = new CheckatradeAuthService();
    (decodeCheckatradeAuthJwt as jest.Mock).mockReturnValue({
      ...mockJWTData,
      accounts: [],
    });

    // Act/Assert
    await expect(() =>
      service.authenticateUser({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        idToken: 'id-token',
      }),
    ).rejects.toThrow(NO_ACCOUNTS_FOUND_ERROR);
  });

  test('authenticateUser throws error if user has mix of suspended/pre-rvm accounts/companies', async () => {
    // Arrange
    const service = new CheckatradeAuthService();
    (decodeCheckatradeAuthJwt as jest.Mock).mockReturnValue({
      ...mockJWTData,
      accounts: [
        {
          company_id: '123',
          sf_vetting_status: 'Active',
          user_id: 'uid',
          vetting_status: 1,
        },
        {
          company_id: '456',
          sf_vetting_status: 'Active',
          user_id: 'uid2',
          vetting_status: 1,
        },
        {
          company_id: '456',
          sf_vetting_status: 'Active',
          user_id: 'uid2',
          vetting_status: 3,
        },
        {
          company_id: '456',
          sf_vetting_status: 'Active',
          user_id: 'uid2',
          vetting_status: 4,
        },
      ],
    });

    // Act
    const result = await service.authenticateUser({
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      idToken: 'id-token',
    });

    // Assert
    expect(result.user.catIdUid).toEqual(mockUser.catIdUid);
    expect(setStoredAccessToken).toHaveBeenCalledWith('access-token');
    expect(setStoredIdToken).toHaveBeenCalledWith('id-token');
    expect(setStoredRefreshToken).toHaveBeenCalledWith('refresh-token');
    expect(logEvent).toHaveBeenCalledWith(EVENT_TYPE.SIGN_IN_SUCCESS);
  });

  test('revokeAuthSession revokes auth tokens and signs out of Firebase', async () => {
    // Arrange
    const service = new CheckatradeAuthService();
    (getStoredAccessToken as jest.Mock).mockResolvedValue('access-token');
    (revokeAsync as jest.Mock).mockResolvedValue(null);

    // Act
    await service.revokeAuthSession();

    // Assert
    expect(fetchDiscoveryAsync).toHaveBeenCalledTimes(1);
    expect(revokeAsync).toHaveBeenCalledWith(
      {
        clientId: config.checkatradeIdentityConfig.clientId,
        token: 'access-token',
        tokenTypeHint: 'access_token',
      },
      'discovery',
    );
    expect(signOutFirebase).toHaveBeenCalledTimes(1);
  });

  test('revokeAuthSession skips revoking if no tokens are stored', async () => {
    // Arrange
    const service = new CheckatradeAuthService();
    (getStoredAccessToken as jest.Mock).mockResolvedValue(null);

    // Act
    await service.revokeAuthSession();

    // Assert
    expect(fetchDiscoveryAsync).not.toHaveBeenCalled();
    expect(revokeAsync).not.toHaveBeenCalled();
    expect(signOutFirebase).toHaveBeenCalledTimes(1);
  });

  test('refreshAuthSession refreshes auth tokens and returns user data', async () => {
    // Arrange
    (hasAccessToken as jest.Mock).mockResolvedValue(true);
    (hasRefreshToken as jest.Mock).mockResolvedValue(true);
    (hasIdToken as jest.Mock).mockResolvedValue(true);
    (getStoredAccessToken as jest.Mock).mockResolvedValue('access-token');
    (getStoredIdToken as jest.Mock).mockResolvedValue('id-token');
    (getStoredRefreshToken as jest.Mock).mockResolvedValue('refresh-token');
    (refreshAsync as jest.Mock).mockResolvedValue({
      accessToken: 'new-access-token',
      refreshToken: 'new-refresh-token',
      idToken: 'new-id-token',
    });
    (getExpiryFromJWT as jest.Mock).mockReturnValue(
      Math.round(subHours(new Date(), 1).getTime() / 1000),
    );
    const service = new CheckatradeAuthService();

    // Act
    const result = await service.refreshAuthSession();

    // Assert
    expect(result).toEqual({
      success: true,
      user: mockUser,
      selectedAccount: mockUser.accounts[0],
    });
    expect(refreshAsync).toHaveBeenCalledTimes(1);
    expect(getExpiryFromJWT).toHaveBeenCalledTimes(1);
    expect(setStoredAccessToken).toHaveBeenCalledTimes(1);
    expect(setStoredIdToken).toHaveBeenCalledTimes(1);
    expect(setStoredRefreshToken).toHaveBeenCalledTimes(1);
  });

  test('refreshAuthSession throws if no accounts found', async () => {
    // Arrange
    (hasAccessToken as jest.Mock).mockResolvedValue(true);
    (hasRefreshToken as jest.Mock).mockResolvedValue(true);
    (hasIdToken as jest.Mock).mockResolvedValue(true);
    (getStoredAccessToken as jest.Mock).mockResolvedValue('access-token');
    (getStoredIdToken as jest.Mock).mockResolvedValue('id-token');
    (getStoredRefreshToken as jest.Mock).mockResolvedValue('refresh-token');
    (refreshAsync as jest.Mock).mockResolvedValue({
      accessToken: 'new-access-token',
      refreshToken: 'new-refresh-token',
      idToken: 'new-id-token',
    });
    (getExpiryFromJWT as jest.Mock).mockReturnValue(
      Math.round(subHours(new Date(), 1).getTime() / 1000),
    );
    (decodeCheckatradeAuthJwt as jest.Mock).mockReturnValue({
      ...mockJWTData,
      accounts: [],
    });
    const service = new CheckatradeAuthService();

    // Act/Assert
    await expect(() => service.refreshAuthSession()).rejects.toThrow(
      NO_ACCOUNTS_FOUND_ERROR,
    );
  });

  test('switchFirebaseCompanyAuth switches the Firebase user to a different company', async () => {
    // Arrange
    const newCompanyId = 456;
    (hasAccessToken as jest.Mock).mockResolvedValue(true);
    (hasRefreshToken as jest.Mock).mockResolvedValue(true);
    (hasIdToken as jest.Mock).mockResolvedValue(true);
    (getStoredAccessToken as jest.Mock).mockResolvedValue('access-token');
    (getStoredIdToken as jest.Mock).mockResolvedValue('id-token');
    (getStoredRefreshToken as jest.Mock).mockResolvedValue('refresh-token');
    (refreshAsync as jest.Mock).mockResolvedValue({
      accessToken: 'new-access-token',
      refreshToken: 'new-refresh-token',
      idToken: 'new-id-token',
    });
    (getExpiryFromJWT as jest.Mock).mockReturnValue(
      Math.round(subHours(new Date(), 1).getTime() / 1000),
    );
    (decodeCheckatradeAuthJwt as jest.Mock).mockReturnValue(mockJWTData);
    const service = new CheckatradeAuthService();

    // Act
    await service.switchFirebaseCompanyAuth(newCompanyId);

    // Assert
    expect(signInToFirebaseAuth).toHaveBeenCalledWith(
      newCompanyId,
      'new-access-token',
    );
  });
});
