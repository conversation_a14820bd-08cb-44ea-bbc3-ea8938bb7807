import React, { ReactNode, useCallback } from 'react';
import { FlatList, ListRenderItem, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  createMortarStyles,
  createTestIds,
  themeObject,
} from '@cat-home-experts/react-native-utilities';
import { Typography } from '@cat-home-experts/react-native-components';
import { ChevronRightSmall } from '@cat-home-experts/mortar-iconography-native';
import { useMarketplaceJobs } from 'src/screens/MarketplaceJobs/hooks';
import { JobListItemType, JobsTab } from 'src/data/schemas/api/capi/jobs';
import {
  getJobConsumerName,
  getJobTitle,
} from 'src/screens/MarketplaceJobs/utilities';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { Spinner } from '../primitives/Spinner';

const TEST_IDS = createTestIds('recent-interested-jobs-list', {
  CUSTOMER_NAME_LABEL: 'customer-name-label',
  JOB_LABEL: 'job-label',
  RIGHT_ICON: 'right-icon',
});

const jobListHeader = 'Your recent jobs';

interface RecentInterestedJobsListProps {
  header?: ReactNode;
  onJobPress: (job: JobListItemType) => void;
  jobsFilter?: JobsTab;
}

export const RecentInterestedJobsList: React.NativeFC<
  RecentInterestedJobsListProps,
  typeof TEST_IDS
> = ({
  header,
  onJobPress,
  jobsFilter = JobsTab.INTERESTED,
}: RecentInterestedJobsListProps) => {
  const isDesktop = useDesktopMediaQuery();
  const { bottom } = useSafeAreaInsets();
  const { jobs, fetchNextPage, isFetchingNextPage, isRefetching, refetch } =
    useMarketplaceJobs(jobsFilter, 20);

  const renderItem = useCallback<ListRenderItem<JobListItemType>>(
    ({ item }) => {
      const handleOnClick = () => {
        onJobPress(item);
      };

      return (
        <TouchableOpacity
          activeOpacity={0.6}
          style={styles.jobItem}
          onPress={handleOnClick}
        >
          <View>
            <Typography
              useVariant="bodySemiBold"
              testID={TEST_IDS.JOB_LABEL}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {getJobConsumerName(item)}
            </Typography>
            <Typography
              useVariant="caption"
              testID={TEST_IDS.CUSTOMER_NAME_LABEL}
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.consumerNameCaption}
            >
              {getJobTitle(item)}
            </Typography>
          </View>

          <ChevronRightSmall
            testID={TEST_IDS.RIGHT_ICON}
            color={themeObject.palette.mortarV3.tokenNeutral600}
            size={18}
          />
        </TouchableOpacity>
      );
    },
    [onJobPress],
  );

  const renderHeader = useCallback(
    () => (
      <>
        {header}
        <View style={styles.jobHeader}>
          <Typography useVariant="bodySMSemiBold" style={styles.textColour}>
            {jobListHeader}
          </Typography>
        </View>
      </>
    ),
    [header],
  );

  const renderFooter = useCallback(() => {
    if (!isFetchingNextPage) {
      return null;
    }

    return (
      <View style={styles.spinnerContainer}>
        <Spinner size={48} />
      </View>
    );
  }, [isFetchingNextPage]);

  return (
    <FlatList
      ListHeaderComponent={renderHeader}
      data={jobs}
      renderItem={renderItem}
      stickyHeaderIndices={[0]}
      style={[styles.root, isDesktop && styles.desktopRoot]}
      contentContainerStyle={[
        styles.contentContainer,
        isDesktop && styles.desktopContentContainer,
        { paddingBottom: bottom },
      ]}
      ListFooterComponent={renderFooter}
      onEndReachedThreshold={0.2}
      onEndReached={fetchNextPage}
      keyExtractor={(item) => item.id}
      refreshing={isRefetching}
      onRefresh={refetch}
      showsVerticalScrollIndicator={false}
      testID={TEST_IDS.ROOT}
    />
  );
};

RecentInterestedJobsList.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  root: {
    flex: 1,
  },
  desktopRoot: {
    paddingVertical: spacing(1),
  },
  contentContainer: {
    width: '100%',
    maxWidth: 800,
    alignSelf: 'center',
  },
  desktopContentContainer: {
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
    borderRadius: spacing(1),
    overflow: 'hidden',
  },
  jobHeader: {
    textAlign: 'left',
    paddingTop: spacing(3),
    paddingBottom: spacing(1),
    paddingHorizontal: spacing(3),
    backgroundColor: palette.mortarV3.tokenNeutral0,
  },
  textColour: {
    color: palette.mortarV3.tokenNeutral600,
  },
  consumerNameCaption: {
    color: palette.mortarV3.tokenNeutral600,
  },
  jobItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(1.625),
    backgroundColor: palette.mortarV3.tokenNeutral0,
    cursor: 'pointer',
  },
  spinnerContainer: {
    alignItems: 'center',
    paddingVertical: spacing(4),
    backgroundColor: palette.mortarV3.tokenNeutral0,
  },
}));
