import React from 'react';
import { Text } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { useMarketplaceJobs } from 'src/screens/MarketplaceJobs/hooks';
import {
  FulfilmentType,
  JobListItemType,
} from 'src/data/schemas/api/capi/jobs';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { RecentInterestedJobsList } from './RecentInterestedJobsList';

// Mock dependencies
jest.mock('src/screens/MarketplaceJobs/hooks', () => ({
  useMarketplaceJobs: jest.fn(),
}));

jest.mock('src/hooks/useMediaQuery', () => ({
  useDesktopMediaQuery: jest.fn(),
  useXLargeDesktopMediaQuery: jest.fn(),
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: jest.fn(),
}));

// Mock data
const mockJobs: JobListItemType[] = [
  {
    id: 'job-1',
    category: {
      id: 1,
      label: 'Category 1',
    },
    channelId: 'channel-1',
    consumer: {
      id: 'consumer-1',
      firstName: 'Jim',
      lastName: 'Bob',
    },
    description: 'This is the description.',
    postcode: 'PO06 3EN',
    createdAt: '2022-05-05T10:48:42.000Z',
    status: 'REQUESTED',
    preferredStart: {
      id: 'start-1',
      title: 'Start 1',
      date: '2022-05-05T10:48:42.000Z',
    },
    tradeViewed: true,
    fulfilmentType: FulfilmentType.ENQUIRY,
  },
  {
    id: 'job-2',
    category: {
      id: 2,
      label: 'Category 2',
    },
    channelId: 'channel-2',
    consumer: {
      id: 'consumer-2',
      firstName: 'Jane',
      lastName: 'Smith',
    },
    description: 'This is another description.',
    postcode: 'PO07 4FG',
    createdAt: '2022-06-10T14:22:30.000Z',
    status: 'REQUESTED',
    preferredStart: {
      id: 'start-2',
      title: 'Start 2',
      date: '2022-06-15T09:00:00.000Z',
    },
    tradeViewed: true,
    fulfilmentType: FulfilmentType.ENQUIRY,
  },
];

describe('RecentInterestedJobsList', () => {
  const mockOnJobPress = jest.fn();
  const mockFetchNextPage = jest.fn();
  const mockRefetch = jest.fn();

  beforeEach(() => {
    // Setup default mocks
    (useMarketplaceJobs as jest.Mock).mockReturnValue({
      jobs: mockJobs,
      fetchNextPage: mockFetchNextPage,
      isFetchingNextPage: false,
      isRefetching: false,
      refetch: mockRefetch,
    });
    (useDesktopMediaQuery as jest.Mock).mockReturnValue(false);
    (useSafeAreaInsets as jest.Mock).mockReturnValue({ bottom: 0 });
  });

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders the component with jobs', () => {
    const { getByText, getAllByTestId } = render(
      <RecentInterestedJobsList onJobPress={mockOnJobPress} />,
    );

    // Check header is rendered
    expect(getByText('Your recent jobs')).toBeOnTheScreen();

    // Check job items are rendered
    const jobLabels = getAllByTestId(
      RecentInterestedJobsList.testIds.JOB_LABEL,
    );
    expect(jobLabels).toHaveLength(2);
    expect(jobLabels[0].props.children).toBe('Jim Bob');
    expect(jobLabels[1].props.children).toBe('Jane Smith');

    const customerLabels = getAllByTestId(
      RecentInterestedJobsList.testIds.CUSTOMER_NAME_LABEL,
    );
    expect(customerLabels).toHaveLength(2);
    expect(customerLabels[0].props.children).toBe('Category 1 - PO06 3EN');
    expect(customerLabels[1].props.children).toBe('Category 2 - PO07 4FG');
  });

  it('renders a custom header when provided', () => {
    const customHeader = <Text>{'Custom Header'}</Text>;
    const { getByText } = render(
      <RecentInterestedJobsList
        onJobPress={mockOnJobPress}
        header={customHeader}
      />,
    );

    expect(getByText('Custom Header')).toBeOnTheScreen();
    expect(getByText('Your recent jobs')).toBeOnTheScreen();
  });

  it('calls onJobPress when a job item is pressed', () => {
    const { getAllByTestId } = render(
      <RecentInterestedJobsList onJobPress={mockOnJobPress} />,
    );

    const jobItems = getAllByTestId(RecentInterestedJobsList.testIds.JOB_LABEL);
    fireEvent.press(jobItems[0].parent.parent);

    expect(mockOnJobPress).toHaveBeenCalledWith(mockJobs[0]);
  });

  it('renders a spinner when fetching next page', () => {
    (useMarketplaceJobs as jest.Mock).mockReturnValue({
      jobs: mockJobs,
      fetchNextPage: mockFetchNextPage,
      isFetchingNextPage: true,
      isRefetching: false,
      refetch: mockRefetch,
    });

    const { getByTestId } = render(
      <RecentInterestedJobsList onJobPress={mockOnJobPress} />,
    );

    // Check spinner is rendered
    expect(getByTestId('spinner')).toBeOnTheScreen();
  });

  it('calls fetchNextPage when end of list is reached', () => {
    const { getByTestId } = render(
      <RecentInterestedJobsList onJobPress={mockOnJobPress} />,
    );

    const flatList = getByTestId(RecentInterestedJobsList.testIds.ROOT);
    fireEvent(flatList, 'onEndReached');

    expect(mockFetchNextPage).toHaveBeenCalled();
  });

  it('calls refetch when pull-to-refresh is triggered', () => {
    const { getByTestId } = render(
      <RecentInterestedJobsList onJobPress={mockOnJobPress} />,
    );

    const flatList = getByTestId(RecentInterestedJobsList.testIds.ROOT);
    fireEvent(flatList, 'onRefresh');

    expect(mockRefetch).toHaveBeenCalled();
  });

  it('applies desktop styles when on desktop', () => {
    (useDesktopMediaQuery as jest.Mock).mockReturnValue(true);

    const { getByTestId } = render(
      <RecentInterestedJobsList onJobPress={mockOnJobPress} />,
    );

    const flatList = getByTestId(RecentInterestedJobsList.testIds.ROOT);
    expect(flatList.props.style[1]).toBeTruthy(); // Desktop style is applied
  });

  it('renders empty state when no jobs are available', () => {
    (useMarketplaceJobs as jest.Mock).mockReturnValue({
      jobs: [],
      fetchNextPage: mockFetchNextPage,
      isFetchingNextPage: false,
      isRefetching: false,
      refetch: mockRefetch,
    });

    const { queryAllByTestId } = render(
      <RecentInterestedJobsList onJobPress={mockOnJobPress} />,
    );

    const jobLabels = queryAllByTestId(
      RecentInterestedJobsList.testIds.JOB_LABEL,
    );
    expect(jobLabels).toHaveLength(0);
  });
});
