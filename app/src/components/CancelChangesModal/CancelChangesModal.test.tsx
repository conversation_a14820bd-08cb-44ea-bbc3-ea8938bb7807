import React from 'react';
import { render, fireEvent, cleanup, act } from '@testing-library/react-native';

import { COMPONENT_TYPE, ANALYTICS_ACTION_TYPE } from 'src/constants.events';
import { CancelChangesModal } from './CancelChangesModal';

const mockOnPressContinue = jest.fn();
const mockLogEvents = jest.fn();

jest.mock('src/services/analytics', () => {
  return {
    logEvent: (...args: unknown[]) => mockLogEvents(...args),
  };
});

const mockAddListener = jest.fn();
jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');

  return {
    ...actualNav,
    useNavigation: () => {
      return {
        navigate: jest.fn(),
        addListener: mockAddListener,
        removeListener: jest.fn(),
        setOptions: jest.fn(),
        canGoBack: () => true,
        goBack: jest.fn(),
      };
    },
  };
});

const event = {
  data: {
    type: 'GO_BACK',
  },
  preventDefault: jest.fn(),
};

let capturedChangeCallback: (arg0: {
  data: { type: string };
  preventDefault: () => void;
}) => void;

describe('Components | CancelChangesModal', () => {
  const parentScreenName = 'test-me-please';
  const defaultProps = {
    onPressContinue: () => null,
    hasDataChanged: false,
    parentScreenName,
    modalTitle: 'Test title',
    modalDescription: 'Test description',
    iconColor: '#DC143C',
    continueButtonLabel: 'Continue',
    discardButtonLabel: 'Discard',
  };

  beforeEach(() => {
    mockAddListener.mockImplementation((_, callback) => {
      capturedChangeCallback = callback;
      return jest.fn();
    });
  });
  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('renders CancelChangesModal and modal is hidden initially', () => {
    defaultProps.hasDataChanged = true;

    const { queryByTestId } = render(
      <CancelChangesModal icon="info-fill" {...defaultProps} />,
    );

    const cancelChangesModal = queryByTestId(CancelChangesModal.testIds.ROOT);
    expect(cancelChangesModal).toBeNull();
  });

  it('renders modal when user attempts to leave when data has changed', () => {
    const { getByTestId } = render(
      <CancelChangesModal icon="info-fill" {...defaultProps} />,
    );

    act(() => {
      capturedChangeCallback(event);
    });

    const cancelChangesModal = getByTestId(CancelChangesModal.testIds.ROOT);

    expect(cancelChangesModal).toBeDefined();
    expect(cancelChangesModal.props.visible).toBe(true);
  });

  it('logs an event when modal is visible', () => {
    const { getByTestId } = render(
      <CancelChangesModal icon="info-fill" {...defaultProps} />,
    );

    act(() => {
      capturedChangeCallback(event);
    });

    const cancelChangesModal = getByTestId(CancelChangesModal.testIds.ROOT);

    expect(cancelChangesModal).toBeDefined();

    expect(mockLogEvents).toHaveBeenNthCalledWith(
      1,
      `${parentScreenName}_${CancelChangesModal.screenName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
    );
  });

  it('can accept continue option pressed when NO "onPressContinue" function is supplied', () => {
    const { getByTestId, queryByTestId } = render(
      <CancelChangesModal icon="info-fill" {...defaultProps} />,
    );

    act(() => {
      capturedChangeCallback(event);
    });

    const ignoreButton = getByTestId(
      CancelChangesModal.testIds.CONTINUE_BUTTON,
    );

    fireEvent.press(ignoreButton);

    const cancelChangesModal = queryByTestId(CancelChangesModal.testIds.ROOT);
    expect(cancelChangesModal).toBeNull();

    expect(mockOnPressContinue).toHaveBeenCalledTimes(0);
  });

  it('can accept continue option pressed when "onPressContinue" function is supplied', () => {
    defaultProps.onPressContinue = mockOnPressContinue;

    const { getByTestId, queryByTestId } = render(
      <CancelChangesModal icon="info-fill" {...defaultProps} />,
    );

    act(() => {
      capturedChangeCallback(event);
    });

    const ignoreButton = getByTestId(
      CancelChangesModal.testIds.CONTINUE_BUTTON,
    );

    fireEvent.press(ignoreButton);

    const cancelChangesModal = queryByTestId(CancelChangesModal.testIds.ROOT);
    expect(cancelChangesModal).toBeNull();

    expect(mockOnPressContinue).toHaveBeenCalledTimes(1);
  });

  it('logs an event when continue option pressed ', () => {
    const { getByTestId } = render(
      <CancelChangesModal icon="info-fill" {...defaultProps} />,
    );

    act(() => {
      capturedChangeCallback(event);
    });

    const ignoreButton = getByTestId(
      CancelChangesModal.testIds.CONTINUE_BUTTON,
    );

    fireEvent.press(ignoreButton);

    expect(mockLogEvents).toHaveBeenNthCalledWith(
      2,
      `${parentScreenName}_${CancelChangesModal.screenName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.CONTINUED}`,
    );
  });

  it('can accept discard option pressed', () => {
    const { getByTestId, queryByTestId } = render(
      <CancelChangesModal icon="info-fill" {...defaultProps} />,
    );

    act(() => {
      capturedChangeCallback(event);
    });

    const ignoreButton = getByTestId(CancelChangesModal.testIds.IGNORE_BUTTON);

    fireEvent.press(ignoreButton);

    const cancelChangesModal = queryByTestId(CancelChangesModal.testIds.ROOT);
    expect(cancelChangesModal).toBeNull();
  });

  it('logs an event when discard option pressed ', () => {
    const { getByTestId } = render(
      <CancelChangesModal icon="info-fill" {...defaultProps} />,
    );

    act(() => {
      capturedChangeCallback(event);
    });

    const ignoreButton = getByTestId(CancelChangesModal.testIds.IGNORE_BUTTON);

    fireEvent.press(ignoreButton);

    expect(mockLogEvents).toHaveBeenNthCalledWith(
      2,
      `${parentScreenName}_${CancelChangesModal.screenName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.DISCARDED}`,
    );
  });
});
