import React, {
  ReactElement,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { NavigationAction, useNavigation } from '@react-navigation/native';
import useLatestCallback from 'use-latest-callback';

import { Modal, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';

import { NavBackIcon } from 'src/components';
import { HOME_SCREEN, IS_WEB } from 'src/constants';
import { ANALYTICS_ACTION_TYPE, COMPONENT_TYPE } from 'src/constants.events';
import { useBlockNavigationGesture } from 'src/hooks/useBlockNavigationGesture';
import { useMobileSmallScreenMediaQuery } from 'src/hooks/useMediaQuery';
import { logEvent } from 'src/services/analytics';

import type { CancelChangesModalProps } from './CancelChangesModal.types';

const screenName = 'change';

const TEST_IDS = createTestIds('cancel-change-modal', {
  CONTINUE_BUTTON: 'continue-button',
  IGNORE_BUTTON: 'ignore-button',
});

export const CancelChangesModal = ({
  onPressContinue,
  onPressDiscard,
  hasDataChanged,
  parentScreenName,
  modalTitle,
  modalDescription,
  icon,
  iconColor,
  continueButtonLabel,
  discardButtonLabel,
  testID = TEST_IDS.ROOT,
}: CancelChangesModalProps): ReactElement => {
  // Refs
  const pendingActionRef = useRef<NavigationAction | null>(null);

  // State
  const [cancelModalVisible, setCancelModalVisible] = useState<boolean>(false);

  // Navigation
  const navigation = useNavigation();
  useBlockNavigationGesture(hasDataChanged);

  // Computed Values
  const isMobileSmallScreen = useMobileSmallScreenMediaQuery();

  // Methods
  const defaultBack = useCallback(() => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate(HOME_SCREEN);
    }
  }, [navigation]);

  const showCancelChangesModal = useCallback(() => {
    setCancelModalVisible(true);
    logEvent(
      `${parentScreenName}_${screenName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
    );
  }, [parentScreenName]);

  const onNavBackIconPress = useLatestCallback(() => {
    // this callback uses useLatestCallback instead of useCallback
    // because it needs to be able to access the latest value of hasDataChanged
    // without causing navigation.setOptions to be called more than once
    if (hasDataChanged && IS_WEB) {
      showCancelChangesModal();
    } else {
      defaultBack();
    }
  });

  const handleOnPressContinue = useCallback(() => {
    setCancelModalVisible(false);
    pendingActionRef.current = null;

    onPressContinue?.();

    logEvent(
      `${parentScreenName}_${screenName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.CONTINUED}`,
    );
  }, [onPressContinue, parentScreenName]);

  const handleOnPressDiscard = useCallback(() => {
    if (pendingActionRef.current) {
      navigation.dispatch(pendingActionRef.current);
    } else {
      defaultBack();
    }

    onPressDiscard?.();

    setCancelModalVisible(false);

    logEvent(
      `${parentScreenName}_${screenName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.DISCARDED}`,
    );
  }, [navigation, parentScreenName, defaultBack, onPressDiscard]);

  // Effects
  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => <NavBackIcon onPress={onNavBackIconPress} />,
    });
  }, [navigation, onNavBackIconPress]);

  useEffect(() => {
    return navigation.addListener('beforeRemove', (event) => {
      if (hasDataChanged) {
        // Prevent default behavior of leaving the screen
        event.preventDefault();

        pendingActionRef.current = event.data.action;
        showCancelChangesModal();
      }
    });
  }, [hasDataChanged, navigation, showCancelChangesModal]);

  return (
    <Modal
      buttons={[
        {
          buttonLabel: continueButtonLabel,
          buttonOnPress: handleOnPressContinue,
          testId: TEST_IDS.CONTINUE_BUTTON,
        },
        {
          buttonLabel: discardButtonLabel,
          buttonOnPress: handleOnPressDiscard,
          testId: TEST_IDS.IGNORE_BUTTON,
          variant: 'muted',
          buttonStyle: styles.buttonStyle,
        },
      ]}
      icon={icon}
      iconColor={iconColor}
      testID={testID}
      title={modalTitle}
      visible={cancelModalVisible}
      allowNonInteractiveDismissal={false}
      titleStyle={styles.title}
    >
      <Typography
        use="bodyRegular"
        style={[
          styles.modalMessage,
          // eslint-disable-next-line react-native/no-inline-styles
          { marginHorizontal: isMobileSmallScreen ? 12 : 24 },
        ]}
      >
        {modalDescription}
      </Typography>
    </Modal>
  );
};

CancelChangesModal.testIds = TEST_IDS;
CancelChangesModal.screenName = screenName;

const styles = createMortarStyles(({ palette, spacing }) => ({
  buttonStyle: {
    marginTop: spacing(2),
  },
  modalMessage: {
    textAlign: 'center',
    color: palette.mortar.tokenColorBlack,
    marginTop: spacing(2),
  },
  title: {
    textAlign: 'center',
  },
}));
