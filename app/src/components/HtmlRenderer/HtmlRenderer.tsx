/* eslint-disable import/no-default-export */
// (dom components require default export)
'use dom';

import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import React, { ReactElement } from 'react';
import { sanitizeRichTextForWebUse } from 'src/utilities/text';

interface HtmlRendererProps {
  html: string;
  dom: import('expo/dom').DOMProps;
}

export default function HtmlRenderer({
  html,
}: HtmlRendererProps): ReactElement {
  const sanitizedHtml = sanitizeRichTextForWebUse(html);

  return (
    <>
      <style>
        {`
          p {
            margin-top: 0;
            margin-bottom: 0;
          }
        `}
      </style>
      <div
        style={styles.root}
        dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
      />
    </>
  );
}

const styles = createMortarStyles(({ palette, typographyV2 }) => ({
  root: {
    width: '100%',
    borderWidth: 0,
    fontFamily: `${typographyV2.tokenFontFamily}, Helvetica, Sans-Serif`,
    fontSize: 16,
    padding: 0,
    margin: 0,
    color: palette.mortarV3.tokenNeutral800,
  },
}));
