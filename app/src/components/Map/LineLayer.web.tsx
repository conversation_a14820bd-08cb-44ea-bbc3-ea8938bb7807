import React, { useMemo } from 'react';
import { Layer as MapGlLayer } from 'react-map-gl';
import type { LineLayerProps } from './Map.types';
import { convertToWebStyle } from './mapUtils';

export const LineLayer: React.FC<LineLayerProps> = ({
  source,
  sourceLayerID,
  style,
  ...props
}) => {
  const webStyle = useMemo(() => convertToWebStyle(style), [style]);
  return (
    <MapGlLayer
      {...props}
      type="line"
      source={source}
      source-layer={sourceLayerID}
      paint={webStyle}
    />
  );
};
