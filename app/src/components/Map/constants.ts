import {
  tokenDefault500,
  tokenDefault700,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV3';
import { MapConfigTypes } from './Map.types';

export const MAP_CONSTANTS = {
  DEFAULT_MAP_STYLE: 'mapbox://styles/mapbox/streets-v9',
};

export const mapDistrictsConfig: MapConfigTypes = {
  url: 'mapbox://checkatrade.70f705ls',
  source: 'districts',
  sourceLayer: 'postcode_districts',
  propertyLabel: 'postcode_district',
};

export const mapAreasConfig: MapConfigTypes = {
  url: 'mapbox://checkatrade.70f705ls',
  source: 'areas',
  sourceLayer: 'postcode_areas',
  propertyLabel: 'postcode_area',
};

const MAP_FILL_COLOR = tokenDefault500;
const MAP_HIGHLIGHT_COLOR = tokenDefault700;
const MAP_TRANSPARENT_COLOR = 'transparent';
const MAP_LINE_COLOR = tokenDefault500;
const MAP_LAYER_OPACITY = 0.3;
const MAP_HOVER_OPACITY = 0.5;
const MAP_TRANSPARENT_OPACITY = 0;

export const MAP_STYLE_TOKENS: Record<string, Record<string, unknown>> = {
  FILL: {
    fillColor: MAP_FILL_COLOR,
    fillOpacity: MAP_LAYER_OPACITY,
  },
  HIGHLIGHTED: {
    fillColor: MAP_HIGHLIGHT_COLOR,
    fillOpacity: MAP_LAYER_OPACITY,
  },
  HOVERED: {
    fillColor: MAP_HIGHLIGHT_COLOR,
    fillOpacity: MAP_HOVER_OPACITY,
  },
  TRANSPARENT: {
    fillColor: MAP_TRANSPARENT_COLOR,
    fillOpacity: MAP_TRANSPARENT_OPACITY,
  },
  LINE: {
    lineColor: MAP_LINE_COLOR,
    lineOpacity: 1,
    lineWidth: 1,
    lineOffset: 0,
  },
};
