import 'mapbox-gl/dist/mapbox-gl.css';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { StyleSheet, View } from 'react-native';
import { config } from 'src/config';
import {
  Map as MapGL,
  GeolocateControl,
  NavigationControl,
  ScaleControl,
  MapRef,
  AttributionControl,
} from 'react-map-gl';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import type { Feature, FeatureCollection } from 'geojson';
import { convertPositionToWebPosition } from 'src/components/Map/mapUtils';
import { MAP_CONSTANTS } from './constants';
import {
  FilterSpecification,
  MapProps,
  MapRefInstance,
  PaddingOptions,
} from './Map.types';

/**
 * Map component for web - Do not import this directly,
 * use the lazy loaded version from src/components/Map/index.tsx
 */

export const Map = forwardRef<MapRefInstance, MapProps>(
  (
    {
      zoom,
      containerStyle,
      mapboxStyle,
      fadeDuration = 0,
      mapStyleURL = MAP_CONSTANTS.DEFAULT_MAP_STYLE,
      interactive = false,
      attributionControl,
      geolocateControl = false,
      scaleControl = false,
      children,
      cursor,
      reuseMaps,
      maxBounds,
      onMapIdle,
      onDidFinishLoadingMap,
      logoPosition,
      attributionPosition,
    },
    ref,
  ) => {
    const initialViewState = {
      zoom,
    };
    const mapRef = useRef<MapRef>(null);

    useImperativeHandle(ref, () => ({
      querySourceFeatures: async (
        sourceId: string,
        filter?: FilterSpecification,
        sourceLayerIDs?: string[],
      ): Promise<FeatureCollection> => {
        const features = mapRef.current?.querySourceFeatures(sourceId, {
          sourceLayer: sourceLayerIDs ? sourceLayerIDs[0] : undefined,
          filter,
        });
        return {
          type: 'FeatureCollection',
          features: features as Feature[],
        };
      },
      fitBounds: (
        bounds: [number, number, number, number],
        padding?: PaddingOptions,
        duration?: number,
      ) => mapRef?.current?.fitBounds(bounds, { padding, duration }),
    }));

    return (
      <View style={[styles.container, containerStyle]}>
        <MapGL
          reuseMaps={reuseMaps}
          interactive={interactive}
          attributionControl={attributionControl}
          fadeDuration={fadeDuration}
          mapStyle={mapStyleURL}
          initialViewState={initialViewState}
          style={StyleSheet.flatten(mapboxStyle) as React.CSSProperties}
          scrollZoom={interactive}
          cursor={cursor}
          mapboxAccessToken={config.mapBoxConfig.apiKey}
          maxBounds={maxBounds ? [...maxBounds.ne, ...maxBounds.sw] : undefined}
          ref={mapRef}
          onIdle={onMapIdle}
          onLoad={onDidFinishLoadingMap}
          logoPosition={
            logoPosition
              ? convertPositionToWebPosition(logoPosition)
              : undefined
          }
        >
          {attributionPosition && (
            <AttributionControl
              position={convertPositionToWebPosition(attributionPosition)}
            />
          )}
          {interactive && (
            <>
              <NavigationControl />
              {geolocateControl && <GeolocateControl />}
              {children}
            </>
          )}
          {scaleControl && <ScaleControl unit="imperial" />}
        </MapGL>
      </View>
    );
  },
);

const styles = createMortarStyles(() => ({
  container: {
    flex: 1,
  },
}));
