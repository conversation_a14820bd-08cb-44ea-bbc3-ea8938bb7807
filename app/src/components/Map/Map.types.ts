import { StyleProp, ViewStyle } from 'react-native';
import type { ReactElement, ReactNode } from 'react';
import type { FeatureCollection } from 'geojson';

export type OnPressEvent = {
  features: Array<GeoJSON.Feature>;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  point: {
    x: number;
    y: number;
  };
};
export type PositionType =
  | { top: number; left: number }
  | { top: number; right: number }
  | { bottom: number; left: number }
  | { bottom: number; right: number };

export type WebPositionType =
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right';

export interface MapProps {
  reuseMaps?: boolean;
  zoom: number;
  containerStyle?: StyleProp<ViewStyle>;
  mapboxStyle?: StyleProp<ViewStyle>;
  fadeDuration?: number;
  mapStyleURL?: string;
  interactive?: boolean;
  attributionControl?: boolean;
  geolocateControl?: boolean;
  scaleControl?: boolean;
  children?: ReactNode;
  cursor?: string;
  bounds?: BoundsType;
  longitude?: number;
  latitude?: number;
  maxBounds?: BoundsType;
  onStyleLoaded?: () => void;
  onMapIdle?: () => void;
  onDidFinishLoadingMap?: () => void;
  attributionPosition?: PositionType;
  logoPosition?: PositionType;
}
export type PaddingOptions = {
  top: number;
  bottom: number;
  right: number;
  left: number;
};
// prettier-ignore
export type FilterSpecification = [
  'has',
  string
] | [
  '!has',
  string
] | [
  '==',
  string,
    string | number | boolean
] | [
  '!=',
  string,
    string | number | boolean
] | [
  '>',
  string,
    string | number | boolean
] | [
  '>=',
  string,
    string | number | boolean
] | [
  '<',
  string,
    string | number | boolean
] | [
  '<=',
  string,
    string | number | boolean
] | Array<string | FilterSpecification>;

export interface MapRefInstance {
  querySourceFeatures: (
    sourceId: string,
    filter?: FilterSpecification,
    sourceLayerIDs?: string[],
  ) => Promise<FeatureCollection> | undefined;
  fitBounds:
    | ((
        bounds: [number, number, number, number],
        paddingConfig?: PaddingOptions,
        animationDuration?: number,
      ) => void)
    | undefined;
}

export type VectorSourceProps = {
  id: string;
  url?: string;
  onPress?: (event: OnPressEvent) => void;
  onMouseMove?: (event: OnPressEvent) => void;
  children?: ReactElement | ReactElement[];
  promoteId?: string;
  hitbox?: { height: number; width: number };
};
export type FillLayerProps = {
  id: string;
  source?: string; // Web only
  sourceID?: string;
  sourceLayerID?: string;
  filter?: string[];
  style?: Record<string, unknown>;
  children?: ReactElement | ReactElement[];
};

export type LineLayerProps = {
  id: string;
  source?: string; // Web only
  sourceID?: string;
  sourceLayerID?: string;
  filter?: string[];
  style?: Record<string, unknown>;
};

export type MapConfigTypes = Record<
  'url' | 'source' | 'sourceLayer' | 'propertyLabel',
  string
>;

export type BoundsType = { ne: [number, number]; sw: [number, number] };

export type DistrictPropertiesTypes = {
  postcode_area: string;
  postcode_district: string;
  postcode_area_name: string;
  region: string;
  region_name: string;
};
