import React, { useMemo } from 'react';
import { Layer as MapGlLayer } from 'react-map-gl';
import type { FillLayerProps } from './Map.types';
import { convertToWebStyle } from './mapUtils';

export const FillLayer: React.FC<FillLayerProps> = ({
  sourceLayerID,
  style,
  source,
  ...props
}) => {
  const webStyle = useMemo(() => convertToWebStyle(style), [style]);
  return (
    <MapGlLayer
      {...props}
      type="fill"
      source={source}
      source-layer={sourceLayerID}
      paint={webStyle}
    />
  );
};
