import { kebabCase } from 'lodash';
import { PositionType, WebPositionType } from 'src/components/Map/Map.types';

// zoomCalculator is to be used when we need to use for eg. a radius circle
// using `import circle from '@turf/circle';` like `const radiusCircle = circle(center, radius, options);`
export const zoomCalculator = (radius: number, latitude: number): number =>
  Math.log2((1600 * Math.cos((latitude * Math.PI) / 180)) / radius) + 3.8; // log2 returns base 2 logarithm of a number and cos returns the cosine of a number in radians

export const convertToWebStyle = (
  style: Record<string, unknown> | undefined,
): Record<string, unknown> | undefined => {
  if (!style) {
    return undefined;
  }

  return Object.entries(style).reduce((acc, [key, value]) => {
    return {
      ...acc,
      [kebabCase(key)]: value,
    };
  }, {});
};

export const convertPositionToWebPosition = (
  position: PositionType,
): WebPositionType => {
  if (Object.prototype.hasOwnProperty.call(position, 'top')) {
    if (Object.prototype.hasOwnProperty.call(position, 'right')) {
      return 'top-right';
    }

    return 'top-left';
  }

  if (Object.prototype.hasOwnProperty.call(position, 'right')) {
    return 'bottom-right';
  }

  return 'bottom-left';
};
