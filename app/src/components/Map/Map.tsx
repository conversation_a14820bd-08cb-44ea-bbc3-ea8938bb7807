import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import Mapbox, { Camera } from '@rnmapbox/maps';
import { View } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { Spinner } from '@cat-home-experts/react-native-components';
import { config } from 'src/config';
import {
  FilterSpecification,
  MapProps,
  MapRefInstance,
  PaddingOptions,
} from './Map.types';
import { MAP_CONSTANTS } from './constants';

Mapbox.setAccessToken(config.mapBoxConfig.apiKey!);

/**
 * Map component for native - Do not import this directly,
 * use the lazy loaded version from src/components/Map/index.tsx
 */
export const Map = forwardRef<MapRefInstance, MapProps>(
  (
    {
      zoom,
      containerStyle,
      fadeDuration = 200,
      interactive = false,
      scaleControl = false,
      mapboxStyle,
      mapStyleURL = MAP_CONSTANTS.DEFAULT_MAP_STYLE,
      children,
      latitude = 54.4,
      longitude = 3.4,
      maxBounds,
      onMapIdle,
      onDidFinishLoadingMap,
      attributionPosition,
      logoPosition,
    },
    ref,
  ) => {
    const mapRef = useRef<Mapbox.MapView>(null);
    const cameraRef = useRef<Camera>(null);
    const [mapOpacity, setMapOpacity] = React.useState(0);

    useImperativeHandle(ref, () => ({
      querySourceFeatures: (
        sourceId: string,
        filter?: FilterSpecification,
        sourceLayerIDs?: string[],
      ) =>
        mapRef?.current?.querySourceFeatures(sourceId, filter, sourceLayerIDs),
      fitBounds: (
        bounds: [number, number, number, number],
        padding?: PaddingOptions,
        duration?: number,
      ) =>
        cameraRef?.current?.fitBounds(
          [bounds[2], bounds[3]],
          [bounds[0], bounds[1]],
          padding
            ? [padding.top, padding.bottom, padding.right, padding.left]
            : undefined,
          duration,
        ),
    }));

    const handleWillStartLoadingMap = () => {
      setMapOpacity(0.5);
    };

    const handleDidFinishLoadingMap = () => {
      setMapOpacity(1);
      onDidFinishLoadingMap?.();
    };

    return (
      <View style={[styles.container, containerStyle]}>
        <View style={styles.spinnerWrapper}>
          <Spinner />
        </View>
        <Mapbox.MapView
          style={[mapboxStyle, { opacity: mapOpacity }]}
          styleURL={mapStyleURL}
          zoomEnabled={interactive}
          pitchEnabled={interactive}
          scrollEnabled={interactive}
          rotateEnabled={interactive}
          scaleBarEnabled={scaleControl}
          ref={mapRef}
          onMapIdle={onMapIdle}
          onWillStartLoadingMap={handleWillStartLoadingMap}
          onDidFinishLoadingMap={handleDidFinishLoadingMap}
          attributionPosition={attributionPosition}
          logoPosition={logoPosition}
        >
          <Camera
            centerCoordinate={[longitude, latitude]}
            animationDuration={fadeDuration}
            zoomLevel={zoom}
            maxBounds={maxBounds}
            ref={cameraRef}
          />
          {children}
        </Mapbox.MapView>
      </View>
    );
  },
);

const styles = createMortarStyles(() => ({
  container: {
    flex: 1,
    backgroundColor: '#F5F1F0',
  },
  spinnerWrapper: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
