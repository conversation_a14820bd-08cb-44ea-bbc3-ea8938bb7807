import React, { useEffect } from 'react';
import { MapMouseEvent, Source as MapGlSource, useMap } from 'react-map-gl';
import { VectorSourceProps } from './Map.types';

export const VectorSource: React.FC<VectorSourceProps> = ({
  onPress,
  onMouseMove,
  id,
  url,
  ...props
}) => {
  const { current: mapRef } = useMap();

  useEffect(() => {
    const map = mapRef?.getMap();
    const callback = (e: MapMouseEvent) => {
      if (e.features) {
        onPress?.({
          features: e.features,
          coordinates: {
            latitude: e.lngLat.lat,
            longitude: e.lngLat.lng,
          },
          point: e.point,
        });
      }
    };

    map?.on('click', ['layer-fill', 'layer-highlighted'], callback);
    return () => {
      map?.off('click', ['layer-fill', 'layer-highlighted'], callback);
    };
  }, [mapRef, onPress]);

  useEffect(() => {
    const map = mapRef?.getMap();
    const callback = (e: MapMouseEvent) => {
      if (e.features) {
        onMouseMove?.({
          features: e.features,
          coordinates: {
            latitude: e.lngLat.lat,
            longitude: e.lngLat.lng,
          },
          point: e.point,
        });
      }
    };

    map?.on('mousemove', ['layer-fill', 'layer-highlighted'], callback);
  }, [mapRef, onMouseMove]);

  return <MapGlSource id={id} url={url} type="vector" {...props} />;
};
