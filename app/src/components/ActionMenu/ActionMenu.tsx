import React, { use<PERSON>allback, useMemo } from 'react';
import {
  FlatList,
  ListRenderItem,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { BottomSheetModalProps, BottomSheetProps } from '@gorhom/bottom-sheet';
import { useMediaQuery } from 'react-responsive';
import { Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type {
  NativeMortarIcon,
  NativeMortarIconProps,
} from '@cat-home-experts/mortar-types';

import { IS_WEB } from 'src/constants';
import { SheetOrModal } from 'src/components/SheetOrModal';
import { Spinner } from 'src/components/primitives/Spinner';

import { Separator } from '../primitives';

export interface ActionMenuItem {
  style?: TextStyle;
  label: string;
  testID?: string;
  action: (() => void) | (() => Promise<void>);
  onLongPress?: () => void;
  loading?: boolean;
  disabled?: boolean;
  removeAction?: boolean;
  href?: string;
  hrefAttrs?: Record<string, string>;
  selectable?: boolean;
  rightIcon?: NativeMortarIcon;
  rightIconProps?: NativeMortarIconProps;
}

export interface ActionMenuProps {
  visible: boolean;
  loading?: boolean;
  onDismiss: () => void;
  renderHeader?: () => ReturnType<React.FC>;
  sheetProps?: Partial<BottomSheetProps | BottomSheetModalProps>;
  actions: ActionMenuItem[];
  testID?: string;
}

export const ActionMenu: React.FC<ActionMenuProps> = ({
  actions,
  visible,
  loading = false,
  renderHeader,
  sheetProps,
  onDismiss,
  testID,
}) => {
  // Computed Values
  const { bottom } = useSafeAreaInsets();

  const isWebSmallScreen = useMediaQuery({ maxWidth: 400 });

  const alignment = actions.some((action) => action.rightIcon)
    ? 'spaceBetween'
    : 'center';

  // Renderers
  const renderItem: ListRenderItem<ActionMenuItem> = useCallback(
    ({ item }) => {
      const RightIcon = item.rightIcon;
      if (item.removeAction) {
        return null;
      }

      return (
        <TouchableOpacity
          onPress={item.action}
          onLongPress={item.onLongPress}
          style={[
            styles.actionButton,
            styles[alignment],
            item.loading && styles.loading,
          ]}
          testID={item.testID}
          disabled={item.loading || item.disabled}
          // @ts-expect-error - rn-web types not available
          href={item.href}
          hrefAttrs={item.hrefAttrs}
        >
          <Typography
            use="bodyMedium"
            style={[item.style, item.disabled && styles.disabled]}
            selectable={item.selectable}
          >
            {item.label}
          </Typography>
          {isTruthy(RightIcon) && isTruthy(item.rightIconProps) && (
            <RightIcon {...item.rightIconProps} />
          )}
        </TouchableOpacity>
      );
    },
    [alignment],
  );

  const headerComponent = useMemo(() => {
    if (!renderHeader) {
      return undefined;
    }

    return <View style={styles.header}>{renderHeader()}</View>;
  }, [renderHeader]);

  return (
    <SheetOrModal
      style={[
        styles.sheetOrModal,
        IS_WEB && !isWebSmallScreen && styles.minModalWidth,
        {
          paddingBottom: bottom,
        },
      ]}
      visible={visible}
      onDismiss={onDismiss}
      sheetProps={{
        ...sheetProps,
        enableDynamicSizing: true,
      }}
    >
      <FlatList
        style={styles.list}
        data={actions}
        renderItem={renderItem}
        ItemSeparatorComponent={Separator}
        ListHeaderComponent={headerComponent}
        scrollEnabled={false}
        testID={testID}
      />
      {loading && (
        <Animated.View
          style={[StyleSheet.absoluteFill, styles.overlay, styles.center]}
          entering={FadeIn.duration(150)}
          exiting={FadeOut.duration(150)}
        >
          <Spinner size={48} />
        </Animated.View>
      )}
    </SheetOrModal>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  sheetOrModal: {
    padding: 0,
    overflow: 'hidden',
  },
  minModalWidth: {
    minWidth: spacing(46),
  },
  backdrop: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  list: {
    width: '100%',
  },
  actionButton: {
    paddingVertical: spacing(2),
    paddingHorizontal: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    width: '100%',
    flexDirection: 'row',
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: palette.mortar.tokenColorLighterGrey,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    padding: spacing(3),
    borderTopLeftRadius: spacing(1),
    borderTopRightRadius: spacing(1),
  },
  loading: {
    backgroundColor: palette.mortar.tokenColorLightGrey,
  },
  disabled: {
    color: palette.mortarV3.tokenNeutral500,
  },
  overlay: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  center: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  spaceBetween: {
    alignItems: 'center',
    justifyContent: 'space-between',
  },
}));
