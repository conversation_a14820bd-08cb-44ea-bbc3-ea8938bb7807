import React from 'react';
import { View } from 'react-native';
import { render, cleanup } from '@testing-library/react-native';
import { useRemoteConfigBoolean as useRemoteConfigBooleanMock } from 'src/hooks/useRemoteConfig';
import { useOverrideFlagStatus as useOverrideFlagStatusMock } from 'src/state/overrideFeatureFlag/hooks/useOverrideFlagStatus';
import { config } from 'src/config';
import { FeatureFlag } from './FeatureFlag';

jest.mock('src/hooks/useRemoteConfig', () => ({
  useRemoteConfigBoolean: jest.fn(),
  useRemoteConfigString: jest.fn(),
}));

jest.mock('src/state/overrideFeatureFlag/hooks/useOverrideFlagStatus', () => ({
  useOverrideFlagStatus: jest.fn(),
}));

jest.mock('src/config');

jest.mock('src/services/firebase/remoteConfigConstants', () => ({
  featureFlags: {
    test_key: {
      description: 'Test flag',
      defaultValue: false,
    },
  },
}));

describe('components | FeatureFlag', () => {
  const ENABLED_COMPONENT_ID = 'enabled_component';
  const DISABLED_COMPONENT_ID = 'disabled_component';
  const EnabledComponent = () => <View testID={ENABLED_COMPONENT_ID} />;
  const DisabledComponent = () => <View testID={DISABLED_COMPONENT_ID} />;

  beforeEach(() => {
    (useOverrideFlagStatusMock as jest.Mock).mockReturnValue(null);
    config.environmentName = 'staging';
  });

  afterEach(() => {
    jest.resetAllMocks();
    jest.restoreAllMocks();
    cleanup();
  });

  it('renders enabled component if flag enabled', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(true);
    const { getByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
        isFeatureComplete
      >
        {(isEnabled) =>
          isEnabled ? <EnabledComponent /> : <DisabledComponent />
        }
      </FeatureFlag>,
    );

    const enabledView = getByTestId(ENABLED_COMPONENT_ID);
    expect(enabledView).toBeDefined();
  });

  it('renders enabled component if flag enabled & without function', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(true);
    const { getByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
        isFeatureComplete
      >
        <EnabledComponent />
      </FeatureFlag>,
    );

    const enabledView = getByTestId(ENABLED_COMPONENT_ID);
    expect(enabledView).toBeDefined();
  });

  it('renders disabled component if flag disabled', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(false);
    const { getByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
        isFeatureComplete
      >
        {(isEnabled) =>
          isEnabled ? <EnabledComponent /> : <DisabledComponent />
        }
      </FeatureFlag>,
    );

    const disabledView = getByTestId(DISABLED_COMPONENT_ID);
    expect(disabledView).toBeDefined();
  });

  it('renders disabled component if flag disabled & without function', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(false);
    const { queryByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
        isFeatureComplete
      >
        <EnabledComponent />
      </FeatureFlag>,
    );

    const disabledView = queryByTestId(DISABLED_COMPONENT_ID);
    expect(disabledView).toBeNull();
  });

  it('renders disabled component if flag enabled, but isFeatureComplete=false & ENV == prod', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(true);
    config.environmentName = 'production';
    const { getByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
        isFeatureComplete={false}
      >
        {(isEnabled) =>
          isEnabled ? <EnabledComponent /> : <DisabledComponent />
        }
      </FeatureFlag>,
    );

    const disabledView = getByTestId(DISABLED_COMPONENT_ID);
    expect(disabledView).toBeDefined();
  });

  it('renders disabled component if flag enabled, but isFeatureComplete=true & ENV == prod', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(true);
    config.environmentName = 'production';
    const { getByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
        isFeatureComplete
      >
        {(isEnabled) =>
          isEnabled ? <EnabledComponent /> : <DisabledComponent />
        }
      </FeatureFlag>,
    );

    const enabledView = getByTestId(ENABLED_COMPONENT_ID);
    expect(enabledView).toBeDefined();
  });

  it('renders enabled component if flag disabled, but is in dev mode && enabledInDev is true', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(false);
    const { getByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
        enabledInDev
      >
        {(isEnabled) =>
          isEnabled ? <EnabledComponent /> : <DisabledComponent />
        }
      </FeatureFlag>,
    );

    const enabledView = getByTestId(ENABLED_COMPONENT_ID);
    expect(enabledView).toBeDefined();
  });

  it('renders enabled component if flag disabled, but useOverrideFlagStatusMock returns true', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(false);
    (useOverrideFlagStatusMock as jest.Mock).mockReturnValue(true);
    const { getByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
      >
        {(isEnabled) =>
          isEnabled ? <EnabledComponent /> : <DisabledComponent />
        }
      </FeatureFlag>,
    );

    const enabledView = getByTestId(ENABLED_COMPONENT_ID);
    expect(enabledView).toBeDefined();
  });

  it('renders disabled component if flag enabled, but useOverrideFlagStatusMock returns false', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(true);
    (useOverrideFlagStatusMock as jest.Mock).mockReturnValue(false);
    const { getByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
      >
        {(isEnabled) =>
          isEnabled ? <EnabledComponent /> : <DisabledComponent />
        }
      </FeatureFlag>,
    );

    const disabledView = getByTestId(DISABLED_COMPONENT_ID);
    expect(disabledView).toBeDefined();
  });

  it('renders disabled component if flag disabled, but useOverrideFlagStatusMock returns null', () => {
    (useRemoteConfigBooleanMock as jest.Mock).mockReturnValue(false);
    (useOverrideFlagStatusMock as jest.Mock).mockReturnValue(false);
    const { getByTestId } = render(
      <FeatureFlag
        // @ts-expect-error using a test key
        remoteConfigKey="test_key"
      >
        {(isEnabled) =>
          isEnabled ? <EnabledComponent /> : <DisabledComponent />
        }
      </FeatureFlag>,
    );

    const disabledView = getByTestId(DISABLED_COMPONENT_ID);
    expect(disabledView).toBeDefined();
  });
});
