import React from 'react';
import {
  type UseFeatureFlagOptions,
  useFeatureFlag,
} from 'src/hooks/useFeatureFlag';
import type { BooleanRemoteConfigKeys } from 'src/services/firebase/remoteConfigConstants';

type Props = {
  /**
   * Firebase Remote Config Boolean key to fetch
   */
  remoteConfigKey: BooleanRemoteConfigKeys;
  /**
   * Component to render if enabled.
   * Pass in a function as the child to render a placeholder or legacy page when disabled.
   *
   * @example - Only render if enabled
   * <FeatureFlag remoteConfigKey="enable_thing">
   *  <FancyFeature />
   * </FeatureFlag>
   *
   * @example - Conditional render
   * <FeatureFlag remoteConfigKey="enable_thing">
   *  {(isEnabled: boolean) => isEnabled ? <FancyFeature /> : <OldFeature />}
   * </FeatureFlag>
   */
  children:
    | ((isFeatureEnabled: boolean) => ReturnType<React.FC>)
    | ReturnType<React.FC>;
} & UseFeatureFlagOptions;

export function FeatureFlag({
  remoteConfigKey,
  isFeatureComplete,
  enabledInDev,
  children,
}: Props): ReturnType<React.FC> {
  const isEnabled = useFeatureFlag(remoteConfigKey, {
    isFeatureComplete,
    enabledInDev,
  });

  if (typeof children === 'function') {
    return children(isEnabled);
  }

  if (!isEnabled) {
    return null;
  }

  return <React.Fragment>{children}</React.Fragment>;
}
