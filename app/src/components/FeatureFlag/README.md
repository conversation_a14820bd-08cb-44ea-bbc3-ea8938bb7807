# FeatureFlag React Component

The `FeatureFlag` component provides the ability to show or hide a child react component dependant on the result of a [Firebase Remote Config](https://firebase.google.com/docs/remote-config) boolean value, or higher priority rule (e.g. staff override, or DEV mode).

Logical priority order:

1. Is the user in local DEV mode & `enabledInDev=true`?
2. Is `disabledInProd=true` & environment is prod? (if so the feature is always disabled)
3. Is the user internal staff & has enabled the feature override?
4. Is the remote config value of `remoteConfigKey` true?

## Related Links

- [Feature Flags Docs](https://checkatrade.atlassian.net/wiki/spaces/TRADE/pages/2839248912/Feature+Flags)
- [FeatureFlag](../../components/FeatureFlag)
- [useFeatureFlag](../useFeatureFlag)
- [useFeatureFlagWithBetaTesters](../../hooks/useFeatureFlagWithBetaTesters)
- [useIsBetaTester](../../hooks/useIsBetaTester)
- [LabFeatureFlag](../../components/LabFeatureFlag)
- [useLabFeatureFlag](../../components/useLabs)

## Usage

### Options

**Props**

- `remoteConfigKey` - Firebase Remote Config Boolean key to fetch
- `disabledInProd` - Disables the feature in production, even if the remote config value is true. (default false)
- `enabledInDev` - If running in local dev mode (`__DEV__ === true`) then the feature will be enabled. (default false)
- `children` - Component to render if enabled. Pass in a function as the child to render a placeholder or legacy page when disabled.

### Examples

#### Render if enabled

```ts
// AmazingFeature.flag.tsx
import { FeatureFlag } from 'src/components/FeatureFlag';
import { AmazingFeature } from './AmazingFeature';

export function AmazingFeature(): ReactElement {
  return (
    <FeatureFlag
      remoteConfigKey="enable_amazing_feature"
      enabledInDev
    >
      <AmazingFeature />
    </FeatureFlag>
  );
}
```

#### Conditional render

```ts
// AmazingFeature.flag.tsx
import { FeatureFlag } from 'src/components/FeatureFlag';
import { AmazingFeature as AmazingFeatureNew } from './AmazingFeature';
import { AmazingFeature as AmazingFeatureOld } from './AmazingFeature.old';

export function AmazingFeature(): ReactElement {
  return (
    <FeatureFlag
      remoteConfigKey="enable_amazing_feature"
      enabledInDev
    >
      {(isEnabled) =>
        isEnabled ? <AmazingFeatureNew /> : <AmazingFeatureOld />
      }
    </FeatureFlag>
  );
}
```
