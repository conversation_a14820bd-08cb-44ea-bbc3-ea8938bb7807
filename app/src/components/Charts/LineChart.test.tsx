import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { LineChart, LineData } from './LineChart';
import { initD3Mocks } from './mock';

describe('LineChart', () => {
  beforeAll(() => {
    initD3Mocks();
  });

  afterEach(cleanup);

  const mockData: LineData[] = [
    {
      name: 'Test Line',
      color: 'blue',
      data: [
        { date: new Date('2023-01-01'), value: 10 },
        { date: new Date('2023-02-01'), value: 20 },
        { date: new Date('2023-03-01'), value: 15 },
        { date: new Date('2023-04-01'), value: 30 },
        { date: new Date('2023-05-01'), value: 25 },
      ],
    },
  ];

  it('should render initial empty container while measuring', () => {
    const { getByTestId } = render(<LineChart lines={mockData} />);
    expect(getByTestId(LineChart.testIds.CONTAINER_EMPTY)).toBeTruthy();
  });

  it('should render chart after layout event', () => {
    const { getByTestId, queryByTestId, rerender } = render(
      <LineChart lines={mockData} />,
    );

    // Trigger layout event
    fireEvent(getByTestId(LineChart.testIds.CONTAINER_EMPTY), 'layout', {
      nativeEvent: { layout: { width: 300, height: 200 } },
    });

    rerender(<LineChart lines={mockData} />);

    // The empty container should no longer be present
    expect(queryByTestId(LineChart.testIds.CONTAINER_EMPTY)).toBeNull();

    // Check if the chart container is rendered
    expect(getByTestId(LineChart.testIds.CONTAINER)).toBeTruthy();
    expect(getByTestId(LineChart.testIds.TITLE)).toBeTruthy();
    expect(getByTestId(LineChart.testIds.SVG)).toBeTruthy();
    expect(getByTestId(LineChart.testIds.CONTENT_GROUP)).toBeTruthy();
  });

  it('should render chart with custom title', () => {
    const chartName = 'Custom Chart Name';
    const { getByTestId, rerender } = render(
      <LineChart lines={mockData} chartName={chartName} />,
    );

    // Trigger layout event
    fireEvent(getByTestId(LineChart.testIds.CONTAINER_EMPTY), 'layout', {
      nativeEvent: { layout: { width: 300, height: 200 } },
    });

    rerender(<LineChart lines={mockData} chartName={chartName} />);

    // Verify chart title
    expect(getByTestId(LineChart.testIds.TITLE).props.children).toBe(chartName);
  });

  it('should render grid lines, data lines, and data points', () => {
    const { getByTestId, getAllByTestId, rerender } = render(
      <LineChart lines={mockData} />,
    );

    // Trigger layout event
    fireEvent(getByTestId(LineChart.testIds.CONTAINER_EMPTY), 'layout', {
      nativeEvent: { layout: { width: 300, height: 200 } },
    });

    rerender(<LineChart lines={mockData} />);

    // Check for 5 horizontal grid lines (based on mock ticks)
    const gridLines = getAllByTestId(/line-chart-grid-y-\d+/);
    expect(gridLines.length).toBe(5);

    // Check for the line path
    expect(getByTestId('line-chart-line-0')).toBeTruthy();

    // Check for the data points (5 in our mock data)
    const dataPoints = getAllByTestId(/line-chart-dot-0-\d+/);
    expect(dataPoints.length).toBe(5);

    // Check for X-axis labels
    const xLabels = getAllByTestId(/line-chart-x-label-\d+/);
    expect(xLabels.length).toBe(5);

    // Check for Y-axis labels
    const yLabels = getAllByTestId(/line-chart-y-label-\d+/);
    expect(yLabels.length).toBe(5);
  });

  it('should render scrollable chart when scrollable prop is true', () => {
    const { getByTestId, rerender } = render(
      <LineChart lines={mockData} scrollable />,
    );

    // Trigger layout event
    fireEvent(getByTestId('line-chart-container-empty'), 'layout', {
      nativeEvent: { layout: { width: 300, height: 200 } },
    });

    rerender(<LineChart lines={mockData} scrollable />);

    // Check for ScrollView
    expect(getByTestId('line-chart-scrollview')).toBeTruthy();
    expect(getByTestId('line-chart-scrollview-wrapper')).toBeTruthy();
  });

  it('should handle multiple lines', () => {
    const multiLineData: LineData[] = [
      ...mockData,
      {
        name: 'Second Line',
        color: 'red',
        data: [
          { date: new Date('2023-01-01'), value: 5 },
          { date: new Date('2023-02-01'), value: 15 },
          { date: new Date('2023-03-01'), value: 10 },
          { date: new Date('2023-04-01'), value: 25 },
          { date: new Date('2023-05-01'), value: 20 },
        ],
      },
    ];

    const { getByTestId, getAllByTestId, rerender } = render(
      <LineChart lines={multiLineData} />,
    );

    // Trigger layout event
    fireEvent(getByTestId('line-chart-container-empty'), 'layout', {
      nativeEvent: { layout: { width: 300, height: 200 } },
    });

    rerender(<LineChart lines={multiLineData} />);

    // Check for both line paths
    expect(getByTestId('line-chart-line-0')).toBeTruthy();
    expect(getByTestId('line-chart-line-1')).toBeTruthy();

    // Check for all data points (5 points per line * 2 lines = 10)
    const dots = getAllByTestId(/line-chart-dot-\d+-\d+/);
    expect(dots.length).toBe(10);
  });

  it('should respect custom height', () => {
    const customHeight = 400;
    const { getByTestId, rerender } = render(
      <LineChart lines={mockData} height={customHeight} />,
    );

    // Trigger layout event
    fireEvent(getByTestId('line-chart-container-empty'), 'layout', {
      nativeEvent: { layout: { width: 300, height: 200 } },
    });

    rerender(<LineChart lines={mockData} height={customHeight} />);

    // Check SVG height
    expect(getByTestId('line-chart-svg').props.height).toBe(customHeight);
  });

  it('should respect custom margins', () => {
    const customMargin = { top: 30, right: 30, bottom: 40, left: 50 };
    const { getByTestId, rerender } = render(
      <LineChart lines={mockData} margin={customMargin} />,
    );

    // Trigger layout event
    fireEvent(getByTestId('line-chart-container-empty'), 'layout', {
      nativeEvent: { layout: { width: 300, height: 200 } },
    });

    rerender(<LineChart lines={mockData} margin={customMargin} />);

    // The G element's x position should be customMargin.left + HORIZONTAL_PADDING
    expect(getByTestId('line-chart-content-group').props.x).toBe(
      customMargin.left + 10,
    );
    expect(getByTestId('line-chart-content-group').props.y).toBe(
      customMargin.top,
    );
  });
});
