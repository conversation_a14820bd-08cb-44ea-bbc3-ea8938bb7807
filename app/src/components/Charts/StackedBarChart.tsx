/* eslint-disable react/no-array-index-key */
import React, { useState, ReactElement } from 'react';
import { View, Text, LayoutChangeEvent, ScrollView } from 'react-native';
import Svg, { G, Path, Rect, Circle, Text as SvgText } from 'react-native-svg';
import * as d3 from 'd3';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import {
  tokenColorMediumGrey,
  tokenColorNavyBlue,
  tokenColorPrimaryRed,
  tokenColorPrimaryWhite,
  tokenColorSystemGreen,
  tokenColorSystemLinkBlue,
  tokenColorSystemOrange,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { Margin } from './types';

// Test IDs for the component
const TEST_IDS = createTestIds('bar-chart', {
  CONTAINER: 'container',
  CONTAINER_EMPTY: 'container-empty',
  SVG: 'svg',
  CHART_TITLE: 'chart-title',
  LEGEND_CONTAINER: 'legend-container',
  LEGEND_ITEM: 'legend-item',
  LEGEND_COLOR_BOX: 'legend-color-box',
  LEGEND_TEXT: 'legend-text',
  LEGEND_LINE_INDICATOR: 'legend-line-indicator',
  SCROLL_VIEW_WRAPPER: 'scroll-view-wrapper',
  SCROLL_VIEW: 'scroll-view',
  Y_AXIS_GRID: 'y-axis-grid',
  Y_AXIS_LABEL: 'y-axis-label',
  X_AXIS_GRID: 'x-axis-grid',
  X_AXIS_LABEL: 'x-axis-label',
  BAR_GROUP: 'bar-group',
  BAR_SEGMENT: 'bar-segment',
  LINE_PATH: 'line-path',
  LINE_MARKER: 'line-marker',
  LINE_LABEL: 'line-label',
});

export interface StackedBarSegment {
  key: string;
  value: number;
  color?: string;
}

export interface GenericStackedBarDataPoint {
  horizontalLineName: string;
  segments: StackedBarSegment[];
  lineValue?: number;
}

export interface StackedBarChartProps {
  chartName: string;
  data: GenericStackedBarDataPoint[];
  height?: number;
  margin?: Margin;
  lineColor?: string;
  defaultColors?: string[];
  scrollable?: boolean;
  fixedBarWidth?: number;
  overlayLineName?: string;
}

const defaultMargin: Margin = { top: 20, right: 20, bottom: 40, left: 34 };

export function StackedBarChart({
  chartName,
  data,
  height = 300,
  margin = defaultMargin,
  lineColor = tokenColorNavyBlue,
  defaultColors = [
    tokenColorSystemGreen,
    tokenColorPrimaryRed,
    tokenColorMediumGrey,
    tokenColorSystemOrange,
    tokenColorSystemLinkBlue,
  ],
  scrollable = false,
  fixedBarWidth,
  overlayLineName,
}: StackedBarChartProps): ReactElement {
  // Measure the parent container width.
  const [containerWidth, setContainerWidth] = useState<number>(0);

  const onLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setContainerWidth(width);
  };

  // Until containerWidth is measured, render an empty View that captures onLayout.
  if (containerWidth === 0) {
    return (
      <View
        testID={TEST_IDS.CONTAINER_EMPTY}
        onLayout={onLayout}
        style={{ height }}
      />
    );
  }

  // ------------------------ SCROLLABLE MODE ------------------------
  if (scrollable) {
    const spacing = 8; // fixed spacing between bars
    // Available width for bars inside the container (excluding margins)
    const availableInnerWidth = containerWidth - margin.left - margin.right;
    // If fixedBarWidth is provided, use it; otherwise, compute so that 5 bars fill the view.
    const barWidth =
      fixedBarWidth !== undefined
        ? fixedBarWidth
        : (availableInnerWidth - spacing * (5 - 1)) / 5;
    // Visible inner width for exactly 5 bars plus spacing.
    const visibleInnerWidth = barWidth * 5 + spacing * (5 - 1);
    // Visible width for the viewport: include only the left margin so that the right margin is clipped.
    const visibleWidth = visibleInnerWidth + margin.left;
    // Total inner chart width for all data points.
    const totalInnerChartWidth =
      barWidth * data.length + spacing * (data.length - 1);
    // Total SVG width including both margins.
    const totalSvgWidth = totalInnerChartWidth + margin.left + margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // Compute maximum stacked value.
    const maxStackValue =
      d3.max(data, (d) =>
        d.segments.reduce((sum, seg) => sum + seg.value, 0),
      ) ?? 0;

    // Y scale.
    const yScale = d3
      .scaleLinear()
      .domain([0, maxStackValue])
      .nice()
      .range([innerHeight, 0]);

    // Line scale (if line values exist).
    const lineValues = data
      .map((d) => d.lineValue)
      .filter((v) => v !== undefined) as number[];
    const hasLineData = lineValues.length > 0;
    const lineScale = hasLineData
      ? d3
          .scaleLinear()
          .domain([d3.min(lineValues) ?? 0, d3.max(lineValues) ?? 100])
          .range([innerHeight, 0])
      : null;

    const yTicks = yScale.ticks(5);

    // In scrollable mode, compute x positions manually:
    // For bar at index i, x = i * (barWidth + spacing)
    const xPosition = (i: number) => i * (barWidth + spacing);

    // Create a line generator using the index as the x accessor.
    const lineGenerator = hasLineData
      ? d3
          .line<GenericStackedBarDataPoint>()
          .x((_, i) => xPosition(i) + barWidth / 2)
          .y((d) => (lineScale ? lineScale(d.lineValue ?? 0) : innerHeight))
          .curve(d3.curveMonotoneX)
      : null;

    // Build the SVG element.
    const svgElement = (
      <Svg
        testID={TEST_IDS.SVG}
        width={totalSvgWidth}
        height={height}
        viewBox={`0 0 ${totalSvgWidth} ${height}`}
        preserveAspectRatio="xMidYMid meet"
      >
        {/* Transparent background */}
        <Rect
          x={0}
          y={0}
          width={totalSvgWidth}
          height={height}
          fill="transparent"
        />

        {/* Chart group offset by margins */}
        <G x={margin.left} y={margin.top}>
          {/* Horizontal grid lines and Y‑axis labels */}
          {yTicks.map((tick, i) => {
            const y = yScale(tick);
            return (
              <G key={`grid-y-${i}`} testID={`${TEST_IDS.Y_AXIS_GRID}-${i}`}>
                <Path
                  d={`M0,${y} L${totalInnerChartWidth},${y}`}
                  stroke={tokenColorPrimaryWhite}
                  strokeWidth={1}
                />
                <SvgText
                  testID={`${TEST_IDS.Y_AXIS_LABEL}-${i}`}
                  x={-10}
                  y={y + 3}
                  fontSize={10}
                  fill="black"
                  textAnchor="end"
                >
                  {tick}
                </SvgText>
              </G>
            );
          })}

          {/* Vertical grid lines, x‑axis indicator ticks and labels */}
          {data.map((d, i) => {
            const x = xPosition(i);
            const barCenter = x + barWidth / 2;
            return (
              <G key={`grid-x-${i}`} testID={`${TEST_IDS.X_AXIS_GRID}-${i}`}>
                <Path
                  d={`M${barCenter},0 L${barCenter},${innerHeight}`}
                  stroke={tokenColorPrimaryWhite}
                  strokeWidth={1}
                />
                {/* Small tick indicator line */}
                <Path
                  d={`M${barCenter},${innerHeight} L${barCenter},${innerHeight + 6}`}
                  stroke="black"
                  strokeWidth={1}
                />
                <SvgText
                  testID={`${TEST_IDS.X_AXIS_LABEL}-${i}`}
                  x={barCenter}
                  y={innerHeight + 21} // moved 6px further down than before
                  fontSize={10}
                  fill="black"
                  textAnchor="middle"
                  transform={`rotate(-20, ${barCenter}, ${innerHeight + 21})`}
                >
                  {d.horizontalLineName}
                </SvgText>
              </G>
            );
          })}

          {/* Stacked Bars */}
          {data.map((d, i) => {
            const x = xPosition(i);
            let cumulative = 0;
            return (
              <G key={`bar-group-${i}`} testID={`${TEST_IDS.BAR_GROUP}-${i}`}>
                {d.segments.map((seg, j) => {
                  const color =
                    seg.color || defaultColors[j % defaultColors.length];
                  const previousTotal = cumulative;
                  cumulative += seg.value;
                  const startY = yScale(previousTotal);
                  const endY = yScale(cumulative);
                  const segmentHeight = startY - endY;
                  return (
                    <Rect
                      key={`bar-${i}-${j}`}
                      testID={`${TEST_IDS.BAR_SEGMENT}-${i}-${j}`}
                      x={x}
                      y={endY}
                      width={barWidth}
                      height={segmentHeight}
                      fill={color}
                    />
                  );
                })}
              </G>
            );
          })}

          {/* Overlay Line (if provided) */}
          {hasLineData && lineGenerator && (
            <>
              <Path
                testID={TEST_IDS.LINE_PATH}
                d={lineGenerator(data) || ''}
                fill="none"
                stroke={lineColor}
                strokeWidth={2}
              />
              {data.map((d, i) => {
                if (typeof d.lineValue !== 'number') {
                  return null;
                }

                const x = xPosition(i) + barWidth / 2;
                const cy = lineScale ? lineScale(d.lineValue) : innerHeight;
                return (
                  <G
                    key={`line-marker-${i}`}
                    testID={`${TEST_IDS.LINE_MARKER}-${i}`}
                  >
                    <Circle
                      cx={x}
                      cy={cy}
                      r={4}
                      fill={lineColor}
                      stroke={tokenColorPrimaryWhite}
                      strokeWidth={1}
                    />
                    <SvgText
                      testID={`${TEST_IDS.LINE_LABEL}-${i}`}
                      x={x}
                      y={cy - 5}
                      fontSize={10}
                      fill={lineColor}
                      textAnchor="middle"
                    >
                      {d.lineValue}
                    </SvgText>
                  </G>
                );
              })}
            </>
          )}
        </G>
      </Svg>
    );

    // Wrap the ScrollView in a parent View that clips overflow.
    return (
      <View testID={TEST_IDS.CONTAINER} style={styles.container}>
        {/* Chart Title */}
        <Text testID={TEST_IDS.CHART_TITLE} style={styles.chartTitle}>
          {chartName}
        </Text>

        {/* Legend */}
        <View testID={TEST_IDS.LEGEND_CONTAINER} style={styles.legendContainer}>
          {data[0]?.segments.map((seg, j) => (
            <View
              key={`legend-${j}`}
              testID={`${TEST_IDS.LEGEND_ITEM}-${j}`}
              style={styles.legendItem}
            >
              <View
                testID={`${TEST_IDS.LEGEND_COLOR_BOX}-${j}`}
                style={[
                  styles.legendColorBox,
                  {
                    backgroundColor:
                      seg.color || defaultColors[j % defaultColors.length],
                  },
                ]}
              />
              <Text
                testID={`${TEST_IDS.LEGEND_TEXT}-${j}`}
                style={styles.legendText}
              >
                {seg.key}
              </Text>
            </View>
          ))}
          {hasLineData && (
            <View
              testID={`${TEST_IDS.LEGEND_ITEM}-line`}
              style={styles.legendItem}
            >
              <View
                testID={TEST_IDS.LEGEND_LINE_INDICATOR}
                style={styles.lineIndicatorContainer}
              >
                <View
                  style={[
                    styles.lineIndicatorDot,
                    { backgroundColor: lineColor },
                  ]}
                />
                <View
                  style={[
                    styles.lineIndicatorDotLine,
                    { backgroundColor: lineColor },
                  ]}
                />
              </View>
              <Text
                testID={`${TEST_IDS.LEGEND_TEXT}-line`}
                style={styles.legendText}
              >
                {overlayLineName ? overlayLineName : 'Overlay Line'}
              </Text>
            </View>
          )}
        </View>

        {/* The parent View has a fixed width (visibleWidth) and hides overflow */}
        <View
          testID={TEST_IDS.SCROLL_VIEW_WRAPPER}
          style={[{ width: visibleWidth }, styles.scrollViewWrapper]}
        >
          <ScrollView
            testID={TEST_IDS.SCROLL_VIEW}
            horizontal
            showsHorizontalScrollIndicator={false}
          >
            {svgElement}
          </ScrollView>
        </View>
      </View>
    );
  }

  // ------------------------ NON-SCROLLABLE MODE ------------------------
  // The chart fills the measured container width.
  const innerWidth = containerWidth - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;

  const xScale = d3
    .scaleBand<string>()
    .domain(data.map((d) => d.horizontalLineName))
    .range([0, innerWidth])
    .padding(0.1);

  const maxStackValue =
    d3.max(data, (d) => d.segments.reduce((sum, seg) => sum + seg.value, 0)) ??
    0;

  const yScale = d3
    .scaleLinear()
    .domain([0, maxStackValue])
    .nice()
    .range([innerHeight, 0]);

  const lineValues = data
    .map((d) => d.lineValue)
    .filter((v) => v !== undefined) as number[];
  const hasLineData = lineValues.length > 0;
  const lineScale = hasLineData
    ? d3
        .scaleLinear()
        .domain([d3.min(lineValues) ?? 0, d3.max(lineValues) ?? 100])
        .range([innerHeight, 0])
    : null;

  const yTicks = yScale.ticks(5);

  const lineGenerator = hasLineData
    ? d3
        .line<GenericStackedBarDataPoint>()
        .x((d) => {
          const bandX = xScale(d.horizontalLineName);
          return bandX != null ? bandX + xScale.bandwidth() / 2 : 0;
        })
        .y((d) => (lineScale ? lineScale(d.lineValue ?? 0) : innerHeight))
        .curve(d3.curveMonotoneX)
    : null;

  return (
    <View
      testID={TEST_IDS.CONTAINER}
      style={styles.container}
      onLayout={onLayout}
    >
      {/* Chart Title */}
      <Text testID={TEST_IDS.CHART_TITLE} style={styles.chartTitle}>
        {chartName}
      </Text>

      {/* Legend */}
      <View testID={TEST_IDS.LEGEND_CONTAINER} style={styles.legendContainer}>
        {data[0]?.segments.map((seg, j) => (
          <View
            key={`legend-${j}`}
            testID={`${TEST_IDS.LEGEND_ITEM}-${j}`}
            style={styles.legendItem}
          >
            <View
              testID={`${TEST_IDS.LEGEND_COLOR_BOX}-${j}`}
              style={[
                styles.legendColorBox,
                {
                  backgroundColor:
                    seg.color || defaultColors[j % defaultColors.length],
                },
              ]}
            />
            <Text
              testID={`${TEST_IDS.LEGEND_TEXT}-${j}`}
              style={styles.legendText}
            >
              {seg.key}
            </Text>
          </View>
        ))}
        {hasLineData && (
          <View
            testID={`${TEST_IDS.LEGEND_ITEM}-line`}
            style={styles.legendItem}
          >
            <View
              testID={TEST_IDS.LEGEND_LINE_INDICATOR}
              style={styles.lineIndicatorContainer}
            >
              <View
                style={[
                  styles.lineIndicatorDot,
                  { backgroundColor: lineColor },
                ]}
              />
              <View
                style={[
                  styles.lineIndicatorDotLine,
                  { backgroundColor: lineColor },
                ]}
              />
            </View>
            <Text
              testID={`${TEST_IDS.LEGEND_TEXT}-line`}
              style={styles.legendText}
            >
              {overlayLineName ? overlayLineName : 'Overlay Line'}
            </Text>
          </View>
        )}
      </View>

      <Svg
        testID={TEST_IDS.SVG}
        width={containerWidth}
        height={height}
        viewBox={`0 0 ${containerWidth} ${height}`}
        preserveAspectRatio="xMidYMid meet"
      >
        <Rect
          x={0}
          y={0}
          width={containerWidth}
          height={height}
          fill="transparent"
        />

        <G x={margin.left} y={margin.top}>
          {yTicks.map((tick, i) => {
            const y = yScale(tick);
            return (
              <G key={`grid-y-${i}`} testID={`${TEST_IDS.Y_AXIS_GRID}-${i}`}>
                <Path
                  d={`M0,${y} L${innerWidth},${y}`}
                  stroke={tokenColorPrimaryWhite}
                  strokeWidth={1}
                />
                <SvgText
                  testID={`${TEST_IDS.Y_AXIS_LABEL}-${i}`}
                  x={-10}
                  y={y + 3}
                  fontSize={10}
                  fill="black"
                  textAnchor="end"
                >
                  {tick}
                </SvgText>
              </G>
            );
          })}

          {data.map((d, i) => {
            const bandX = xScale(d.horizontalLineName);
            if (bandX == null) {
              return null;
            }

            const barCenter = bandX + xScale.bandwidth() / 2;
            return (
              <G key={`grid-x-${i}`} testID={`${TEST_IDS.X_AXIS_GRID}-${i}`}>
                <Path
                  d={`M${barCenter},0 L${barCenter},${innerHeight}`}
                  stroke={tokenColorPrimaryWhite}
                  strokeWidth={1}
                />
                {/* Small tick indicator */}
                <Path
                  d={`M${barCenter},${innerHeight} L${barCenter},${innerHeight + 6}`}
                  stroke="black"
                  strokeWidth={1}
                />
                <SvgText
                  testID={`${TEST_IDS.X_AXIS_LABEL}-${i}`}
                  x={barCenter}
                  y={innerHeight + 21}
                  fontSize={10}
                  fill="black"
                  textAnchor="middle"
                  transform={`rotate(-20, ${barCenter}, ${innerHeight + 21})`}
                >
                  {d.horizontalLineName}
                </SvgText>
              </G>
            );
          })}

          {data.map((d, i) => {
            const bandX = xScale(d.horizontalLineName);
            if (bandX == null) {
              return null;
            }

            let cumulative = 0;
            return (
              <G key={`bar-group-${i}`} testID={`${TEST_IDS.BAR_GROUP}-${i}`}>
                {d.segments.map((seg, j) => {
                  const color =
                    seg.color || defaultColors[j % defaultColors.length];
                  const previousTotal = cumulative;
                  cumulative += seg.value;
                  const startY = yScale(previousTotal);
                  const endY = yScale(cumulative);
                  const segmentHeight = startY - endY;
                  return (
                    <Rect
                      key={`bar-${i}-${j}`}
                      testID={`${TEST_IDS.BAR_SEGMENT}-${i}-${j}`}
                      x={bandX}
                      y={endY}
                      width={xScale.bandwidth()}
                      height={segmentHeight}
                      fill={color}
                    />
                  );
                })}
              </G>
            );
          })}

          {hasLineData && lineGenerator && (
            <>
              <Path
                testID={TEST_IDS.LINE_PATH}
                d={lineGenerator(data) || ''}
                fill="none"
                stroke={lineColor}
                strokeWidth={2}
              />
              {data.map((d, i) => {
                const bandX = xScale(d.horizontalLineName);
                if (bandX == null || typeof d.lineValue !== 'number') {
                  return null;
                }

                const cx = bandX + xScale.bandwidth() / 2;
                const cy = lineScale ? lineScale(d.lineValue) : innerHeight;
                return (
                  <G
                    key={`line-marker-${i}`}
                    testID={`${TEST_IDS.LINE_MARKER}-${i}`}
                  >
                    <Circle
                      cx={cx}
                      cy={cy}
                      r={4}
                      fill={lineColor}
                      stroke={tokenColorPrimaryWhite}
                      strokeWidth={1}
                    />
                    <SvgText
                      testID={`${TEST_IDS.LINE_LABEL}-${i}`}
                      x={cx}
                      y={cy - 5}
                      fontSize={12}
                      fontWeight={700}
                      fill={lineColor}
                      textAnchor="middle"
                    >
                      {d.lineValue}
                    </SvgText>
                  </G>
                );
              })}
            </>
          )}
        </G>
      </Svg>
    </View>
  );
}

StackedBarChart.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    width: '100%',
  },
  scrollViewWrapper: { overflow: 'hidden' },
  chartTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing(1),
    color: palette.mortarV3.tokenNeutral800,
  },
  legendContainer: {
    flexDirection: 'row',
    marginBottom: spacing(1.25),
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: spacing(1.25),
  },
  lineIndicatorContainer: {
    position: 'relative',
    width: spacing(3),
    height: spacing(1.5),
  },
  lineIndicatorDot: {
    position: 'absolute',
    width: spacing(1.5),
    height: spacing(1.5),
    borderRadius: spacing(1.5),
    left: spacing(0.75),
  },
  lineIndicatorDotLine: {
    position: 'absolute',
    width: spacing(3),
    height: 2,
    top: spacing(0.6),
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing(1.9),
    gap: spacing(0.8),
  },
  legendColorBox: {
    width: spacing(1.5),
    height: spacing(1.5),
    borderRadius: 2,
  },
  legendText: {
    fontSize: 12,
    color: palette.mortarV3.tokenNeutral800,
  },
}));
