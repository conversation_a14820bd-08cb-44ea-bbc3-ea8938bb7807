import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { StackedBarChart } from './StackedBarChart';
import { initD3Mocks } from './mock';

describe('StackedBarChart', () => {
  const mockData = [
    {
      horizontalLineName: 'Jan',
      segments: [
        { key: 'Complete', value: 30, color: '#00AA00' },
        { key: 'In Progress', value: 20, color: '#FF0000' },
      ],
      lineValue: 35,
    },
    {
      horizontalLineName: 'Feb',
      segments: [
        { key: 'Complete', value: 40, color: '#00AA00' },
        { key: 'In Progress', value: 15, color: '#FF0000' },
      ],
      lineValue: 45,
    },
    {
      horizontalLineName: 'Mar',
      segments: [
        { key: 'Complete', value: 50, color: '#00AA00' },
        { key: 'In Progress', value: 10, color: '#FF0000' },
      ],
      lineValue: 55,
    },
  ];

  beforeAll(() => {
    initD3Mocks();
  });

  afterEach(cleanup);

  it('renders an initial empty view while measuring container width', () => {
    const { getByTestId } = render(
      <StackedBarChart chartName="Test Chart" data={mockData} />,
    );
    // Initially, containerWidth is 0 so the component renders the empty view.
    expect(getByTestId(StackedBarChart.testIds.CONTAINER_EMPTY)).toBeTruthy();
  });

  it('renders the chart with correct title after layout', () => {
    const { getByTestId, getByText } = render(
      <StackedBarChart chartName="Test Chart" data={mockData} />,
    );
    // Fire the layout event on the empty container so that the chart renders.
    const emptyView = getByTestId(StackedBarChart.testIds.CONTAINER_EMPTY);
    fireEvent(emptyView, 'layout', {
      nativeEvent: { layout: { width: 300, height: 400 } },
    });
    // Now the main container should be rendered.
    expect(getByTestId(StackedBarChart.testIds.CONTAINER)).toBeTruthy();
    // And the chart title is visible.
    expect(getByText('Test Chart')).toBeTruthy();
  });

  it('renders the legend items correctly', () => {
    const { getByTestId, getByText } = render(
      <StackedBarChart chartName="Test Chart" data={mockData} />,
    );
    const emptyView = getByTestId(StackedBarChart.testIds.CONTAINER_EMPTY);
    fireEvent(emptyView, 'layout', {
      nativeEvent: { layout: { width: 300, height: 400 } },
    });
    // Check for legend texts.
    expect(getByText('Complete')).toBeTruthy();
    expect(getByText('In Progress')).toBeTruthy();
    expect(getByText('Overlay Line')).toBeTruthy();
  });

  it('renders custom overlay line name when provided', () => {
    const { getByTestId, getByText } = render(
      <StackedBarChart
        chartName="Test Chart"
        data={mockData}
        overlayLineName="Target"
      />,
    );
    const emptyView = getByTestId(StackedBarChart.testIds.CONTAINER_EMPTY);
    fireEvent(emptyView, 'layout', {
      nativeEvent: { layout: { width: 300, height: 400 } },
    });
    expect(getByText('Target')).toBeTruthy();
  });

  it('renders in scrollable mode when scrollable prop is true', () => {
    const { getByTestId } = render(
      <StackedBarChart
        chartName="Test Chart"
        data={mockData}
        scrollable={true}
      />,
    );
    const emptyView = getByTestId(StackedBarChart.testIds.CONTAINER_EMPTY);
    fireEvent(emptyView, 'layout', {
      nativeEvent: { layout: { width: 300, height: 400 } },
    });
    // In scrollable mode, check for the ScrollView by its test id.
    expect(getByTestId(StackedBarChart.testIds.SCROLL_VIEW)).toBeTruthy();
  });

  it('renders with fixed bar width when provided in scrollable mode', () => {
    const { getByTestId } = render(
      <StackedBarChart
        chartName="Test Chart"
        data={mockData}
        scrollable={true}
        fixedBarWidth={40}
      />,
    );
    const emptyView = getByTestId(StackedBarChart.testIds.CONTAINER_EMPTY);
    fireEvent(emptyView, 'layout', {
      nativeEvent: { layout: { width: 300, height: 400 } },
    });
    // Check that the SVG element is rendered.
    expect(getByTestId(StackedBarChart.testIds.SVG)).toBeTruthy();
  });

  it('renders chart with custom height when provided', () => {
    const customHeight = 500;
    const { getByTestId } = render(
      <StackedBarChart
        chartName="Test Chart"
        data={mockData}
        height={customHeight}
      />,
    );
    const emptyView = getByTestId(StackedBarChart.testIds.CONTAINER_EMPTY);
    fireEvent(emptyView, 'layout', {
      nativeEvent: { layout: { width: 300, height: 400 } },
    });
    // Get the SVG element and check its height prop.
    const svgElement = getByTestId(StackedBarChart.testIds.SVG);
    expect(svgElement.props.height).toBe(customHeight);
  });

  it('renders chart with custom colors when provided', () => {
    const customColors = ['#AABBCC', '#DDEEFF'];
    const customData = [
      {
        horizontalLineName: 'Jan',
        segments: [
          { key: 'Complete', value: 30 }, // Should use custom color since none is specified.
          { key: 'In Progress', value: 20 },
        ],
        lineValue: 35,
      },
    ];
    const { getByTestId } = render(
      <StackedBarChart
        chartName="Test Chart"
        data={customData}
        defaultColors={customColors}
      />,
    );
    const emptyView = getByTestId(StackedBarChart.testIds.CONTAINER_EMPTY);
    fireEvent(emptyView, 'layout', {
      nativeEvent: { layout: { width: 300, height: 400 } },
    });
    // Check legend color boxes for the custom colors using their test ids.
    const legendColorBox0 = getByTestId(
      `${StackedBarChart.testIds.LEGEND_COLOR_BOX}-0`,
    );
    const legendColorBox1 = getByTestId(
      `${StackedBarChart.testIds.LEGEND_COLOR_BOX}-1`,
    );
    expect(legendColorBox0.props.style).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ backgroundColor: customColors[0] }),
      ]),
    );
    expect(legendColorBox1.props.style).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ backgroundColor: customColors[1] }),
      ]),
    );
  });
});
