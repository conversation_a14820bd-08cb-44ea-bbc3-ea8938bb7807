/* eslint-disable react/no-array-index-key */
import React, { useState, ReactElement } from 'react';
import { View, Text, LayoutChangeEvent, ScrollView } from 'react-native';
import Svg, { G, Path, Circle, Text as SvgText, Rect } from 'react-native-svg';
import * as d3 from 'd3';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import {
  tokenColorLightGrey,
  tokenColorPrimaryWhite,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { Margin } from './types';

const HORIZONTAL_PADDING = 10;

export interface DataPoint {
  date: Date;
  value: number;
}

export interface LineData {
  name: string;
  color: string;
  data: DataPoint[];
}

export interface LineChartProps {
  chartName?: string;
  lines: LineData[];
  height?: number;
  margin?: Margin;
  scrollable?: boolean;
  fixedPointWidth?: number;
  visiblePoints?: number;
  yAxisLabelFormatter?: (value: number) => number | string;
  xAxisLabelTimeFormatSpecifier?: string;
}

const defaultMargin: Margin = { top: 20, right: 20, bottom: 30, left: 40 };

const TEST_IDS = createTestIds('line-chart', {
  CONTAINER_EMPTY: 'container-empty',
  SVG: 'svg',
  CONTENT_GROUP: 'content-group',
  GRID_Y: 'grid-y',
  LINE: 'line',
  DOT: 'dot',
  X_LABEL: 'x-label',
  Y_LABEL: 'y-label',
  SCROLLVIEW_WRAPPER: 'scrollview-wrapper',
  SCROLLVIEW: 'scrollview',
  CONTAINER: 'container',
  TITLE: 'title',
});

export function LineChart({
  chartName = 'Line Chart',
  lines,
  height = 300,
  margin = defaultMargin,
  scrollable = false,
  fixedPointWidth,
  visiblePoints = 5,
  yAxisLabelFormatter = (value: number) => value,
  xAxisLabelTimeFormatSpecifier = '%b',
}: LineChartProps): ReactElement {
  const [containerWidth, setContainerWidth] = useState<number>(0);

  const onLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setContainerWidth(width);
  };

  if (containerWidth === 0) {
    return (
      <View
        // eslint-disable-next-line react-native/no-inline-styles
        style={{ width: '100%', height }}
        onLayout={onLayout}
        testID={TEST_IDS.CONTAINER_EMPTY}
      />
    );
  }

  const innerHeight =
    height - margin.top - margin.bottom - HORIZONTAL_PADDING * 2;

  let xScale: d3.ScaleTime<number, number>;
  let totalSvgWidth: number;
  let innerWidth: number;

  if (scrollable) {
    // Compute effective spacing so that exactly visiblePoints items are visible in the viewport.
    const effectiveSpacing =
      fixedPointWidth !== undefined
        ? fixedPointWidth
        : (containerWidth - margin.left - margin.right) / (visiblePoints - 1);

    const numPoints = lines[0]?.data.length || 0;
    const totalInnerWidth = effectiveSpacing * (numPoints - 1);
    totalSvgWidth = totalInnerWidth + margin.left + margin.right;
    xScale = d3
      .scaleTime()
      .domain(
        d3.extent(lines.flatMap((line) => line.data.map((d) => d.date))) as [
          Date,
          Date,
        ],
      )
      .range([0, totalInnerWidth]);

    innerWidth = totalInnerWidth;
  } else {
    innerWidth =
      containerWidth - margin.left - margin.right - HORIZONTAL_PADDING * 2;
    totalSvgWidth = containerWidth;
    xScale = d3
      .scaleTime()
      .domain(
        d3.extent(lines.flatMap((line) => line.data.map((d) => d.date))) as [
          Date,
          Date,
        ],
      )
      .range([0, innerWidth]);
  }

  // Build the y-scale.
  const allValues = lines.flatMap((line) => line.data.map((d) => d.value));
  const yMin = Math.min(...allValues);
  const yMax = Math.max(...allValues);
  const yScale = d3
    .scaleLinear()
    .domain([yMin, yMax])
    .nice()
    .range([innerHeight, 0]);

  // Create a line generator.
  const lineGenerator = d3
    .line<DataPoint>()
    .x((d) => xScale(d.date))
    .y((d) => yScale(d.value))
    .curve(d3.curveMonotoneX);

  const svgContent = (
    <Svg
      testID={TEST_IDS.SVG}
      width={totalSvgWidth}
      height={height}
      viewBox={`0 0 ${totalSvgWidth} ${height}`}
      preserveAspectRatio="xMidYMid meet"
    >
      <Rect
        x={0}
        y={0}
        width={totalSvgWidth}
        height={height}
        fill="transparent"
      />
      <G
        x={margin.left + HORIZONTAL_PADDING}
        y={margin.top}
        testID={TEST_IDS.CONTENT_GROUP}
      >
        {/* Horizontal grid lines */}
        {yScale.ticks(5).map((tickVal, idx) => {
          const y = yScale(tickVal);
          return (
            <Path
              key={`grid-y-${idx}`}
              d={`M0,${y} L${innerWidth},${y}`}
              stroke={tokenColorLightGrey}
              strokeWidth={1}
              testID={`${TEST_IDS.GRID_Y}-${idx}`}
            />
          );
        })}

        {/* Each line path */}
        {lines.map((line, idx) => {
          const pathData = lineGenerator(line.data);
          return (
            <Path
              key={`line-${idx}`}
              d={pathData || ''}
              fill="none"
              stroke={line.color}
              strokeWidth={2}
              testID={`${TEST_IDS.LINE}-${idx}`}
            />
          );
        })}

        {/* Data point circles */}
        {lines.map((line, lineIdx) =>
          line.data.map((d, i) => {
            const cx = xScale(d.date);
            const cy = yScale(d.value);
            return (
              <Circle
                key={`dot-${lineIdx}-${i}`}
                cx={cx}
                cy={cy}
                r={4}
                fill={line.color}
                stroke={tokenColorPrimaryWhite}
                strokeWidth={2}
                testID={`${TEST_IDS.DOT}-${lineIdx}-${i}`}
              />
            );
          }),
        )}

        {/* X-axis labels: one per data point from the first line */}
        {lines[0].data.map((d, i) => {
          const x = xScale(d.date);
          return (
            <SvgText
              key={`x-label-${i}`}
              x={x}
              y={innerHeight + 15}
              fontSize={10}
              fill="black"
              textAnchor="middle"
              testID={`${TEST_IDS.X_LABEL}-${i}`}
            >
              {d3.timeFormat(xAxisLabelTimeFormatSpecifier)(d.date)}
            </SvgText>
          );
        })}

        {/* Y-axis labels */}
        {yScale.ticks(5).map((tickVal, idx) => {
          const y = yScale(tickVal);
          return (
            <SvgText
              key={`y-label-${idx}`}
              x={-10}
              y={y + 3}
              fontSize={10}
              fill="black"
              textAnchor="end"
              testID={`${TEST_IDS.Y_LABEL}-${idx}`}
            >
              {yAxisLabelFormatter(tickVal)}
            </SvgText>
          );
        })}
      </G>
    </Svg>
  );

  const chartContent = scrollable ? (
    <View
      // eslint-disable-next-line react-native/no-inline-styles
      style={{ width: containerWidth, overflow: 'hidden' }}
      testID={TEST_IDS.SCROLLVIEW_WRAPPER}
    >
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        testID={TEST_IDS.SCROLLVIEW}
      >
        {svgContent}
      </ScrollView>
    </View>
  ) : (
    svgContent
  );

  return (
    <View
      style={styles.container}
      onLayout={onLayout}
      testID={TEST_IDS.CONTAINER}
    >
      <Text style={styles.chartTitle} testID={TEST_IDS.TITLE}>
        {chartName}
      </Text>
      {chartContent}
    </View>
  );
}

LineChart.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    width: '100%',
  },
  chartTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing(1),
    color: palette.mortarV3.tokenNeutral800,
  },
}));
