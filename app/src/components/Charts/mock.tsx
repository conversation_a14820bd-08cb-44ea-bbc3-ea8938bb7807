import React from 'react';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      'mock-svg': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      > & {
        testID?: string;
        width?: number;
        height?: number;
        viewBox?: string;
        preserveAspectRatio?: string;
      };
      'mock-g': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      > & {
        testID?: string;
        x?: number;
        y?: number;
      };
      'mock-path': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      > & {
        testID?: string;
        d?: string;
        stroke?: string;
        strokeWidth?: number;
        fill?: string;
      };
      'mock-circle': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      > & {
        testID?: string;
        cx?: number;
        cy?: number;
        r?: number;
        fill?: string;
        stroke?: string;
        strokeWidth?: number;
      };
      'mock-text': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      > & {
        testID?: string;
        x?: number;
        y?: number;
        fontSize?: number;
        fontWeight?: number;
        fill?: string;
        textAnchor?: string;
        transform?: string;
      };
      'mock-rect': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      > & {
        testID?: string;
        x?: number;
        y?: number;
        width?: number;
        height?: number;
        fill?: string;
      };
    }
  }
}

interface SvgProps {
  children: React.ReactNode;
  testID?: string;
  width?: number;
  height?: number;
  viewBox?: string;
  preserveAspectRatio?: string;
}

interface GProps {
  children: React.ReactNode;
  testID?: string;
  x?: number;
  y?: number;
}

interface PathProps {
  d?: string;
  stroke?: string;
  strokeWidth?: number;
  fill?: string;
  testID?: string;
}

interface CircleProps {
  cx?: number;
  cy?: number;
  r?: number;
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  testID?: string;
}

interface TextProps {
  x?: number;
  y?: number;
  fontSize?: number;
  fontWeight?: number;
  fill?: string;
  textAnchor?: string;
  transform?: string;
  testID?: string;
  children: React.ReactNode;
}

interface RectProps {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  fill?: string;
  testID?: string;
}

export const initD3Mocks = (): void => {
  jest.mock('react-native-svg', () => {
    const mockSvg = ({ children, testID, ...props }: SvgProps) => (
      <mock-svg testID={testID} {...props}>
        {children}
      </mock-svg>
    );

    return {
      __esModule: true,
      default: mockSvg,
      G: ({ children, testID, x, y }: GProps) => (
        <mock-g testID={testID} x={x} y={y}>
          {children}
        </mock-g>
      ),
      Path: ({ d, stroke, strokeWidth, fill, testID }: PathProps) => (
        <mock-path
          d={d}
          stroke={stroke}
          strokeWidth={strokeWidth}
          fill={fill}
          testID={testID}
        />
      ),
      Circle: ({
        cx,
        cy,
        r,
        fill,
        stroke,
        strokeWidth,
        testID,
      }: CircleProps) => (
        <mock-circle
          cx={cx}
          cy={cy}
          r={r}
          fill={fill}
          stroke={stroke}
          strokeWidth={strokeWidth}
          testID={testID}
        />
      ),
      Text: ({
        x,
        y,
        fontSize,
        fontWeight,
        fill,
        textAnchor,
        transform,
        testID,
        children,
      }: TextProps) => (
        <mock-text
          x={x}
          y={y}
          fontSize={fontSize}
          fontWeight={fontWeight}
          fill={fill}
          textAnchor={textAnchor}
          transform={transform}
          testID={testID}
        >
          {children}
        </mock-text>
      ),
      Rect: ({ x, y, width, height, fill, testID }: RectProps) => (
        <mock-rect
          x={x}
          y={y}
          width={width}
          height={height}
          fill={fill}
          testID={testID}
        />
      ),
    };
  });

  type ScaleFunction = ((value: number | Date) => number) & {
    domain: () => ScaleFunction;
    range: () => ScaleFunction;
    nice: () => ScaleFunction;
    ticks: () => number[];
  };

  type ScaleBandFunction = ((value: string) => number | null) & {
    domain: (values: string[]) => ScaleBandFunction;
    range: (values: number[]) => ScaleBandFunction;
    padding: (value: number) => ScaleBandFunction;
    bandwidth: () => number;
  };

  type LineGenerator = ((data: unknown[]) => string) & {
    x: (arg: unknown) => LineGenerator;
    y: (arg: unknown) => LineGenerator;
    curve: (arg: unknown) => LineGenerator;
  };

  jest.mock('d3', () => {
    const createMockScale = (returnPoints: number[]): ScaleFunction => {
      const scale = ((value: number | Date): number => {
        if (typeof value === 'number') {
          return value * 2;
        }

        if (value instanceof Date) {
          return value.getMonth() * 30;
        }

        return 100;
      }) as ScaleFunction;

      scale.domain = jest.fn().mockReturnValue(scale);
      scale.range = jest.fn().mockReturnValue(scale);
      scale.nice = jest.fn().mockReturnValue(scale);
      scale.ticks = jest.fn().mockReturnValue(returnPoints);

      return scale;
    };

    const createMockScaleBand = (): ScaleBandFunction => {
      const scale = ((value: string): number | null => {
        // Return a predictable value based on the input string
        if (!value) {
          return null;
        }

        return value.length * 10;
      }) as ScaleBandFunction;

      scale.domain = jest.fn().mockReturnValue(scale);
      scale.range = jest.fn().mockReturnValue(scale);
      scale.padding = jest.fn().mockReturnValue(scale);
      scale.bandwidth = jest.fn().mockReturnValue(30);

      return scale;
    };

    const mockLineGenerator = (): LineGenerator => {
      const generator = ((_data: unknown[]): string =>
        'mock-path-data') as LineGenerator;
      generator.x = jest.fn().mockReturnValue(generator);
      generator.y = jest.fn().mockReturnValue(generator);
      generator.curve = jest.fn().mockReturnValue(generator);
      return generator;
    };

    return {
      scaleTime: jest.fn(() => createMockScale([0, 1, 2, 3, 4])),
      scaleLinear: jest.fn(() => createMockScale([0, 25, 50, 75, 100])),
      scaleBand: jest.fn(() => createMockScaleBand()),
      line: jest.fn(() => mockLineGenerator()),
      extent: jest
        .fn()
        .mockReturnValue([new Date('2023-01-01'), new Date('2023-12-01')]),
      curveMonotoneX: 'mock-curve',
      timeFormat: jest.fn().mockReturnValue(() => 'Jan'),
      max: jest.fn().mockImplementation((array, accessor) => {
        if (!array || !array.length) {
          return 0;
        }

        if (accessor) {
          return Math.max(...array.map(accessor));
        }

        return Math.max(...array);
      }),
      min: jest.fn().mockImplementation((array, accessor) => {
        if (!array || !array.length) {
          return 0;
        }

        if (accessor) {
          return Math.min(...array.map(accessor));
        }

        return Math.min(...array);
      }),
    };
  });
};
