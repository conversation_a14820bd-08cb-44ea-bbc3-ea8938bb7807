import React, { useMemo } from 'react';
import { View } from 'react-native';
import { Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { ActionMenu, ActionMenuItem } from 'src/components/ActionMenu';
import { IS_WEB } from 'src/constants';
import { DELETE_CONFIRMATION } from './constants';

const TEST_IDS = createTestIds('delete-confirmation', {
  CANCEL: 'cancel',
  DELETE: 'delete',
});

interface DeleteConfirmationProps {
  onDelete: () => void | Promise<void>;
  onClose: () => void;
  header?: string;
  deleteWarningText?: string;
  confirmationText?: string;
  isVisible: boolean;
  isRequestLoading?: boolean;
  showConfirm?: boolean;
  deleteLabel?: string;
  icon?: React.ReactNode;
}

export const DeleteConfirmation = ({
  onDelete,
  onClose,
  deleteWarningText,
  isVisible,
  isRequestLoading,
  showConfirm = true,
  deleteLabel = DELETE_CONFIRMATION.DELETE,
  confirmationText = DELETE_CONFIRMATION.ARE_YOU_SURE_YOU_WOULD_LIKE_TO,
  header = DELETE_CONFIRMATION.ARE_YOU_SURE,
  icon,
}: DeleteConfirmationProps): React.ReactElement => {
  // Computed Values
  const menuItems = useMemo<ActionMenuItem[]>(
    () =>
      [
        {
          style: styles.deleteButtonText,
          label: deleteLabel,
          testID: TEST_IDS.DELETE,
          action: onDelete,
          loading: isRequestLoading,
        },
        {
          label: DELETE_CONFIRMATION.CANCEL,
          testID: TEST_IDS.CANCEL,
          action: onClose,
        },
      ].filter(isTruthy),
    [deleteLabel, isRequestLoading, onClose, onDelete],
  );

  return (
    <ActionMenu
      visible={isVisible}
      onDismiss={onClose}
      actions={menuItems}
      renderHeader={() => (
        <View style={styles.warningTextWrapper}>
          <View style={styles.iconContainer}>{icon}</View>
          <Typography use="subHeader" style={styles.warningHeader}>
            {header}
          </Typography>
          <Typography style={styles.warningTextInnerWrapper}>
            {deleteWarningText && (
              <Typography style={[styles.warningText, styles.bold]}>
                {deleteWarningText}
              </Typography>
            )}
            {showConfirm && (
              <Typography style={styles.warningText}>
                {confirmationText}
              </Typography>
            )}
          </Typography>
        </View>
      )}
    />
  );
};

DeleteConfirmation.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  bold: {
    fontFamily: 'semi-bold',
  },
  warningText: {
    fontSize: spacing(2),
    lineHeight: spacing(3),
  },
  warningHeader: {
    fontSize: spacing(2.5),
    lineHeight: spacing(3.5),
    letterSpacing: -0.8,
  },
  warningTextWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    width: '100%',
    marginTop: !IS_WEB ? spacing(-2) : 0,
  },
  warningTextInnerWrapper: {
    textAlign: 'center',
    marginTop: 10,
  },
  deleteButtonText: { color: palette.mortar.tokenColorPrimaryRed },
  iconContainer: {
    marginVertical: spacing(2),
  },
}));
