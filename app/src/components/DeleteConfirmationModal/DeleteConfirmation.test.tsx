import React from 'react';
import { fireEvent, render, cleanup, act } from '@testing-library/react-native';

import { DeleteConfirmation } from './DeleteConfirmation';
import { DELETE_CONFIRMATION } from './constants';

jest.mock('@gorhom/bottom-sheet', () =>
  jest.requireActual('@gorhom/bottom-sheet/mock'),
);

const mockOnDelete = jest.fn();
const mockOnClose = jest.fn();

describe('Components | DeleteConfirmationModal | DeleteConfirmation', () => {
  afterEach(cleanup);

  it('can hide confirm text', () => {
    const { queryByText, getByText } = render(
      <DeleteConfirmation
        onDelete={mockOnDelete}
        onClose={mockOnClose}
        isVisible={true}
        isRequestLoading={false}
        showConfirm={false}
      />,
    );

    const header = getByText(DELETE_CONFIRMATION.DELETE);

    const confirmationText = queryByText(
      DELETE_CONFIRMATION.ARE_YOU_SURE_YOU_WOULD_LIKE_TO,
    );

    expect(header).toBeDefined();
    expect(confirmationText).toBeNull();
  });

  it('display an optional warning text', () => {
    const warningBody = 'warning';
    const { getByText } = render(
      <DeleteConfirmation
        onDelete={mockOnDelete}
        onClose={mockOnClose}
        isVisible={true}
        deleteWarningText={warningBody}
        isRequestLoading={false}
        showConfirm={false}
      />,
    );

    const body = getByText(warningBody);

    expect(body).toBeDefined();
  });

  it('can show default confirmation text', () => {
    const { getByText } = render(
      <DeleteConfirmation
        onDelete={mockOnDelete}
        onClose={mockOnClose}
        isVisible={true}
        isRequestLoading={false}
      />,
    );

    const confirmationText = getByText(
      DELETE_CONFIRMATION.ARE_YOU_SURE_YOU_WOULD_LIKE_TO,
    );

    expect(confirmationText).toBeDefined();
  });

  it('can override deletion label', () => {
    const customDeletionLabel = 'Custom delete';
    const { getByText } = render(
      <DeleteConfirmation
        onDelete={mockOnDelete}
        onClose={mockOnClose}
        isVisible={true}
        deleteLabel={customDeletionLabel}
        isRequestLoading={false}
      />,
    );

    const deletionLabel = getByText(customDeletionLabel);

    expect(deletionLabel).toBeDefined();

    act(() => fireEvent.press(deletionLabel));

    expect(mockOnDelete).toHaveBeenCalled();
  });
});
