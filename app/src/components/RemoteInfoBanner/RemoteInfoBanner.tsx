import React, { useMemo } from 'react';
import type { ViewStyle } from 'react-native';
import { z } from 'zod';
import { InfoBanner } from '@cat-home-experts/react-native-components';
import { useRemoteConfigString } from 'src/hooks/useRemoteConfig';

interface Props {
  style?: ViewStyle;
}

const RemoteInfoBannerSchema = z.object({
  level: z.union([
    z.literal('error'),
    z.literal('warn'),
    z.literal('info'),
    z.literal('ok'),
  ]),
  title: z.string(),
  body: z.string().optional(),
});

export const RemoteInfoBanner: React.FC<Props> = ({ style }) => {
  const remoteBannerData = useRemoteConfigString('remote_info_banner_data');
  // computed values
  const parsedBannerData = useMemo(() => {
    if (remoteBannerData) {
      const result = RemoteInfoBannerSchema.safeParse(
        JSON.parse(remoteBannerData),
      );
      if (result.success) {
        return result.data;
      }
    }

    return null;
  }, [remoteBannerData]);

  return parsedBannerData ? (
    <InfoBanner
      style={style}
      title={parsedBannerData.title}
      body={parsedBannerData.body}
      level={parsedBannerData.level}
    />
  ) : null;
};
