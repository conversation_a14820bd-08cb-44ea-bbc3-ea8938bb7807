import React from 'react';
import { View, type StyleProp, type ViewStyle } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { IS_WEB } from 'src/constants';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';

interface ContentSegmentProps extends React.PropsWithChildren {
  style?: StyleProp<ViewStyle>;
  testID?: string;
}

const TEST_IDS = { ROOT: 'content-segment' };

export const ContentSegment: React.FC<ContentSegmentProps> = ({
  children,
  style,
  testID,
}) => {
  const isDesktop = useDesktopMediaQuery();

  return (
    <View
      style={[styles.root, IS_WEB && isDesktop && styles.web, style]}
      testID={testID ?? TEST_IDS.ROOT}
    >
      {children}
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  root: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    padding: spacing(3),
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: palette.mortar.tokenColorLighterGrey,
    marginBottom: spacing(1),
  },
  web: {
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderRadius: spacing(1),
    overflow: 'hidden',
  },
}));
