import React from 'react';
import { useLabFeatureFlag } from 'src/hooks/useLabs/useLabFeatureFlag';

type LabFeatureFlagProps = {
  /**
   * Lab feature key to check if the feature is enabled
   */
  labFeatureKey: `labs_${string}`;
  /**
   * Component to render if enabled.
   */
  children:
    | ((isFeatureEnabled: boolean) => ReturnType<React.FC>)
    | ReturnType<React.FC>;
};

export const LabFeatureFlag: React.FC<LabFeatureFlagProps> = ({
  labFeatureKey,
  children,
}) => {
  const isLabFeatureEnabled = useLabFeatureFlag(labFeatureKey);

  if (typeof children === 'function') {
    return children(isLabFeatureEnabled);
  }

  if (!isLabFeatureEnabled) {
    return null;
  }

  return <React.Fragment>{children}</React.Fragment>;
};
