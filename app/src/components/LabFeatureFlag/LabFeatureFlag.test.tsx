import React from 'react';
import { Text } from 'react-native';
import { cleanup, render } from '@testing-library/react-native';
import { useLabFeatureFlag } from 'src/hooks/useLabs/useLabFeatureFlag';
import { LabFeatureFlag } from './LabFeatureFlag';

// Mocks
jest.mock('src/hooks/useLabs/useLabFeatureFlag', () => ({
  useLabFeatureFlag: jest.fn(),
}));

const useLabFeatureFlagMock = useLabFeatureFlag as jest.Mock;

describe('LabFeatureFlag Component', () => {
  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  it('renders child when feature is enabled', () => {
    useLabFeatureFlagMock.mockReturnValue(true);
    const child = <Text>{'Feature Enabled'}</Text>;
    const { getByText } = render(
      <LabFeatureFlag labFeatureKey="labs_test1">{child}</LabFeatureFlag>,
    );
    expect(getByText('Feature Enabled')).toBeTruthy();
  });

  it('renders function child when feature is disabled', () => {
    useLabFeatureFlagMock.mockReturnValue(false);
    const child = (isLabFeatureEnabled: boolean) => (
      <Text>{isLabFeatureEnabled ? 'Enabled' : 'Disabled'}</Text>
    );
    const { getByText } = render(
      <LabFeatureFlag labFeatureKey="labs_test1">{child}</LabFeatureFlag>,
    );
    expect(getByText('Disabled')).toBeTruthy();
  });

  it('renders function child when feature is enabled', () => {
    useLabFeatureFlagMock.mockReturnValue(true);
    const child = (isLabFeatureEnabled: boolean) => (
      <Text>{isLabFeatureEnabled ? 'Enabled' : 'Disabled'}</Text>
    );
    const { getByText } = render(
      <LabFeatureFlag labFeatureKey="labs_test1">{child}</LabFeatureFlag>,
    );
    expect(getByText('Enabled')).toBeTruthy();
  });

  it('renders nothing when feature is disabled and child is an element', () => {
    useLabFeatureFlagMock.mockReturnValue(false);
    const child = <div>{'Feature Enabled'}</div>;
    const { queryByText } = render(
      <LabFeatureFlag labFeatureKey="labs_test1">{child}</LabFeatureFlag>,
    );
    expect(queryByText('Feature Enabled')).toBeNull();
  });
});
