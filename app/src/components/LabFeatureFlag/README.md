# LabFeatureFlag React Component

The `LabFeatureFlag` component provides the ability to show or hide a child react component if the end-user enables or disables a Labs experiment.

## Related Links

- [Feature Flags Docs](https://checkatrade.atlassian.net/wiki/spaces/TRADE/pages/2839248912/Feature+Flags)
- [FeatureFlag](../../components/FeatureFlag)
- [useFeatureFlag](../useFeatureFlag)
- [useFeatureFlagWithBetaTesters](../../hooks/useFeatureFlagWithBetaTesters)
- [useIsBetaTester](../../hooks/useIsBetaTester)
- [LabFeatureFlag](../../components/LabFeatureFlag)
- [useLabFeatureFlag](../../components/useLabs)

## Usage

### Options

**Props**

- `labFeatureKey` - Lab feature key to check if the feature is enabled
- `children` - Component to render if enabled. Pass in a function as the child to render a placeholder or legacy page when disabled.

### Examples

#### Render if enabled

```ts
// AmazingFeature.flag.tsx
import { LabFeatureFlag } from 'src/components/LabFeatureFlag';
import { AmazingFeature } from './AmazingFeature';

export function AmazingFeature(): ReactElement {
  return (
    <LabFeatureFlag
      labFeatureKey="labs_amazing_feature"
    >
      <AmazingFeature />
    </LabFeatureFlag>
  );
}
```

#### Conditional render

```ts
// AmazingFeature.flag.tsx
import { LabFeatureFlag } from 'src/components/LabFeatureFlag';
import { AmazingFeature as AmazingFeatureNew } from './AmazingFeature';
import { AmazingFeature as AmazingFeatureOld } from './AmazingFeature.old';

export function AmazingFeature(): ReactElement {
  return (
    <LabFeatureFlag
      labFeatureKey="labs_amazing_feature"
    >
      {(isEnabled) =>
        isEnabled ? <AmazingFeatureNew /> : <AmazingFeatureOld />
      }
    </LabFeatureFlag>
  );
}
```
