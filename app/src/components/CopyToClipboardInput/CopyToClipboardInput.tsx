import React, { useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { copyTextToClipboard } from 'src/utilities/clipboard';
import { showToast } from '../primitives';

interface CopyToClipboardInputProps {
  value: string;
  onCopySuccess?: () => void;
  testID?: string;
}

const TEST_IDS = createTestIds('copy-to-clipboard-input', {});

export const CopyToClipboardInput: React.NativeFC<
  CopyToClipboardInputProps,
  typeof TEST_IDS
> = ({ value, onCopySuccess, testID = TEST_IDS.ROOT }) => {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = () => {
    try {
      copyTextToClipboard(value, {
        disableSnackbar: true,
      });
      onCopySuccess?.();
      setIsCopied(true);

      setTimeout(() => {
        setIsCopied(false);
      }, 3000);
    } catch (error) {
      showToast({
        text1: 'Failed to copy to clipboard',
        type: 'error',
      });
    }
  };

  return (
    <View style={styles.container} testID={testID}>
      <Typography
        useVariant="bodySMRegular"
        isMuted
        numberOfLines={1}
        style={styles.value}
      >
        {value}
      </Typography>
      <TouchableOpacity onPress={handleCopy}>
        <Typography
          useVariant="bodySMSemiBold"
          style={isCopied ? styles.copyButton : styles.copyButtonActive}
        >
          {isCopied ? 'Link copied' : 'Copy link'}
        </Typography>
      </TouchableOpacity>
    </View>
  );
};

CopyToClipboardInput.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing(1.5),
    paddingHorizontal: spacing(2),
    paddingVertical: spacing(1.5),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
    borderRadius: spacing(0.5),
    backgroundColor: palette.mortarV3.tokenNeutral0,
  },
  value: {
    flex: 1,
    userSelect: 'all',
  },
  copyButton: {
    color: palette.mortarV3.tokenNeutral600,
  },
  copyButtonActive: {
    color: palette.mortarV3.tokenDefault500,
  },
}));
