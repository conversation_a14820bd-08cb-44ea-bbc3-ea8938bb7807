import React from 'react';
import { copyTextToClipboard } from 'src/utilities/clipboard';
import { render, fireEvent, cleanup, act } from '@testing-library/react-native';
import { CopyToClipboardInput } from './CopyToClipboardInput';
import { showToast } from '../primitives';

jest.mock('src/utilities/clipboard', () => ({
  copyTextToClipboard: jest.fn(),
}));

jest.mock('../primitives', () => ({
  showToast: jest.fn(),
}));

describe('Components | CopyToClipboardInput', () => {
  const mockValue = 'https://example.com/payment-link';
  const mockOnCopySuccess = jest.fn();

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  it('renders correctly with provided value', () => {
    const { getByText } = render(<CopyToClipboardInput value={mockValue} />);

    expect(getByText(mockValue)).toBeTruthy();
    expect(getByText('Copy link')).toBeTruthy();
  });

  it('calls copyTextToClipboard with correct parameters when copy button is pressed', () => {
    const { getByText } = render(<CopyToClipboardInput value={mockValue} />);

    fireEvent.press(getByText('Copy link'));

    expect(copyTextToClipboard).toHaveBeenCalledWith(mockValue, {
      disableSnackbar: true,
    });
  });

  it('calls onCopySuccess callback when copy is successful', () => {
    const { getByText } = render(
      <CopyToClipboardInput
        value={mockValue}
        onCopySuccess={mockOnCopySuccess}
      />,
    );

    fireEvent.press(getByText('Copy link'));

    expect(mockOnCopySuccess).toHaveBeenCalled();
  });

  it('changes text to "Link copied" after successful copy', () => {
    const { getByText } = render(<CopyToClipboardInput value={mockValue} />);

    fireEvent.press(getByText('Copy link'));

    expect(getByText('Link copied')).toBeTruthy();
  });

  it('changes text to "Copy link" after 3 seconds', () => {
    const { getByText } = render(<CopyToClipboardInput value={mockValue} />);

    fireEvent.press(getByText('Copy link'));
    expect(getByText('Link copied')).toBeTruthy();

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(getByText('Copy link')).toBeTruthy();
  });

  it('shows error toast when copy fails', () => {
    (copyTextToClipboard as jest.Mock).mockImplementationOnce(() => {
      throw new Error('Copy failed');
    });

    const { getByText } = render(<CopyToClipboardInput value={mockValue} />);

    fireEvent.press(getByText('Copy link'));

    expect(showToast).toHaveBeenCalledWith({
      text1: 'Failed to copy to clipboard',
      type: 'error',
    });
  });

  it('does not change text to "Link copied" when copy fails', () => {
    (copyTextToClipboard as jest.Mock).mockImplementationOnce(() => {
      throw new Error('Copy failed');
    });

    const { getByText } = render(<CopyToClipboardInput value={mockValue} />);

    fireEvent.press(getByText('Copy link'));

    expect(getByText('Copy link')).toBeTruthy();
  });
});
