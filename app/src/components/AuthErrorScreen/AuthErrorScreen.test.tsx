import React from 'react';
import { render, cleanup, fireEvent } from '@testing-library/react-native';
import { useUserContext, useUserEvents } from 'src/hooks/useUser';
import type { User } from 'src/context/UserContext';
import { UserAccessType } from 'src/auth/auth.types';
import { deleteStoredSelectedCompanyId as deleteStoredSelectedCompanyIdMock } from 'src/auth/utils/getSelectedAccount';
import { AuthErrorScreen } from './AuthErrorScreen';
import { AUTH_ERROR_SCREEN_TEXT } from './constants';

jest.mock('src/services/analytics');
jest.mock('src/services/datadog');
jest.mock('src/utilities/appVersion');
jest.mock('src/hooks/useUser', () => ({
  useUserContext: jest.fn(),
  useUserEvents: jest.fn(),
}));
jest.mock('src/screens/AccountSwitcher', () => {
  const { View } = jest.requireActual('react-native');
  return {
    AccountSwitcher: View,
  };
});
jest.mock('src/auth/utils/getSelectedAccount', () => ({
  deleteStoredSelectedCompanyId: jest.fn(),
}));

describe('components | AuthErrorScreen', () => {
  const useUserContextMock = useUserContext as jest.Mock<
    ReturnType<typeof useUserContext>
  >;
  const useUserEventsMock = useUserEvents as jest.Mock<
    Partial<ReturnType<typeof useUserEvents>>
  >;
  const mockCompanyId = 1234;
  const mockSelectedAccount = {
    userId: '123',
    companyId: mockCompanyId,
    vettingStatus: UserAccessType.Active,
    isActive: true,
  };
  const mockUser: User = {
    catIdUid: 'uid',
    email: '<EMAIL>',
    accounts: [mockSelectedAccount],
  };
  const signOutUserMock = jest.fn();

  beforeEach(() => {
    useUserContextMock.mockReturnValue({
      isUserLoading: false,
      user: mockUser,
      selectedAccount: mockSelectedAccount,
      companyId: mockCompanyId,
    });
    useUserEventsMock.mockReturnValue({
      signOutUser: signOutUserMock,
    });
  });

  afterEach(() => {
    cleanup();
    jest.resetAllMocks();
  });

  test('should render the AuthErrorScreen component (single company)', () => {
    // Act
    const { getByText } = render(<AuthErrorScreen />);

    // Assert
    const title = getByText(AUTH_ERROR_SCREEN_TEXT.TITLE_NO_COMPANY_DATA);
    expect(title).toBeOnTheScreen();
  });

  test('should render the AuthErrorScreen component with multiple companies', () => {
    // Arrange
    const mockUserWithMultipleCompanies: User = {
      ...mockUser,
      accounts: [
        mockSelectedAccount,
        {
          userId: '123',
          companyId: 5678,
          vettingStatus: UserAccessType.Active,
          isActive: true,
        },
      ],
    };
    useUserContextMock.mockReturnValue({
      isUserLoading: false,
      user: mockUserWithMultipleCompanies,
      selectedAccount: mockSelectedAccount,
      companyId: mockCompanyId,
    });

    // Act
    const { getByText, getByTestId } = render(<AuthErrorScreen />);

    // Assert
    const title = getByText(AUTH_ERROR_SCREEN_TEXT.TITLE_NO_COMPANY_DATA);
    expect(title).toBeTruthy();
    const accountSwitcher = getByTestId(
      AuthErrorScreen.testIds!.ACCOUNT_SWITCHER,
    );
    expect(accountSwitcher).toBeTruthy();
    const body = getByText(
      `${AUTH_ERROR_SCREEN_TEXT.BODY_MULTI_COMPANIES}\n(companyID: ${mockCompanyId})`,
    );
    expect(body).toBeOnTheScreen();
  });

  test('should render the AuthErrorScreen component with multiple companies and no company data', () => {
    // Arrange
    const mockUserWithMultipleCompanies: User = {
      ...mockUser,
      accounts: [
        mockSelectedAccount,
        {
          userId: '123',
          companyId: 5678,
          vettingStatus: UserAccessType.Active,
          isActive: true,
        },
      ],
    };
    useUserContextMock.mockReturnValue({
      isUserLoading: false,
      user: mockUserWithMultipleCompanies,
      selectedAccount: mockSelectedAccount,
      companyId: mockCompanyId,
    });

    // Act
    const { getByText, getByTestId } = render(<AuthErrorScreen />);

    // Assert
    const title = getByText(AUTH_ERROR_SCREEN_TEXT.TITLE_NO_COMPANY_DATA);
    expect(title).toBeOnTheScreen();
    const accountSwitcher = getByTestId(
      AuthErrorScreen.testIds!.ACCOUNT_SWITCHER,
    );
    expect(accountSwitcher).toBeOnTheScreen();
    const body = getByText(
      `${AUTH_ERROR_SCREEN_TEXT.BODY_MULTI_COMPANIES}\n(companyID: ${mockCompanyId})`,
    );
    expect(body).toBeOnTheScreen();
  });

  test('should sign out and clear the selected company', async () => {
    // Act
    const { getByTestId } = render(<AuthErrorScreen />);

    // Assert
    const signOutBtn = getByTestId(AuthErrorScreen.testIds!.SIGN_OUT_BTN);
    expect(signOutBtn).toBeOnTheScreen();

    // Act
    await fireEvent.press(signOutBtn);

    // Assert
    expect(signOutUserMock).toHaveBeenCalledTimes(1);
    expect(deleteStoredSelectedCompanyIdMock).toHaveBeenCalledTimes(1);
  });
});
