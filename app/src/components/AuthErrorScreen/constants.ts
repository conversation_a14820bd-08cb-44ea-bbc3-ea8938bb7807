import type { User } from 'src/context/UserContext';

export const AUTH_ERROR_SCREEN_TEXT = {
  TITLE_NO_COMPANY_DATA: 'Company info unavailable',
  BODY_DEFAULT: 'Try again later or contact support if the issue persists.',
  BODY_MULTI_COMPANIES:
    'Try switching to a different company below. Alternatively try again later or contact support if the issue persists.',
  SIGN_OUT_BTN: 'Sign out',
};

export const createCompanyIdString = (companyId?: number): string =>
  companyId ? `(companyID: ${companyId})` : '';

export const getHasMultipleCompanies = (user?: User): boolean => {
  return (user?.accounts?.length || 0) > 1;
};
