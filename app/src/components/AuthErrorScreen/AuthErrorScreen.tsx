import React, { useEffect } from 'react';
import { View, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { deleteStoredSelectedCompanyId } from 'src/auth/utils/getSelectedAccount';
import { useUserContext, useUserEvents } from 'src/hooks/useUser';
import { AccountSwitcher } from 'src/screens/AccountSwitcher';
import { createAppVersionString } from 'src/utilities/appVersion';
import Logo from 'src/assets/images/logos/logo.png';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { captureLog } from 'src/services/datadog';
import {
  AUTH_ERROR_SCREEN_TEXT,
  getHasMultipleCompanies,
  createCompanyIdString,
} from './constants';

const testIds = createTestIds('auth-error', {
  ACCOUNT_SWITCHER: 'account-switcher',
  SIGN_OUT_BTN: 'sign-out-btn',
});

export const AuthErrorScreen: React.FC = () => {
  // Context
  const { user, companyId } = useUserContext();
  const { signOutUser } = useUserEvents();

  // Computed Values
  const hasMultipleCompanies = getHasMultipleCompanies(user);
  const companyDataString = createCompanyIdString(companyId);

  // Effects
  useEffect(() => {
    captureLog('info', EVENT_TYPE.AUTH_ERROR_SCREEN_VIEWED);
    logEvent(EVENT_TYPE.AUTH_ERROR_SCREEN_VIEWED);
  }, []);

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <View style={styles.container}>
        <Image source={Logo} style={styles.logo} resizeMode="contain" />
        <Typography style={styles.titleSummaryTxt} useVariant="header">
          {AUTH_ERROR_SCREEN_TEXT.TITLE_NO_COMPANY_DATA}
        </Typography>

        <Typography
          style={styles.titleSummaryTxt}
          useVariant="bodyRegular"
          isMuted
        >
          {hasMultipleCompanies
            ? `${AUTH_ERROR_SCREEN_TEXT.BODY_MULTI_COMPANIES}\n${companyDataString}`
            : `${AUTH_ERROR_SCREEN_TEXT.BODY_DEFAULT}\n${companyDataString}`}
        </Typography>
        {hasMultipleCompanies && (
          <AccountSwitcher
            location="bottom-tabs"
            testID={testIds.ACCOUNT_SWITCHER}
            style={styles.accountSwitcher}
          />
        )}
        <Button
          label={AUTH_ERROR_SCREEN_TEXT.SIGN_OUT_BTN}
          onPress={async () => {
            await deleteStoredSelectedCompanyId();
            await signOutUser();
          }}
          variant="secondary"
          testID={testIds.SIGN_OUT_BTN}
          style={styles.button}
        />
        <Typography useVariant="bodySmall" style={styles.appVersion}>
          {createAppVersionString()}
        </Typography>
      </View>
    </SafeAreaView>
  );
};

AuthErrorScreen.testIds = testIds;

const styles = createMortarStyles(({ spacing }) => ({
  safeAreaView: {
    flex: 1,
  },
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: '20%',
    paddingHorizontal: spacing(4),
  },
  logo: {
    width: '100%',
    aspectRatio: 64 / 9,
    maxWidth: 400,
    height: undefined,
    marginBottom: spacing(4),
  },
  titleSummaryTxt: {
    textAlign: 'center',
    marginBottom: spacing(2),
    maxWidth: 500,
  },
  accountSwitcher: {
    marginTop: spacing(2),
    marginBottom: spacing(6),
  },
  button: {
    width: '100%',
  },
  appVersion: {
    marginTop: spacing(3),
  },
}));
