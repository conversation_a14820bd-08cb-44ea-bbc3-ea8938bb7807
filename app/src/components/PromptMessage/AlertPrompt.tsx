import React, { ReactElement } from 'react';
import { View } from 'react-native';

import {
  Icon,
  NewTypographyVariants,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
  spacing,
} from '@cat-home-experts/react-native-utilities';

import { IconsId } from '@cat-home-experts/iconography';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { applyAlpha } from 'src/utilities/styles/applyAlpha';
import { ChevronRightSmall } from '@cat-home-experts/mortar-iconography-native';
import { tokenColorSecondary } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortar';
import { AlertTypes } from './types';

const TEST_IDS = createTestIds('alert-message', {});

interface AlertPromptProps {
  type: AlertTypes;
  title: string;
  children?: ReactElement;
  testID?: string;
  onPress?: () => void;
  titleVariant?: NewTypographyVariants;
  borderRadius?: number;
  leadingIcon?: IconsId;
  leadingIconColour?: string;
  bordered?: boolean;
}

export function AlertPrompt({
  type,
  title,
  children,
  testID,
  onPress,
  titleVariant = 'labelRegular',
  borderRadius = spacing(0.5),
  leadingIcon = 'info-fill',
  leadingIconColour,
  bordered = false,
}: AlertPromptProps): ReactElement {
  return (
    <TouchableOpacity onPress={onPress} disabled={!onPress}>
      <View
        testID={testID || TEST_IDS.ROOT}
        style={[
          styles.alertMessageContainer,
          bordered && styles.thinGreyBorder,
          { borderRadius },
          { backgroundColor: styles[type].backgroundColor },
        ]}
      >
        <View style={styles.iconContainer}>
          <Icon
            style={[
              styles.icon,
              { color: leadingIconColour ?? styles[type].color },
            ]}
            name={leadingIcon}
          />
        </View>
        <View style={styles.contentContainer}>
          <View style={styles.titleContainer}>
            <Typography style={styles.text} useVariant={titleVariant}>
              {title}
            </Typography>
          </View>
          {children}
        </View>
        {onPress && (
          <View style={styles.iconContainer}>
            <ChevronRightSmall color={tokenColorSecondary} size={20} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

AlertPrompt.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette }) => ({
  alertMessageContainer: {
    marginTop: spacing(1),
    paddingHorizontal: spacing(2),
    paddingVertical: spacing(1.5),
    flexDirection: 'row',
    gap: spacing(0.5),
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
  },
  text: { color: palette.mortar.tokenColorBlack, flex: 1 },
  info: {
    color: palette.mortar.tokenColorPrimaryBlue,
    backgroundColor: applyAlpha(palette.mortar.tokenColorPrimaryBlue, 0.1),
  },
  danger: {
    color: palette.mortar.tokenColorPrimaryRed,
    backgroundColor: applyAlpha(palette.mortar.tokenColorPrimaryRed, 0.1),
  },
  warning: {
    color: palette.mortar.tokenColorSystemOrange,
    backgroundColor: applyAlpha(palette.mortar.tokenColorSystemOrange, 0.1),
  },
  success: {
    color: palette.mortar.tokenColorSystemGreen,
    backgroundColor: applyAlpha(palette.mortar.tokenColorSystemGreen, 0.1),
  },
  icon: {
    marginRight: spacing(1),
  },
  thinGreyBorder: {
    borderWidth: 1,
    borderRadius: 8,
    borderColor: palette.mortarV3.tokenNeutral400,
  },
}));
