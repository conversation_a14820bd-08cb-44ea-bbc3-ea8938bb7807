import React from 'react';
import { View } from 'react-native';
import { render, cleanup, fireEvent } from '@testing-library/react-native';

import {
  tokenColorPrimaryBlue,
  tokenColorPrimaryRed,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { getFlattenedStyles } from '@cat-home-experts/react-native-utilities';

import { applyAlpha } from 'src/utilities/styles/applyAlpha';
import { PromptMessage } from './PromptMessage';

describe('Screen | Dashboard | widgets | ChannelPerformance | PromptMessage', () => {
  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('renders a the component', () => {
    const { getByTestId } = render(
      <PromptMessage type="info" title="test title" />,
    );

    expect(getByTestId(PromptMessage.testIds.ROOT)).toBeDefined();
  });

  it('renders a the component with custom Id', () => {
    const testId = 'test';
    const { getByTestId } = render(
      <PromptMessage type="info" title="test title" testID={testId} />,
    );

    expect(getByTestId(testId)).toBeDefined();
  });

  it('renders correct colors for info type', () => {
    const { getByTestId } = render(
      <PromptMessage type="info" title="test title" />,
    );

    const rootEl = getByTestId(PromptMessage.testIds.ROOT);
    const rootElStyles = getFlattenedStyles(rootEl);

    expect(rootElStyles).toMatchObject({
      backgroundColor: applyAlpha(tokenColorPrimaryBlue, 0.1),
    });
  });

  it('renders correct colors for danger type', () => {
    const { getByTestId } = render(
      <PromptMessage type="danger" title="test title" />,
    );

    const rootEl = getByTestId(PromptMessage.testIds.ROOT);
    const rootElStyles = getFlattenedStyles(rootEl);

    expect(rootElStyles).toMatchObject({
      backgroundColor: applyAlpha(tokenColorPrimaryRed, 0.1),
    });
  });

  it('can render a child element', () => {
    const childElementtestId = 'child-element-test-id';
    const { getByTestId } = render(
      <PromptMessage type="info" title="test title">
        <View testID={childElementtestId} />
      </PromptMessage>,
    );

    expect(getByTestId(childElementtestId)).toBeDefined();
  });

  it('calls function when onPress func is passed as a prop', () => {
    const promptMsgTestId = 'prompt-message-test-id';
    const mockOnPress = jest.fn();
    const { getByTestId } = render(
      <PromptMessage
        testID={promptMsgTestId}
        type="info"
        title="test title"
        onPress={mockOnPress}
      />,
    );
    fireEvent.press(getByTestId(promptMsgTestId));
    expect(mockOnPress).toHaveBeenCalled();
  });
});
