import React, { ReactElement } from 'react';
import { View } from 'react-native';

import {
  Icon,
  NewTypographyVariants,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
  spacing,
} from '@cat-home-experts/react-native-utilities';

import { IconsId } from '@cat-home-experts/iconography';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { applyAlpha } from 'src/utilities/styles/applyAlpha';
import { AlertTypes } from './types';

const TEST_IDS = createTestIds('alert-message', {});

interface AlertMessageProps {
  type: AlertTypes;
  title: string;
  children?: ReactElement;
  testID?: string;
  onPress?: () => void;
  titleVariant?: NewTypographyVariants;
  borderRadius?: number;
  leadingIcon?: IconsId;
  bordered?: boolean;
}

export function PromptMessage({
  type,
  title,
  children,
  testID,
  onPress,
  titleVariant = 'labelRegular',
  borderRadius = spacing(0.5),
  leadingIcon = 'info-fill',
  bordered = false,
}: AlertMessageProps): ReactElement {
  return (
    <TouchableOpacity onPress={onPress} disabled={!onPress}>
      <View
        testID={testID || TEST_IDS.ROOT}
        style={[
          styles.alertMessageContainer,
          bordered && styles.thinGreyBorder,
          { borderRadius },
          { backgroundColor: styles[type].backgroundColor },
        ]}
      >
        <View style={styles.titleContainer}>
          <Icon
            style={[styles.icon, { color: styles[type].color }]}
            name={leadingIcon}
          />
          <Typography style={styles.text} useVariant={titleVariant}>
            {title}
          </Typography>
        </View>
        {children}
      </View>
    </TouchableOpacity>
  );
}

PromptMessage.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette }) => ({
  alertMessageContainer: {
    marginTop: spacing(1),
    paddingHorizontal: spacing(2),
    paddingVertical: spacing(1.5),
    flexDirection: 'column',
    gap: spacing(0.5),
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: { color: palette.mortar.tokenColorBlack, flex: 1 },
  info: {
    color: palette.mortar.tokenColorPrimaryBlue,
    backgroundColor: applyAlpha(palette.mortar.tokenColorPrimaryBlue, 0.1),
  },
  danger: {
    color: palette.mortar.tokenColorPrimaryRed,
    backgroundColor: applyAlpha(palette.mortar.tokenColorPrimaryRed, 0.1),
  },
  warning: {
    color: palette.mortar.tokenColorSystemOrange,
    backgroundColor: applyAlpha(palette.mortar.tokenColorSystemOrange, 0.1),
  },
  success: {
    color: palette.mortar.tokenColorSystemGreen,
    backgroundColor: applyAlpha(palette.mortar.tokenColorSystemGreen, 0.1),
  },
  icon: {
    marginRight: spacing(1),
  },
  thinGreyBorder: {
    borderWidth: 1,
    borderRadius: 8,
    borderColor: palette.mortarV3.tokenNeutral400,
  },
}));
