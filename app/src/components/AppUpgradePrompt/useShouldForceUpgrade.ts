import { nativeApplicationVersion } from 'expo-application';
import { useRemoteConfigString } from 'src/hooks/useRemoteConfig';
import { compareVersion } from 'src/utilities/compareVersion';

export function useShouldForceUpgrade(): boolean {
  const minAppVersion = useRemoteConfigString('min_app_version');
  const shouldForceUpgrade =
    compareVersion(nativeApplicationVersion, minAppVersion) < 0;
  return shouldForceUpgrade;
}
