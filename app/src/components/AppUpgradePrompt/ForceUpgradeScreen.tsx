import React, { ReactElement } from 'react';
import { StyleSheet, View } from 'react-native';
import * as Linking from 'expo-linking';
import { APP_STORE_URL } from 'src/constants';
import {
  Button,
  Icon,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  tokenColorSystemOrange,
  tokenColorPrimaryWhite,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';

const rootTestId = 'force-upgrade';

const testIds = {
  ROOT: rootTestId,
  BUTTON: `${rootTestId}-upgrade-button`,
};

export const ForceUpgradeScreen = (): ReactElement => {
  return (
    <View style={styles.container} testID={testIds.ROOT}>
      <Icon
        name="warning-circle-fill"
        color={tokenColorSystemOrange}
        size={40}
      />
      <Typography use="header" style={styles.heading}>
        {'App update required'}
      </Typography>
      <Typography use="bodyRegular" style={styles.message}>
        {"This version of the app isn't supported anymore. You must update it "}
        {'before you can continue to use the app.'}
      </Typography>
      <Button
        label="Update App"
        onPress={() => {
          if (APP_STORE_URL) {
            Linking.openURL(APP_STORE_URL);
          }
        }}
        variant="secondary"
        style={styles.button}
        testID={testIds.BUTTON}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokenColorPrimaryWhite,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: '10%',
  },
  heading: {
    paddingBottom: 16,
    paddingTop: 20,
  },
  message: {
    textAlign: 'center',
    paddingBottom: 32,
  },
  button: {
    width: 240,
  },
});
