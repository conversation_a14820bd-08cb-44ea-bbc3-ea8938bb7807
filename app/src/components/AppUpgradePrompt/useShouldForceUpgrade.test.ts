import { renderHook, cleanup } from '@testing-library/react-native';
import { useRemoteConfigString } from 'src/hooks/useRemoteConfig';
import { useShouldForceUpgrade } from './useShouldForceUpgrade';

jest.mock('src/hooks/useRemoteConfig', () => ({
  useRemoteConfigString: jest.fn(() => '1.0.0'),
}));

describe('AppUpgradePrompt / useShouldForceUpgrade', () => {
  afterEach(cleanup);

  it('useShouldForceUpgrade / equal versions should return false', () => {
    jest.mock('expo-application', () => ({
      nativeApplicationVersion: '1.0.0',
    }));
    (useRemoteConfigString as jest.Mock).mockImplementationOnce(() => '1.0.0');

    const { result } = renderHook(() => useShouldForceUpgrade());
    expect(result.current).toBeFalsy();
  });

  it('useShouldForceUpgrade / app version is newer than min_app_version returns false', () => {
    jest.mock('expo-application', () => ({
      nativeApplicationVersion: '2.0.0',
    }));
    (useRemoteConfigString as jest.Mock).mockImplementationOnce(() => '1.0.0');

    const { result } = renderHook(() => useShouldForceUpgrade());
    expect(result.current).toBeFalsy();
  });

  it('useShouldForceUpgrade / app version is older than min_app_version returns true', () => {
    jest.mock('expo-application', () => ({
      nativeApplicationVersion: '1.0.0',
    }));
    (useRemoteConfigString as jest.Mock).mockImplementationOnce(() => '2.0.0');

    const { result } = renderHook(() => useShouldForceUpgrade());
    expect(result.current).toBeTruthy();
  });
});
