/* eslint-disable react-native/no-inline-styles */
import React, { ReactElement, useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import {
  Button,
  Icon,
  Typography,
} from '@cat-home-experts/react-native-components';
import * as Linking from 'expo-linking';
import { APP_STORE_URL } from 'src/constants';
import { tokenColorLighterGrey } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import {
  useMobileSmallScreenMediaQuery,
  useTabletMediaQuery,
} from 'src/hooks/useMediaQuery';
import { useShouldSuggestUpgrade } from './useShouldSuggestUpgrade';

const rootTestId = 'suggest-upgrade';

const testIds = {
  ROOT: rootTestId,
  BUTTON: `${rootTestId}-upgrade-button`,
  CLOSE_BUTTON: `${rootTestId}-close-button`,
};

export const SuggestBanner = (): ReactElement | null => {
  const shouldSuggestUpgrade = useShouldSuggestUpgrade();
  const [bannerVisible, setBannerVisible] = useState(true);
  const isMobileSmallScreen = useMobileSmallScreenMediaQuery();
  const isTablet = useTabletMediaQuery();

  return shouldSuggestUpgrade && bannerVisible ? (
    <View
      style={[
        styles.container,
        { paddingHorizontal: isMobileSmallScreen ? 16 : 24 },
      ]}
      testID={testIds.ROOT}
    >
      <Typography
        use="bodyRegular"
        style={[styles.bannerText, { fontSize: isTablet ? 16 : 14 }]}
      >
        {'A newer version of the app is available to use'}
      </Typography>
      <View style={styles.ctas}>
        <Button
          label="Update"
          onPress={() => {
            if (APP_STORE_URL) {
              Linking.openURL(APP_STORE_URL);
            }
          }}
          variant="secondary"
          size="small"
          style={styles.updateButton}
          testID={testIds.BUTTON}
        />
        <Pressable
          onPress={() => setBannerVisible(false)}
          style={styles.closeButton}
          testID={testIds.CLOSE_BUTTON}
        >
          <Icon name="cross" />
        </Pressable>
      </View>
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: tokenColorLighterGrey,
  },
  bannerText: {
    flex: 1,
  },
  ctas: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  updateButton: {
    marginLeft: 16,
  },
  closeButton: {
    marginLeft: 16,
  },
});
