import { nativeApplicationVersion } from 'expo-application';
import { useRemoteConfigString } from 'src/hooks/useRemoteConfig';
import { compareVersion } from 'src/utilities/compareVersion';

export function useShouldSuggestUpgrade(): boolean {
  const latestAppVersion = useRemoteConfigString('latest_app_version');
  const shouldSuggestUpgrade =
    compareVersion(nativeApplicationVersion, latestAppVersion) < 0;
  return shouldSuggestUpgrade;
}
