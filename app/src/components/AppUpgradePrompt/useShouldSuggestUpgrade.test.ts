import { renderHook, cleanup } from '@testing-library/react-native';
import { useRemoteConfigString } from 'src/hooks/useRemoteConfig';
import { useShouldSuggestUpgrade } from './useShouldSuggestUpgrade';

jest.mock('src/hooks/useRemoteConfig', () => ({
  useRemoteConfigString: jest.fn(() => '1.0.0'),
}));

describe('AppUpgradePrompt / useShouldSuggestUpgrade', () => {
  afterEach(cleanup);

  it('useShouldSuggestUpgrade / equal versions should return false', () => {
    jest.mock('expo-application', () => ({
      nativeApplicationVersion: '1.0.0',
    }));
    (useRemoteConfigString as jest.Mock).mockImplementationOnce(() => '1.0.0');

    const { result } = renderHook(() => useShouldSuggestUpgrade());
    expect(result.current).toBeFalsy();
  });

  it('useShouldSuggestUpgrade / app version is newer than latest_app_version returns false', () => {
    jest.mock('expo-application', () => ({
      nativeApplicationVersion: '2.0.0',
    }));
    (useRemoteConfigString as jest.Mock).mockImplementationOnce(() => '1.0.0');

    const { result } = renderHook(() => useShouldSuggestUpgrade());
    expect(result.current).toBeFalsy();
  });

  it('useShouldForceUpgrade / app version is older than latest_app_version returns true', () => {
    jest.mock('expo-application', () => ({
      nativeApplicationVersion: '1.0.0',
    }));
    (useRemoteConfigString as jest.Mock).mockImplementationOnce(() => '2.0.0');

    const { result } = renderHook(() => useShouldSuggestUpgrade());
    expect(result.current).toBeTruthy();
  });
});
