import React, { ReactElement, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { Modal, Typography } from '@cat-home-experts/react-native-components';
import {
  tokenColorBlack,
  tokenColorSystemGreen,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { getTestID } from 'src/utilities/testIds';
import { logEvent } from 'src/services/analytics';
import { COMPONENT_TYPE, ANALYTICS_ACTION_TYPE } from 'src/constants.events';
import { useMobileSmallScreenMediaQuery } from 'src/hooks/useMediaQuery';
import { SuccessModalProps } from './SuccessModal.types';

const rootTestId = 'success-modal';
const screenName = 'confirm';

const testIds = {
  ROOT: rootTestId,
  DISMISS_BUTTON: `${rootTestId}-dismiss-button`,
};

export const SuccessModal = ({
  onDismiss,
  parentScreenName,
  modalTitle,
  modalDescription,
  modalDescriptionEndInBold,
}: SuccessModalProps): ReactElement => {
  const isMobileSmallScreen = useMobileSmallScreenMediaQuery();

  useEffect(() => {
    logEvent(
      `${parentScreenName}_${screenName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
    );
  }, [parentScreenName]);

  return (
    <Modal
      buttons={[
        {
          buttonLabel: 'OK',
          buttonOnPress: onDismiss,
          testId: getTestID(testIds.DISMISS_BUTTON),
        },
      ]}
      icon="check-circle-fill"
      iconColor={tokenColorSystemGreen}
      onClose={onDismiss}
      testID={getTestID(testIds.ROOT)}
      title={modalTitle}
      visible
    >
      <Typography
        use="bodyRegular"
        style={[
          styles.modalMessage,
          // eslint-disable-next-line react-native/no-inline-styles
          { marginHorizontal: isMobileSmallScreen ? 12 : 24 },
        ]}
      >
        {modalDescription}
        {modalDescriptionEndInBold ? (
          <Typography use="bodyBold">{modalDescriptionEndInBold}</Typography>
        ) : null}
      </Typography>
    </Modal>
  );
};

SuccessModal.testIds = testIds;
SuccessModal.screenName = screenName;

const styles = StyleSheet.create({
  modalMessage: {
    textAlign: 'center',
    color: tokenColorBlack,
    marginTop: 16,
  },
});
