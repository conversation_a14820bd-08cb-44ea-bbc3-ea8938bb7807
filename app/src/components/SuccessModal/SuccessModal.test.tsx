import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';

import { ANALYTICS_ACTION_TYPE, COMPONENT_TYPE } from 'src/constants.events';

import { SuccessModal } from './SuccessModal';

const mockOnDismiss = jest.fn();

const mockLogEvents = jest.fn();

jest.mock('src/services/analytics', () => {
  return {
    logEvent: (...args: unknown[]) => mockLogEvents(...args),
  };
});

describe('Components | SuccessModal', () => {
  const parentScreenName = 'test-me-please';
  const defaultProps = {
    onDismiss: mockOnDismiss,
    parentScreenName,
    modalTitle: 'Test title',
    modalDescription: 'Test description',
    modalDescriptionEndInBold: 'Test bold description',
  };

  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('renders', () => {
    const { getByTestId } = render(<SuccessModal {...defaultProps} />);

    const successModal = getByTestId(SuccessModal.testIds.ROOT);

    expect(successModal).toBeDefined();
    expect(successModal.props.visible).toBe(true);
    expect(mockLogEvents).toHaveBeenNthCalledWith(
      1,
      `${parentScreenName}_${SuccessModal.screenName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
    );
  });

  it('can be dismissed ', () => {
    const { getByTestId } = render(<SuccessModal {...defaultProps} />);

    const dismissButton = getByTestId(SuccessModal.testIds.DISMISS_BUTTON);

    fireEvent.press(dismissButton);

    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });
});
