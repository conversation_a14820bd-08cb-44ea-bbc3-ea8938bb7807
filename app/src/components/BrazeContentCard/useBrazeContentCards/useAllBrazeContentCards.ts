import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLinkTo } from '@react-navigation/native';
import {
  Braze,
  type PlacementOptions,
  type ContentCard,
  ContentCardType,
} from 'src/services/braze';
import { extractPathFromURL } from 'src/utilities/extractPathFromURL';
import { openExternalLink } from 'src/utilities/openExternalLink';
import { isCheckatradeURL } from 'src/utilities/url';

export type UseAllBrazeContentCardProps = {
  placement: PlacementOptions;
  /** The type of content card to fetch.
   * If not provided, it defaults to ContentCardType.All.
   * */
  cardTypes?: ContentCardType | ContentCardType[];
};

type UseBrazeContentCardReturn = {
  contentCards: ContentCard[];
  handleBrazeContentCardPress: (contentCard: ContentCard) => void;
};

export const useAllBrazeContentCards = ({
  placement,
  cardTypes = ContentCardType.All,
}: UseAllBrazeContentCardProps): UseBrazeContentCardReturn => {
  const linkTo = useLinkTo();
  const [allContentCards, setAllContentCards] = useState<ContentCard[]>([]);
  const cardTypesRef = useRef(cardTypes);

  const contentCards: ContentCard[] | undefined = useMemo(
    () =>
      allContentCards.filter((item) => {
        if (item?.extras?.placement !== placement) {
          return false;
        }

        if (cardTypesRef.current === ContentCardType.All) {
          return true;
        }

        if (Array.isArray(cardTypesRef.current)) {
          return cardTypesRef.current.includes(item.type as ContentCardType);
        }

        return item.type === cardTypesRef.current;
      }),
    [allContentCards, placement, cardTypesRef],
  );

  const fetchContentCards = useCallback(async () => {
    const cachedContentCards = await Braze.getCachedContentCards();
    setAllContentCards(cachedContentCards);

    Braze.requestContentCardsRefresh();
  }, []);

  useEffect(() => {
    fetchContentCards();

    const removeSubscription = Braze.addContentCardListener(({ cards }) => {
      setAllContentCards(cards);
    });

    return () => {
      removeSubscription();
    };
  }, [fetchContentCards]);

  const handleBrazeContentCardPress = useCallback(
    (contentCard: ContentCard) => {
      if (contentCard && contentCard.url) {
        contentCard.logClick();

        if (isCheckatradeURL(contentCard.url)) {
          const path = extractPathFromURL(contentCard.url);
          if (path && path.length > 1) {
            linkTo(path);
          }
        } else {
          openExternalLink(contentCard.url);
        }
      }
    },
    [linkTo],
  );

  return {
    contentCards,
    handleBrazeContentCardPress,
  };
};
