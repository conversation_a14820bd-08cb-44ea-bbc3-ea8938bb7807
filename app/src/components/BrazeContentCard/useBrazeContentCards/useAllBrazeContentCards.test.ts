import { cleanup, renderHook, waitFor } from '@testing-library/react-native';
import { Braze, ContentCardType, type ContentCard } from 'src/services/braze';
import { useAllBrazeContentCards } from './useAllBrazeContentCards';

jest.mock('@react-navigation/native', () => ({
  useLinkTo: jest.fn(),
}));

jest.mock('src/services/braze', () => ({
  Braze: {
    addContentCardListener: jest.fn(),
    getCachedContentCards: jest.fn(),
    requestContentCardsRefresh: jest.fn(),
  },
  ContentCardType: {
    All: 'All',
    ImageOnly: 'ImageOnly',
    Captioned: 'Captioned',
  },
}));

describe('useAllBrazeContentCards', () => {
  jest.setTimeout(30000);

  const mockContentCard: ContentCard = {
    cardDescription:
      'Earn a £200 Amazon gift card each. Plus, you enter to win a Mercedes eVito van!',
    clicked: true,
    created: 1721221382,
    dismissed: false,
    dismissible: true,
    expiresAt: 1723640582,
    extras: { placement: 'active-home' },
    id: 'NjY5N2MwNWQ3OTI5ZjMxNTk3MTU5ZTAyXyRfY2M9MTM2MGY0N2MtZmI5Zi1kZWE1LTYzMTItYWQ5YmIwY2E2Y2JlJm12PTY2OTdjMDVkNzkyOWYzMTU5NzE1OWRmZSZwaT1jbXA=',
    image: 'https://example.com/original.jpg?1721221398',
    imageAspectRatio: 2.5546875,
    isControl: false,
    logClick: jest.fn(),
    logImpression: jest.fn(),
    logDismiss: jest.fn(),
    pinned: false,
    title: 'Refer a friend',
    type: 'Captioned',
    url: 'cattraderapp://offers',
    viewed: true,
  };

  beforeEach(() => {
    (Braze.getCachedContentCards as jest.Mock).mockResolvedValue([]);
    (Braze.addContentCardListener as jest.Mock).mockReturnValue(jest.fn());
  });
  afterEach(() => {
    cleanup();
    jest.resetAllMocks();
  });

  test('should return undefined contentCard initially', () => {
    // Arrange/Act
    const { result, unmount } = renderHook(() =>
      useAllBrazeContentCards({ placement: 'active-home' }),
    );

    // Assert
    expect(result.current.contentCards).toEqual([]);
    unmount();
  });

  it('renders all correct content cards for a given placement key', async () => {
    // Arrange
    const mocks = [
      mockContentCard,
      { ...mockContentCard, id: 'second-card' },
      { ...mockContentCard, id: 'third-card' },
      {
        ...mockContentCard,
        id: 'fourth-card',
        extras: { placement: 'another-key' },
      },
    ];

    (Braze.getCachedContentCards as jest.Mock).mockResolvedValue(mocks);

    const { result } = renderHook(() =>
      useAllBrazeContentCards({ placement: 'active-home' }),
    );

    const expectedContentCards = mocks.filter(
      (card) => card.extras.placement === 'active-home',
    );

    // Assert
    await waitFor(() =>
      expect(result.current.contentCards).toEqual(expectedContentCards),
    );
  });

  it('should return all content cards for a given placement key and a single card type', async () => {
    // Arrange
    const mocks = [
      mockContentCard,
      {
        ...mockContentCard,
        id: 'second-card',
        type: ContentCardType.ImageOnly,
      },
      { ...mockContentCard, id: 'third-card', type: ContentCardType.Classic },
      {
        ...mockContentCard,
        id: 'fourth-card',
        type: ContentCardType.ImageOnly,
      },
      {
        ...mockContentCard,
        id: 'fifth-card',
        extras: { placement: 'another-key' },
      },
    ];

    (Braze.getCachedContentCards as jest.Mock).mockResolvedValue(mocks);

    const { result } = renderHook(() =>
      useAllBrazeContentCards({
        placement: 'active-home',
        cardTypes: ContentCardType.ImageOnly,
      }),
    );

    const expectedContentCards = mocks.filter(
      (card) =>
        card.extras.placement === 'insights-banner' &&
        card.type === ContentCardType.ImageOnly,
    );

    // Assert
    await waitFor(() =>
      expect(result.current.contentCards).toEqual(expectedContentCards),
    );
  });

  it('should return all content cards for a given placement key and an array of card types', async () => {
    // Arrange
    const mocks = [
      mockContentCard,
      {
        ...mockContentCard,
        id: 'second-card',
        type: ContentCardType.ImageOnly,
      },
      { ...mockContentCard, id: 'third-card', type: ContentCardType.Classic },
      {
        ...mockContentCard,
        id: 'fourth-card',
        type: ContentCardType.ImageOnly,
      },
      {
        ...mockContentCard,
        id: 'fifth-card',
        extras: { placement: 'another-key' },
      },
    ];

    (Braze.getCachedContentCards as jest.Mock).mockResolvedValue(mocks);

    const { result } = renderHook(() =>
      useAllBrazeContentCards({
        placement: 'active-home',
        cardTypes: [ContentCardType.Captioned, ContentCardType.ImageOnly],
      }),
    );

    const expectedContentCards = mocks.filter(
      (card) =>
        card.extras.placement === 'insights-banner' &&
        (card.type === ContentCardType.Captioned ||
          card.type === ContentCardType.ImageOnly),
    );

    // Assert
    await waitFor(() =>
      expect(result.current.contentCards).toEqual(expectedContentCards),
    );
  });
});
