import {
  act,
  cleanup,
  renderHook,
  waitFor,
} from '@testing-library/react-native';
import { Braze, type ContentCard } from 'src/services/braze';
import { useBrazeContentCards } from './useBrazeContentCards';

jest.mock('src/services/braze', () => ({
  Braze: {
    addContentCardListener: jest.fn(),
    getCachedContentCards: jest.fn(),
    requestContentCardsRefresh: jest.fn(),
  },
}));

describe('Components | BrazeContentCard | useBrazeContentCards', () => {
  jest.setTimeout(30000);

  const mockContentCard: ContentCard = {
    cardDescription:
      'Earn a £200 Amazon gift card each. Plus, you enter to win a Mercedes eVito van!',
    clicked: true,
    created: 1721221382,
    dismissed: false,
    dismissible: true,
    expiresAt: 1723640582,
    extras: { placement: 'active-home' },
    id: 'NjY5N2MwNWQ3OTI5ZjMxNTk3MTU5ZTAyXyRfY2M9MTM2MGY0N2MtZmI5Zi1kZWE1LTYzMTItYWQ5YmIwY2E2Y2JlJm12PTY2OTdjMDVkNzkyOWYzMTU5NzE1OWRmZSZwaT1jbXA=',
    image: 'https://example.com/original.jpg?1721221398',
    imageAspectRatio: 2.5546875,
    isControl: false,
    logClick: jest.fn(),
    logImpression: jest.fn(),
    logDismiss: jest.fn(),
    pinned: false,
    title: 'Refer a friend',
    type: 'Captioned',
    url: 'cattraderapp://offers',
    viewed: true,
  };

  beforeEach(() => {
    (Braze.getCachedContentCards as jest.Mock).mockResolvedValue([]);
    (Braze.addContentCardListener as jest.Mock).mockReturnValue(jest.fn());
  });
  afterEach(() => {
    cleanup();
    jest.resetAllMocks();
  });

  test('should return undefined contentCard initially', () => {
    // Arrange/Act
    const { result, unmount } = renderHook(() =>
      useBrazeContentCards({ placement: 'active-home' }),
    );

    // Assert
    expect(result.current.contentCard).toBeUndefined();
    unmount();
  });

  test('should return specified content card for a given placement', async () => {
    // Arrange
    (Braze.getCachedContentCards as jest.Mock).mockResolvedValue([
      mockContentCard,
    ]);

    // Act
    const { result } = renderHook(() =>
      useBrazeContentCards({ placement: 'active-home' }),
    );

    // Assert
    await waitFor(() =>
      expect(result.current.contentCard).toEqual(mockContentCard),
    );
  });

  test('should NOT return specified content card if placement is unknown', async () => {
    // Arrange
    (Braze.getCachedContentCards as jest.Mock).mockResolvedValue([
      mockContentCard,
    ]);

    // Act
    const { result } = renderHook(() =>
      // @ts-expect-error - testing unknown placement
      useBrazeContentCards({ placement: 'unknown-placement' }),
    );

    // Assert
    await waitFor(() => {
      expect(Braze.getCachedContentCards).toHaveBeenCalledTimes(1);
      expect(Braze.requestContentCardsRefresh).toHaveBeenCalledTimes(1);
    });
    await waitFor(() => expect(result.current.contentCard).toBeUndefined());
  });

  test('should update the content card if the listener is triggered', async () => {
    // Arrange
    let listener: (payload: { cards: ContentCard[] }) => void = () => {
      // noop
    };

    const removeSubscription = jest.fn();
    (Braze.addContentCardListener as jest.Mock).mockImplementation(
      (callback) => {
        listener = callback;
        return removeSubscription;
      },
    );
    (Braze.getCachedContentCards as jest.Mock).mockResolvedValue([
      mockContentCard,
    ]);

    // Act
    const { result, unmount } = renderHook(() =>
      useBrazeContentCards({ placement: 'active-home' }),
    );

    // Assert
    await waitFor(() =>
      expect(result.current.contentCard).toEqual(mockContentCard),
    );

    // Act
    const newContentCard = { ...mockContentCard, title: 'New title' };
    act(() => {
      listener({ cards: [newContentCard] });
    });

    // Assert
    await waitFor(() =>
      expect(result.current.contentCard).toEqual(newContentCard),
    );

    // Cleanup
    unmount();
    expect(removeSubscription).toHaveBeenCalledTimes(1);
  });
});
