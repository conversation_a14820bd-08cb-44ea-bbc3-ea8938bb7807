import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Braze,
  type PlacementOptions,
  type ContentCard,
} from 'src/services/braze';

export type UseBrazeContentCardProps = {
  placement: PlacementOptions;
};

type UseBrazeContentCardReturn = {
  contentCard: ContentCard | undefined;
};

export const useBrazeContentCards = ({
  placement,
}: UseBrazeContentCardProps): UseBrazeContentCardReturn => {
  const [allContentCards, setAllContentCards] = useState<ContentCard[]>();
  const contentCard = useMemo(
    () =>
      allContentCards?.find((item) => item?.extras?.placement === placement),
    [allContentCards, placement],
  );

  const fetchContentCards = useCallback(async () => {
    const cachedContentCards = await Braze.getCachedContentCards();
    setAllContentCards(cachedContentCards);

    Braze.requestContentCardsRefresh();
  }, []);

  useEffect(() => {
    fetchContentCards();

    const removeSubscription = Braze.addContentCardListener(({ cards }) => {
      setAllContentCards(cards);
    });

    return () => {
      removeSubscription();
    };
  }, [fetchContentCards]);

  return { contentCard };
};
