import { ContentCard } from 'src/services/braze';

export const mockContentCard: ContentCard = {
  cardDescription:
    'Earn a £200 Amazon gift card each. Plus, you enter to win a Mercedes eVito van!',
  clicked: true,
  created: 1721221382,
  dismissed: false,
  dismissible: true,
  expiresAt: 1723640582,
  extras: { placement: 'active-home' },
  id: 'NjY5N2MwNWQ3OTI5ZjMxNTk3MTU5ZTAyXyRfY2M9MTM2MGY0N2MtZmI5Zi1kZWE1LTYzMTItYWQ5YmIwY2E2Y2JlJm12PTY2OTdjMDVkNzkyOWYzMTU5NzE1OWRmZSZwaT1jbXA=',
  image: 'https://example.com/original.jpg?1721221398',
  imageAspectRatio: 2.5546875,
  isControl: false,
  logClick: jest.fn(),
  logImpression: jest.fn(),
  logDismiss: jest.fn(),
  pinned: false,
  title: 'Refer a friend',
  type: 'Captioned',
  url: 'cattraderapp://offers',
  viewed: true,
  linkText: 'Learn more',
};
