import React, { ReactElement } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { Typography } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { BRAZE_CONTENT_CARD_COPY } from './constants';

type TextBlockProps = {
  title: string;
  description: string;
  linkText?: string;
  style?: StyleProp<ViewStyle>;
};

export const TextBlock = ({
  title,
  description,
  linkText,
  style,
}: TextBlockProps): ReactElement => {
  return (
    <View style={[styles.textContainer, style]}>
      <Typography
        useVariant="subHeadingSemiBold"
        style={styles.title}
        numberOfLines={4}
      >
        {title}
      </Typography>
      <Typography useVariant="bodySMRegular" numberOfLines={8}>
        {description}
      </Typography>
      <Typography style={styles.link} useVariant="textLinkSMSemiBold">
        {linkText || BRAZE_CONTENT_CARD_COPY.SEE_MORE}
      </Typography>
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  textContainer: {
    paddingHorizontal: spacing(2),
    paddingVertical: spacing(1.5),
  },
  title: {
    color: palette.mortarV3.tokenDefault800,
    marginBottom: spacing(0.5),
  },
  link: {
    color: palette.mortarV3.tokenDefault500,
    marginTop: spacing(0.2),
  },
}));
