import React, { ReactElement } from 'react';
import { Image, ImageStyle, StyleProp } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { ScreenBreakpoints } from 'src/hooks/useMediaQuery/ScreenBreakpoints';
import { useIsAtMostSmallScreenWidth } from 'src/hooks/useMediaQuery';

type ImageBlockProps = {
  url: string;
  aspectRatio: number;
  style?: StyleProp<ImageStyle>;
};

export const ImageBlock = ({
  url,
  aspectRatio,
  style,
}: ImageBlockProps): ReactElement => {
  const isMobileScreen = useIsAtMostSmallScreenWidth();

  return (
    <Image
      source={{ uri: url }}
      style={[
        isMobileScreen && styles.image,
        !isMobileScreen && styles.imageLargeScreen,
        { aspectRatio: aspectRatio },
        style,
      ]}
    />
  );
};

const IMAGE_MAX_WIDTH_SMALL_SCREEN = ScreenBreakpoints.Medium - 1;

const styles = createMortarStyles(() => ({
  image: {
    width: '100%',
    maxWidth: IMAGE_MAX_WIDTH_SMALL_SCREEN,
  },
  imageLargeScreen: {
    width: '50%',
  },
}));
