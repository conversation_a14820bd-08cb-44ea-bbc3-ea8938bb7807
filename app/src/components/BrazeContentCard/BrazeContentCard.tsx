import React, { ReactElement, useCallback, useState } from 'react';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';
import { Cross } from '@cat-home-experts/mortar-iconography-native';
import { StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';
import { extractPathFromURL } from 'src/utilities/extractPathFromURL';
import { isCheckatradeURL } from 'src/utilities/url';
import { openExternalLink } from 'src/utilities/openExternalLink';
import { useIsAtMostSmallScreenWidth } from 'src/hooks/useMediaQuery';
import { ScreenBreakpoints } from 'src/hooks/useMediaQuery/ScreenBreakpoints';
import { useFocusEffect, useLinkTo } from '@react-navigation/native';
import { TextBlock } from './TextBlock';
import { ImageBlock } from './ImageBlock';
import {
  useBrazeContentCards,
  type UseAllBrazeContentCardProps,
} from './useBrazeContentCards';

type BrazeContentCardProps = UseAllBrazeContentCardProps & {
  style?: StyleProp<ViewStyle>;
};

const TEST_IDS = createTestIds('braze-content-card', {
  PRESSABLE: 'pressable',
  DISMISS_BUTTON: 'dismiss-button',
});

export const BrazeContentCard = ({
  style,
  placement,
}: BrazeContentCardProps): ReactElement | null => {
  const [dismissedNow, setDismissedNow] = useState(false);

  const { contentCard } = useBrazeContentCards({
    placement,
  });
  const isMobileScreen = useIsAtMostSmallScreenWidth();
  const linkTo = useLinkTo();

  useFocusEffect(
    useCallback(() => {
      contentCard?.logImpression();
    }, [contentCard]),
  );

  const handleOnPress = useCallback(() => {
    if (contentCard && contentCard.url) {
      contentCard.logClick();

      if (isCheckatradeURL(contentCard.url)) {
        const path = extractPathFromURL(contentCard.url);
        if (path && path.length > 1) {
          linkTo(path);
        }
      } else {
        openExternalLink(contentCard.url);
      }
    }
  }, [contentCard, linkTo]);

  const handleDismiss = useCallback(() => {
    setDismissedNow(true);
    contentCard?.logDismiss();
  }, [contentCard]);

  if (!contentCard || contentCard.dismissed || dismissedNow) {
    return null;
  }

  return (
    <View testID={TEST_IDS.ROOT} style={[styles.container, style]}>
      <View
        style={[
          styles.innerContainer,
          !isMobileScreen && styles.largeScreenCardInnerContainer,
        ]}
      >
        {isTruthy(contentCard.image) &&
          isTruthy(contentCard.imageAspectRatio) && (
            <ImageBlock
              url={contentCard.image}
              aspectRatio={contentCard.imageAspectRatio}
            />
          )}
        <TouchableOpacity
          testID={TEST_IDS.PRESSABLE}
          style={styles.textBlock}
          onPress={handleOnPress}
        >
          <TextBlock
            title={contentCard.title}
            description={contentCard.cardDescription}
            linkText={contentCard.linkText}
            style={styles.textBlock}
          />
        </TouchableOpacity>
        {contentCard.dismissible && (
          <TouchableOpacity
            testID={TEST_IDS.DISMISS_BUTTON}
            onPress={handleDismiss}
            style={styles.closeButton}
          >
            <Cross color={staticPalette.mortarV3.tokenDefault700} size={14} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

BrazeContentCard.testIds = TEST_IDS;

const IMAGE_MAX_WIDTH_SMALL_SCREEN = ScreenBreakpoints.Medium - 1;
const CLOSE_BUTTON_SIZE = 28;

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    position: 'relative',
  },
  innerContainer: {
    flex: 1,
    maxWidth: IMAGE_MAX_WIDTH_SMALL_SCREEN,
    borderRadius: 8,
    backgroundColor: palette.mortarV3.tokenNeutral0,
    overflow: 'hidden',
  },
  largeScreenCardInnerContainer: {
    flexDirection: 'row-reverse',
    maxWidth: '100%',
  },
  textBlock: {
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    top: spacing(1),
    right: spacing(1),
    width: CLOSE_BUTTON_SIZE,
    height: CLOSE_BUTTON_SIZE,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: palette.mortarV3.tokenNeutral0,
    borderRadius: CLOSE_BUTTON_SIZE / 2,
  },
}));
