import React from 'react';
import { useLinkTo } from '@react-navigation/native';
import { cleanup, fireEvent, render } from '@testing-library/react-native';
import type { ContentCard } from 'src/services/braze';
import { useIsAtMostSmallScreenWidth } from 'src/hooks/useMediaQuery';
import { BrazeContentCard } from './BrazeContentCard';
import { useBrazeContentCards } from './useBrazeContentCards';

jest.mock('./useBrazeContentCards', () => ({
  useBrazeContentCards: jest.fn(),
}));

jest.mock('src/hooks/useMediaQuery', () => ({
  useIsAtMostSmallScreenWidth: jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  useLinkTo: jest.fn(),
  useFocusEffect: (callback: () => void) => callback(),
}));

describe('Components | BrazeContentCard', () => {
  jest.setTimeout(30000);

  const mockContentCard: ContentCard = {
    cardDescription:
      'Earn a £200 Amazon gift card each. Plus, you enter to win a Mercedes eVito van!',
    clicked: true,
    created: 1721221382,
    dismissed: false,
    dismissible: true,
    expiresAt: 1723640582,
    extras: { placement: 'active-home' },
    id: 'NjY5N2MwNWQ3OTI5ZjMxNTk3MTU5ZTAyXyRfY2M9MTM2MGY0N2MtZmI5Zi1kZWE1LTYzMTItYWQ5YmIwY2E2Y2JlJm12PTY2OTdjMDVkNzkyOWYzMTU5NzE1OWRmZSZwaT1jbXA=',
    image: 'https://example.com/original.jpg?1721221398',
    imageAspectRatio: 2.5546875,
    isControl: false,
    logClick: jest.fn(),
    logImpression: jest.fn(),
    logDismiss: jest.fn(),
    pinned: false,
    title: 'Refer a friend',
    type: 'Captioned',
    url: 'cattraderapp://offers',
    viewed: true,
  };

  beforeEach(() => {
    (useBrazeContentCards as jest.Mock).mockReturnValue({
      contentCard: mockContentCard,
    });
    (useIsAtMostSmallScreenWidth as jest.Mock).mockReturnValue(true);
    (useLinkTo as jest.Mock).mockReturnValue(jest.fn());
  });

  afterEach(() => {
    cleanup();
    jest.resetAllMocks();
  });

  test('content card renders correctly & logs impression', () => {
    // Arrange/Act
    const { getByTestId } = render(
      <BrazeContentCard placement="active-home" />,
    );

    // Assert
    const contentCard = getByTestId(BrazeContentCard.testIds.ROOT);
    expect(contentCard).toBeDefined();

    expect(mockContentCard.logImpression).toHaveBeenCalledTimes(1);
  });

  test('content card onPress opens correct url', () => {
    // Arrange
    const linkTo = jest.fn();
    (useLinkTo as jest.Mock).mockReturnValue(linkTo);

    const { getByTestId } = render(
      <BrazeContentCard placement="active-home" />,
    );

    // Act
    const contentCard = getByTestId(BrazeContentCard.testIds.PRESSABLE);
    fireEvent.press(contentCard);

    // Assert
    expect(linkTo).toHaveBeenCalledWith('/offers');
  });

  test('does not render if content card is undefined', () => {
    // Arrange
    (useBrazeContentCards as jest.Mock).mockReturnValue({
      contentCard: undefined,
    });

    // Act
    const { queryByTestId } = render(
      <BrazeContentCard placement="active-home" />,
    );

    // Assert
    const contentCard = queryByTestId(BrazeContentCard.testIds.ROOT);
    expect(contentCard).toBeNull();
  });

  test('does not render if content card is dismissed', () => {
    // Arrange
    (useBrazeContentCards as jest.Mock).mockReturnValue({
      contentCard: {
        ...mockContentCard,
        dismissed: true,
      },
    });

    // Act
    const { queryByTestId } = render(
      <BrazeContentCard placement="active-home" />,
    );

    // Assert
    const contentCard = queryByTestId(BrazeContentCard.testIds.ROOT);
    expect(contentCard).toBeNull();
  });

  test('calls logDismiss when close button pressed', () => {
    const logDismiss = jest.fn();
    // Arrange
    (useBrazeContentCards as jest.Mock).mockReturnValue({
      contentCard: {
        ...mockContentCard,
        logDismiss,
      },
    });

    // Act
    const { queryByTestId } = render(
      <BrazeContentCard placement="active-home" />,
    );

    // Assert
    const dismissButton = queryByTestId(
      BrazeContentCard.testIds.DISMISS_BUTTON,
    );
    fireEvent.press(dismissButton);
    expect(logDismiss).toHaveBeenCalledTimes(1);

    const contentCard = queryByTestId(BrazeContentCard.testIds.ROOT);
    expect(contentCard).toBeNull();
  });
});
