import React from 'react';
import { cleanup, render } from '@testing-library/react-native';

import { InfoStepList } from './InfoStepList';

const mockListData = [
  {
    title:
      'Confirm your details and submit the required information and documents',
  },
  { title: 'Choose the bank account you want to be paid into' },
  { title: 'Sign the terms and conditions' },
];

const mockNextSteps = [
  {
    title: 'Request payments from customers for your Checkatrade jobs',
  },
  { title: 'Get paid directly into your bank account' },
  { title: 'Track and manage your payments in one place' },
];

const mockSetupPageStepsData = {
  list: [{ title: 'Step 1' }, { title: 'Step 2' }, { title: 'Step 3' }],
};

describe('Screens | JobPayments | components | InfoStepList', () => {
  beforeEach(cleanup);

  it('renders the correct amount of list', () => {
    const { getAllByTestId } = render(
      <InfoStepList list={mockListData} isCheckCircleList={false} />,
    );

    const listItems = getAllByTestId(InfoStepList.testIds.LIST_ITEM);

    expect(listItems.length).toBe(mockSetupPageStepsData.list.length);
  });

  it('renders step numbers by default', () => {
    const { getAllByTestId, getByText } = render(
      <InfoStepList list={mockListData} isCheckCircleList={false} />,
    );

    const circleElements = getAllByTestId(InfoStepList.testIds.INDEX_CIRCLE);

    expect(circleElements).toHaveLength(mockSetupPageStepsData.list.length);

    circleElements.forEach((_, index) => {
      expect(getByText(`${index + 1}`)).toBeDefined();
    });
  });

  it('renders isCheckCircleList if set to true', () => {
    const { getAllByTestId } = render(
      <InfoStepList list={mockNextSteps} isCheckCircleList={true} />,
    );

    const checkCircle = getAllByTestId(InfoStepList.testIds.CHECK_CIRCLE);

    expect(checkCircle).toBeDefined();
  });
});
