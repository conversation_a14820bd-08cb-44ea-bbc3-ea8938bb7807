import React from 'react';
import { View } from 'react-native';
import {
  Typography,
  TypographyVariants,
} from '@cat-home-experts/react-native-components';
import {
  adjustMortarIconMargins,
  createMortarStyles,
  createTestIds,
  themeObject,
} from '@cat-home-experts/react-native-utilities';
import { CheckCircle } from '@cat-home-experts/mortar-iconography-native';
import type { SetupInfoListType } from './types';

interface InfoStepListProps {
  list: SetupInfoListType;
  isCheckCircleList?: boolean;
  titleVariant?: TypographyVariants;
}

const TEST_IDS = createTestIds('info-step-list', {
  LIST_ITEM: 'list-item',
  INDEX_CIRCLE: 'index-circle',
  CHECK_CIRCLE: 'check-circle',
});

const ICON_SIZE = 20;

export const InfoStepList = ({
  list,
  isCheckCircleList,
  titleVariant = 'bodySmall',
}: InfoStepListProps): ReturnType<React.FC> => {
  return (
    <View style={styles.listContainer}>
      {list.map((listItem, index) => (
        <View
          key={listItem.title}
          style={styles.listItem}
          testID={TEST_IDS.LIST_ITEM}
        >
          <View style={styles.marginTop}>
            {isCheckCircleList ? (
              <CheckCircle
                style={styles.checkCircleSize}
                size={ICON_SIZE}
                color={themeObject.palette.mortarV3.tokenDefault700}
                testID={TEST_IDS.CHECK_CIRCLE}
              />
            ) : (
              <View testID={TEST_IDS.INDEX_CIRCLE} style={styles.circle}>
                <Typography useVariant="bodySMBold" style={styles.circleLabel}>
                  {index + 1}
                </Typography>
              </View>
            )}
          </View>

          <View style={styles.descriptionContainer}>
            <Typography useVariant={titleVariant} style={styles.text}>
              {listItem.title}
            </Typography>
            {listItem.description && (
              <Typography use="bodySmall" style={styles.text}>
                {listItem.description}
              </Typography>
            )}
          </View>
        </View>
      ))}
    </View>
  );
};

InfoStepList.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  listContainer: {
    gap: spacing(1),
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing(1),
  },
  text: {
    flexShrink: 1,
  },
  marginTop: { marginTop: 2 },
  circle: {
    justifyContent: 'center',
    alignItems: 'center',
    width: spacing(2.5),
    height: spacing(2.5),
    borderRadius: spacing(2),
    borderWidth: 2,
    borderColor: palette.mortar.tokenColorPrimaryBlue,
  },
  circleLabel: {
    color: palette.mortar.tokenColorPrimaryBlue,
    lineHeight: 16,
    fontSize: 12,
  },
  checkCircleSize: {
    marginHorizontal: adjustMortarIconMargins(spacing(2.5)),
    width: ICON_SIZE,
  },
  checkCircleContainer: { width: 20 },
  descriptionContainer: {
    flex: 1,
  },
}));
