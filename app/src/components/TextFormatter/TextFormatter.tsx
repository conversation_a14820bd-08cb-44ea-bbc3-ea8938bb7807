import React, { ReactElement, useMemo, useState } from 'react';
import QuillEditor from 'react-native-cn-quill';
import { noop } from 'lodash';

import {
  tokenColorLightBlue,
  tokenColorNavyBlue,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import {
  createMortarStyles,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { InputWrapper } from '@cat-home-experts/react-native-components/src/ui/InputField/InputWrapper';
import { getTextFormatterToolbarOptions } from './TextFormatterOptions';
import type { TextFormatterProps } from './TextFormatter.types';

const injectedJavaScript = `
  // >>> CSS overwrite START <<<
  const toolbar = document.querySelector('.ql-toolbar');
  const container = document.querySelector('.ql-container');
  const editor = document.querySelector('.ql-editor');

  const toolbarStyles = {
    backgroundColor: "${tokenColorLightBlue}",
    position: "fixed",
    width: "100vw",
    border: "0",
    top: "0",
    left: "0",
    zIndex: "1",
  };

  const containerStyles = {
    paddingTop: "36px",
    fontFamily: "sans-serif",
  };

  const editorStyles = {
    fontSize: "16px",
    lineHeight: "24px",
    color: "${tokenColorNavyBlue}",
  };

  Object.assign(toolbar.style, toolbarStyles);
  Object.assign(container.style, containerStyles);
  Object.assign(editor.style, editorStyles);

  // >>> CSS overwrite END <<<


  // >>> Prevent clicks/taps on links function START <<<

  // This function stops the user from being able to open any links from within
  // the webview. Using webview onShouldStartLoadWithRequest would not behave the
  // same with target:blank links and would still open a blank page within the webview.
  (function(){

    // register custom events
    var handleRegisterEvent = function(element, event, callback)
    {
      event = event.replace(/^on/g, '');
      if ( 'addEventListener' in window ) {
        element.addEventListener(event, callback, false);
      } else if ( 'registerEvent' in window ) {
        element.handleRegisterEvent('on'+event, callback);
      } else {
        var registered = element['on' + event];
        element['on' + event] = registered ? function(e) {
          registered(e);
          callback(e);
        } : callback;
      }
      return element;
    }

    // queries all links in the document and binds a click event
    var allLinks = document.querySelectorAll('a[href]');
    if ( allLinks ) {
      for ( var key in allLinks ) {
        if ( allLinks.hasOwnProperty(key) ) {
          handleRegisterEvent(allLinks[key], 'onclick', function(event){
            // handle all external URL and prevent the default event
            event.preventDefault();
          });
        }
      }
    }
  })();

  // >>> Prevent clicks/taps on links function END <<<
`;

export function TextFormatter({
  value,
  onChange,
  onFocus,
  onBlur,
  label,
  error,
  hintMessage,
  isDisabled = false,
  isSensitive = false,
  hideLabel = false,
  allowLinks = false,
  placeholder = 'Add description',
}: TextFormatterProps): ReactElement {
  const [isFocused, setIsFocused] = useState<boolean>(false);

  const handleFocus = () => {
    if (isDisabled) {
      return;
    }

    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const height = isFocused ? 300 : 200;

  const containerStyles = useMemo(
    () => [
      styles.inputWrapper,
      {
        height,
      },
    ],
    [height],
  );

  const webviewOptions = useMemo(
    () => ({
      containerStyle: [
        styles.containerStyles,
        {
          height: height - 4, // Account for input wrapper padding to fix border
        },
      ],
      injectedJavaScript,
      nestedScrollEnabled: true,
    }),
    [height],
  );

  const editorOptions = useMemo(
    () => ({
      placeholder,
      modules: getTextFormatterToolbarOptions(allowLinks),
    }),
    [placeholder, allowLinks],
  );

  const isActive = isTruthy(value?.length) || isFocused;

  return (
    <InputWrapper
      label={label}
      isDisabled={isDisabled}
      error={error}
      hintMessage={hintMessage}
      isSensitive={isSensitive}
      isActive={isActive}
      isFocused={isFocused}
      labelHidden={hideLabel}
      isInputCovered={false}
      onIconPress={noop}
      onInputCoverToggle={noop}
      containerStyle={containerStyles}
      focusBorderHidden
    >
      <QuillEditor
        quill={editorOptions}
        webview={webviewOptions}
        initialHtml={value}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onHtmlChange={({ html }) => onChange?.(html)}
        // eslint-disable-next-line react-native/no-inline-styles
        style={{ minHeight: 196, maxHeight: 296 }}
      />
    </InputWrapper>
  );
}

const styles = createMortarStyles(({ spacing }) => ({
  inputWrapper: {
    paddingTop: 2,
    paddingBottom: 2,
  },
  containerStyles: {
    height: 'auto',
    borderRadius: spacing(0.5),
    padding: 0,
  },
}));
