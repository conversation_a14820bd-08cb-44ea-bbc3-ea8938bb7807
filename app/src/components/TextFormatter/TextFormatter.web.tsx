/* eslint-disable react-native/no-inline-styles */
import React, { ReactElement, useMemo, useState } from 'react';
import ReactQuill from 'react-quill';
import { noop } from 'lodash';

import { InputWrapper } from '@cat-home-experts/react-native-components/src/ui/InputField/InputWrapper';
import {
  createMortarStyles,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import type { TextFormatterProps } from './TextFormatter.types';
import { getTextFormatterToolbarOptions } from './TextFormatterOptions';
import './TextFormatterWeb.css';

export function TextFormatter({
  value,
  onChange,
  onFocus,
  onBlur,
  label,
  error,
  hintMessage,
  isDisabled = false,
  isSensitive = false,
  hideLabel = false,
  allowLinks = false,
  placeholder = 'Add description',
}: TextFormatterProps): ReactElement {
  const [isFocused, setIsFocused] = useState<boolean>(false);

  const handleFocus = () => {
    if (isDisabled) {
      return;
    }

    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const height = isFocused ? 300 : 200;

  const containerStyles = useMemo(
    () => [
      styles.inputWrapper,
      {
        height,
      },
    ],
    [height],
  );

  const isActive = isTruthy(value?.length) || isFocused;

  return (
    <InputWrapper
      label={label}
      isDisabled={isDisabled}
      error={error}
      hintMessage={hintMessage}
      isSensitive={isSensitive}
      isActive={isActive}
      isFocused={isFocused}
      labelHidden={hideLabel}
      isInputCovered={false}
      onIconPress={noop}
      onInputCoverToggle={noop}
      containerStyle={containerStyles}
      focusBorderHidden
    >
      <ReactQuill
        style={{
          width: '100%',
          height: '100%',
          overflow: 'scroll',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
        }}
        theme="snow"
        readOnly={false}
        placeholder={placeholder}
        modules={getTextFormatterToolbarOptions(allowLinks)}
        value={value}
        onChange={onChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
      />
    </InputWrapper>
  );
}

const styles = createMortarStyles(() => ({
  inputWrapper: {
    paddingTop: 2,
  },
}));
