import React, { lazy, Suspense } from 'react';
import { View } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { Spinner } from 'src/components/primitives/Spinner';
import type { TextFormatterProps } from './TextFormatter.types';

const TextFormatterLazy = lazy(() =>
  import('./TextFormatter').then((module) => ({
    default: module.TextFormatter,
  })),
);

// Lazy load to split quill library out of the main bundle
export const TextFormatter: React.FC<TextFormatterProps> = (props) => {
  return (
    <Suspense
      fallback={
        <View style={styles.spinnerContainer}>
          <Spinner size={40} />
        </View>
      }
    >
      <TextFormatterLazy {...props} />
    </Suspense>
  );
};

const styles = createMortarStyles(() => ({
  spinnerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
