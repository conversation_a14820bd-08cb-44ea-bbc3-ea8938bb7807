import React from 'react';
import { View, type StyleProp, type ViewStyle } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';

interface MaxWidthContainerProps extends React.PropsWithChildren {
  maxWidth: number;
  innerStyle?: StyleProp<ViewStyle>;
  outerStyle?: StyleProp<ViewStyle>;
  testID?: string;
}

export const MaxWidthContainer: React.FC<MaxWidthContainerProps> = ({
  children,
  maxWidth,
  testID,
  innerStyle,
  outerStyle,
}) => {
  const isDesktop = useDesktopMediaQuery();

  return (
    <View
      style={[styles.outer, isDesktop && styles.outerDesktop, outerStyle]}
      testID={testID}
    >
      <View style={[styles.inner, { maxWidth }, innerStyle]}>{children}</View>
    </View>
  );
};

const styles = createMortarStyles(({ spacing }) => ({
  outer: {
    width: '100%',
    alignItems: 'center',
  },
  outerDesktop: {
    paddingHorizontal: spacing(3),
  },
  inner: {
    width: '100%',
  },
}));
