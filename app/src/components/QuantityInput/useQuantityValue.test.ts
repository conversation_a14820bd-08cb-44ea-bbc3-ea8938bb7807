import { renderHook, cleanup } from '@testing-library/react-native';
import { useQuantityValue } from 'src/components/QuantityInput/useQuantityValue';

describe('Components | QuantityInput | Hooks | useQuantityValue', () => {
  afterEach(cleanup);

  it('shows an error state if amount too little', () => {
    // Act
    const { result } = renderHook(() =>
      useQuantityValue({
        initialValue: -5,
        min: 0,
        max: 100,
        step: 1,
      }),
    );

    // Assert
    expect(result.current.errors.includes('Quantity must be at least 0.')).toBe(
      true,
    );
  });

  it('shows an error state if amount too much', () => {
    // Act
    const { result } = renderHook(() =>
      useQuantityValue({
        initialValue: 105,
        min: 0,
        max: 100,
        step: 1,
      }),
    );

    // Assert
    expect(
      result.current.errors.includes('Quantity must be no more than 100.'),
    ).toBe(true);
  });

  it('shows an error state if amount not a multiple of the step', () => {
    // Act
    const { result } = renderHook(() =>
      useQuantityValue({
        initialValue: 51,
        min: 0,
        max: 100,
        step: 5,
      }),
    );

    // Assert
    expect(
      result.current.errors.includes('Quantity must be a multiple of 5.'),
    ).toBe(true);
  });

  it('describes all issues if multiple issues', () => {
    // Act
    const { result } = renderHook(() =>
      useQuantityValue({
        initialValue: 51,
        min: 0,
        max: 50,
        step: 5,
      }),
    );

    // Assert
    expect(
      result.current.errors.includes('Quantity must be a multiple of 5.'),
    ).toBe(true);
    expect(
      result.current.errors.includes('Quantity must be no more than 50.'),
    ).toBe(true);
  });
});
