import React from 'react';
import { TextInput, TouchableOpacity, View } from 'react-native';
import {
  createMortarStyles,
  createTestIds,
  themeObject,
} from '@cat-home-experts/react-native-utilities';
import { Minus, Plus } from '@cat-home-experts/mortar-iconography-native';
import { Typography } from '@cat-home-experts/react-native-components';

interface OrderMarketingMaterialsQuantityInputProps {
  value: number;
  placeholder: string;
  onChange: (value: number) => void;
  testID?: string;
  success: boolean;
  errors: string[];
  step?: number;
  min: number;
  max: number;
}

const TEST_IDS = createTestIds('order-marketing-materials-quantity-input', {
  INPUT: 'input',
  INCREASE_BUTTON: 'increase-button',
  DECREASE_BUTTON: 'decrease-button',
  ERROR_TEXT: 'error-text',
});

export function QuantityInput({
  value,
  placeholder,
  onChange,
  success,
  errors,
  step = 1,
  min,
  max,
  testID,
}: OrderMarketingMaterialsQuantityInputProps): ReturnType<React.FC> {
  // Computed Values
  const minusDisabled = value <= min || !success;
  const plusDisabled = value >= max || !success;

  // Methods
  const handleChangeText = (text: string) => {
    if (!text) {
      onChange(0);
      return;
    }

    const removedPoints = text.replace(/\./g, '');
    const newValue = parseInt(removedPoints, 10);

    onChange(Number.isNaN(newValue) ? 0 : newValue);
  };

  const handleIncrement = () => onChange(value + step);

  const handleDecrement = () => onChange(value - step);

  return (
    <View style={styles.root}>
      <View style={styles.row}>
        <TouchableOpacity
          testID={TEST_IDS.DECREASE_BUTTON}
          onPress={handleDecrement}
          disabled={minusDisabled}
          activeOpacity={0.5}
        >
          <View style={[styles.button, minusDisabled && styles.buttonDisabled]}>
            <Minus
              size={16}
              color={themeObject.palette.mortarV3.tokenNeutral600}
            />
          </View>
        </TouchableOpacity>
        <TextInput
          testID={testID ?? TEST_IDS.INPUT}
          placeholderTextColor={themeObject.palette.mortarV3.tokenNeutral600}
          placeholder={placeholder}
          style={[styles.input, !success && styles.errorBorder]}
          value={value.toString(10)}
          onChangeText={handleChangeText}
          keyboardType="numeric"
        />
        <TouchableOpacity
          testID={TEST_IDS.INCREASE_BUTTON}
          onPress={handleIncrement}
          disabled={plusDisabled}
          activeOpacity={0.5}
        >
          <View style={[styles.button, plusDisabled && styles.buttonDisabled]}>
            <Plus
              size={16}
              color={themeObject.palette.mortarV3.tokenNeutral600}
            />
          </View>
        </TouchableOpacity>
      </View>
      {!success && (
        <Typography
          useVariant="captionSmall"
          style={styles.info}
          testID={TEST_IDS.ERROR_TEXT}
        >
          {errors.join(' ')}
        </Typography>
      )}
    </View>
  );
}

QuantityInput.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  root: { gap: spacing(1) },
  row: { flexDirection: 'row', gap: spacing(1) },
  buttonDisabled: { opacity: 0.5 },
  button: {
    height: spacing(6.2),
    width: spacing(6.2),
    borderRadius: spacing(0.5),
    borderWidth: 1,
    borderColor: palette.mortar.tokenColorLighterGrey,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    textAlign: 'center',
    fontSize: spacing(2),
    fontFamily: 'regular',
    flex: 1,
    height: spacing(6.2),
    paddingHorizontal: spacing(1.5),
    borderRadius: spacing(0.5),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderWidth: 1,
    borderColor: palette.mortar.tokenColorLighterGrey,
  },
  errorBorder: {
    borderColor: palette.mortarV3.tokenPrimary700,
  },
  info: {
    textAlign: 'center',
    color: palette.mortarV3.tokenPrimary700,
    fontSize: spacing(1.125),
  },
}));
