import React from 'react';
import { cleanup, fireEvent, render } from '@testing-library/react-native';
import {
  getFlattenedStyles,
  themeObject,
} from '@cat-home-experts/react-native-utilities';
import { QuantityInput } from './QuantityInput';

const ERROR_COLOR = themeObject.palette.mortarV3.tokenPrimary700;

describe('Screens | OrderMarketingMaterials | Components | OrderMarketingMaterialsQRCode', () => {
  afterEach(cleanup);

  it('increments by expected amount when pressing increase', () => {
    // Arrange
    const step = 5;
    const onChange = jest.fn();

    // Act
    const { getByTestId } = render(
      <QuantityInput
        value={5}
        placeholder=""
        onChange={onChange}
        step={step}
        success={true}
        errors={[]}
        min={0}
        max={100}
      />,
    );
    const increaseButton = getByTestId(QuantityInput.testIds.INCREASE_BUTTON);
    fireEvent.press(increaseButton);

    // Assert
    expect(onChange).toHaveBeenCalledWith(10);
  });

  it('decrements by expected amount when pressing decrease', () => {
    // Arrange
    const step = 5;
    const onChange = jest.fn();

    // Act
    const { getByTestId } = render(
      <QuantityInput
        value={10}
        placeholder=""
        onChange={onChange}
        step={step}
        success={true}
        errors={[]}
        min={0}
        max={100}
      />,
    );
    const decreaseButton = getByTestId(QuantityInput.testIds.DECREASE_BUTTON);
    fireEvent.press(decreaseButton);

    // Assert
    expect(onChange).toHaveBeenCalledWith(5);
  });

  it('Sets value to zero if string entered is falsy', () => {
    // Act
    const onChange = jest.fn();
    const { getByTestId } = render(
      <QuantityInput
        value={10}
        placeholder=""
        onChange={onChange}
        success={true}
        errors={[]}
        step={5}
        min={0}
        max={20}
      />,
    );
    const input = getByTestId(QuantityInput.testIds.INPUT);
    fireEvent.changeText(input, '');

    // Assert
    expect(onChange).toHaveBeenCalledWith(0);
  });

  it('Removes floating points if entered', () => {
    // Act
    const onChange = jest.fn();
    const { getByTestId } = render(
      <QuantityInput
        value={10}
        placeholder=""
        onChange={onChange}
        success={true}
        errors={[]}
        step={5}
        min={0}
        max={20}
      />,
    );
    const input = getByTestId(QuantityInput.testIds.INPUT);
    fireEvent.changeText(input, '10.0');

    // Assert
    expect(onChange).toHaveBeenCalledWith(100);
  });

  it('Converts entered string to value', () => {
    // Act
    const onChange = jest.fn();
    const { getByTestId } = render(
      <QuantityInput
        value={10}
        placeholder=""
        onChange={onChange}
        success={true}
        errors={[]}
        step={5}
        min={0}
        max={20}
      />,
    );
    const input = getByTestId(QuantityInput.testIds.INPUT);
    fireEvent.changeText(input, '10');

    // Assert
    expect(onChange).toHaveBeenCalledWith(10);
  });

  it('Shows an error state if unsuccessful', () => {
    // Arrange
    const mockErrors = ['errorOne', 'errorTwo'];

    // Act
    const onChange = jest.fn();
    const { getByTestId } = render(
      <QuantityInput
        value={10}
        placeholder=""
        onChange={onChange}
        success={false}
        errors={mockErrors}
        step={5}
        min={0}
        max={20}
      />,
    );
    const input = getByTestId(QuantityInput.testIds.INPUT);
    const errorText = getByTestId(QuantityInput.testIds.ERROR_TEXT);

    // Assert
    expect(getFlattenedStyles(input)).toMatchObject({
      borderColor: ERROR_COLOR,
    });
    for (const error of mockErrors) {
      expect(errorText).toHaveTextContent(new RegExp(error));
    }
  });
});
