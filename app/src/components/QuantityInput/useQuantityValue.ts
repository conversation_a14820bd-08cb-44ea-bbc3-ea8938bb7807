import { useMemo, useState } from 'react';
import { isQuantityValid, IsQuantityValidProps } from './utilities';

interface UseQuantityValueProps extends IsQuantityValidProps {
  initialValue: number;
}

interface UseQuantityValueSuccessResponse {
  value: number;
  success: boolean;
  errors: string[];
  onChange: (value: number | ((value: number) => number)) => void;
}

export const useQuantityValue = ({
  min,
  max,
  step,
  initialValue,
}: UseQuantityValueProps): UseQuantityValueSuccessResponse => {
  // State
  const [value, setValue] = useState<number>(initialValue);

  // Computed Values
  const isValid = useMemo(
    () => isQuantityValid({ min, max, step }),
    [min, max, step],
  );
  const { success, errors } = isValid(value);

  return { value, success, errors, onChange: setValue };
};
