export interface IsQuantityValidProps {
  min: number;
  max: number;
  step: number;
}

interface IsQuantityValidResponse {
  success: boolean;
  errors: string[];
}

interface QuantityValidator {
  message: string;
  validate: (value: number) => boolean;
}

type IsQuantityValid = (
  config: IsQuantityValidProps,
) => (value: number) => IsQuantityValidResponse;

const minValidator = (min: number): QuantityValidator => ({
  message: `Quantity must be at least ${min}.`,
  validate: (value: number) => value >= min,
});

const maxValidator = (max: number): QuantityValidator => ({
  message: `Quantity must be no more than ${max}.`,
  validate: (value: number) => value <= max,
});

const stepValidator = (step: number): QuantityValidator => ({
  message: `Quantity must be a multiple of ${step}.`,
  validate: (value: number) => Number.isInteger(value / step),
});

export const isQuantityValid: IsQuantityValid = ({ min, max, step }) => {
  const validators = [
    minValidator(min),
    maxValidator(max),
    stepValidator(step),
  ];

  return (value) => {
    const errors = validators.reduce<string[]>((acc, { message, validate }) => {
      const isValid = validate(value);
      return isValid ? acc : [...acc, message];
    }, []);

    return {
      success: !errors.length,
      errors,
    };
  };
};
