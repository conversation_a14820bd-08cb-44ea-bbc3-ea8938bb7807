import { Platform } from 'react-native';
import { nativeApplicationVersion } from 'expo-application';
import * as Device from 'expo-device';

const emailTo = '<EMAIL>';
const subject = 'Checkatrade Feedback';

const bodyTemplate = (feature: string, companyId?: number): string => `


  Please do not change the technical data below.
  [${Platform.Version}],[${nativeApplicationVersion}],[${companyId ?? ''}],[${
    Device.modelName
  }],[${Platform.OS}][${feature}]
`;

export { emailTo, subject, bodyTemplate };
