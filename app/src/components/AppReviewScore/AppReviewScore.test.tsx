import React from 'react';
import { Alert } from 'react-native';
import { render, act, cleanup } from '@testing-library/react-native';

import { useAppReviewScore } from 'src/hooks/useAppReviewScore';
import { captureException } from 'src/services/sentry';
import { EVENT_TYPE, ANALYTICS_ACTION_TYPE } from 'src/constants.events';
import { logEvent as _mockLogEvent } from 'src/services/analytics';
import { AppReviewScore } from './AppReviewScore';

jest.mock('src/hooks/useAppReviewScore');

const mockRequestInAppReview = jest.fn();

jest.mock('src/services/sentry', () => ({
  captureException: jest.fn(),
}));

jest.mock('expo-store-review', () => ({
  isAvailableAsync: () => Promise.resolve(true),
  requestReview: mockRequestInAppReview,
}));

const mockLogEvent = _mockLogEvent as jest.Mock;
jest.mock('src/services/analytics', () => ({
  logEvent: jest.fn(),
}));

describe('Screens | AppReviewScore', () => {
  const mockDismissModal = jest.fn();

  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('should render alert if conditions met ', () => {
    // Arrange
    (useAppReviewScore as jest.Mock).mockImplementation(() => ({
      showAppReviewModalForEvent: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE,
      handleDismissModal: mockDismissModal,
    }));

    jest.spyOn(Alert, 'alert');

    // Act
    render(<AppReviewScore />);

    // Assert
    expect(Alert.alert).toHaveBeenCalled();
  });

  it('should log a positive user event on positive response', async () => {
    // Arrange
    (useAppReviewScore as jest.Mock).mockImplementation(() => ({
      showAppReviewModalForEvent: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE,
      handleDismissModal: mockDismissModal,
    }));

    mockRequestInAppReview.mockResolvedValue(undefined);

    jest.spyOn(Alert, 'alert');

    // Act
    render(<AppReviewScore />);

    await act(async () => {
      const [firstCall] = (Alert.alert as jest.Mock).mock.calls;
      const [, , buttons] = firstCall;
      const [firstButton] = buttons;

      await firstButton.onPress();
    });

    await mockLogEvent.mock.calls[1];

    // Assert
    expect(mockLogEvent).toHaveBeenNthCalledWith(
      2,
      `${EVENT_TYPE.REVIEW_MODAL}_positive_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      { eventName: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE },
    );

    expect(mockDismissModal).toHaveBeenCalledTimes(1);
  });

  it('should log a complete event when reaches end of flow', async () => {
    // Arrange
    (useAppReviewScore as jest.Mock).mockImplementation(() => ({
      showAppReviewModalForEvent: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE,
      handleDismissModal: mockDismissModal,
    }));

    mockRequestInAppReview.mockImplementationOnce(() => Promise.resolve());

    jest.spyOn(Alert, 'alert');

    // Act
    render(<AppReviewScore />);

    await act(async () => {
      const [firstCall] = (Alert.alert as jest.Mock).mock.calls;
      const [, , buttons] = firstCall;
      const [firstButton] = buttons;

      await firstButton.onPress();
    });

    await mockLogEvent.mock.calls[1];

    // Assert
    expect(mockLogEvent).toHaveBeenNthCalledWith(
      3,
      EVENT_TYPE.REVIEW_MODAL_FLOW_COMPLETE,
      { eventName: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE },
    );
  });

  it('should log an error event when flow errors', async () => {
    // Arrange
    (useAppReviewScore as jest.Mock).mockImplementation(() => ({
      showAppReviewModalForEvent: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE,
      handleDismissModal: mockDismissModal,
    }));

    const mockError = new Error('Mock error');
    mockRequestInAppReview.mockImplementationOnce(() =>
      Promise.reject(mockError),
    );

    jest.spyOn(Alert, 'alert');

    // Act
    render(<AppReviewScore />);

    await act(async () => {
      const [firstCall] = (Alert.alert as jest.Mock).mock.calls;
      const [, , buttons] = firstCall;
      const [firstButton] = buttons;

      await firstButton.onPress();
    });

    await mockLogEvent.mock.calls[1];

    // Assert
    expect(captureException).toHaveBeenCalledWith(mockError);
    expect(mockLogEvent).toHaveBeenNthCalledWith(
      3,
      EVENT_TYPE.REVIEW_MODAL_REVIEW_ERROR,
      { eventName: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE },
    );
  });

  it('should log a negative user event on negative response', async () => {
    // Arrange
    (useAppReviewScore as jest.Mock).mockImplementation(() => ({
      showAppReviewModalForEvent: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE,
      handleDismissModal: mockDismissModal,
    }));

    jest.spyOn(Alert, 'alert');

    // Act
    render(<AppReviewScore />);

    await act(async () => {
      const [firstCall] = (Alert.alert as jest.Mock).mock.calls;
      const [, , buttons] = firstCall;
      const [, secondButton] = buttons;

      await secondButton.onPress();
    });

    // Assert
    expect(mockLogEvent).toHaveBeenNthCalledWith(
      3,
      `${EVENT_TYPE.REVIEW_MODAL}_negative_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      { eventName: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE },
    );

    expect(mockDismissModal).toHaveBeenCalledTimes(1);
  });

  it("calls handleDismissModal if user doesn't want to answer now", async () => {
    // Arrange
    (useAppReviewScore as jest.Mock).mockImplementation(() => ({
      showAppReviewModalForEvent: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE,
      handleDismissModal: mockDismissModal,
    }));

    jest.spyOn(Alert, 'alert');

    // Act
    render(<AppReviewScore />);

    await act(async () => {
      const [firstCall] = (Alert.alert as jest.Mock).mock.calls;
      const [, , buttons] = firstCall;
      const [, , thirdButton] = buttons;

      await thirdButton.onPress();
    });

    // Assert
    expect(mockDismissModal).toHaveBeenCalledTimes(1);
  });

  it('calls handleDismissModal if user dismisses', async () => {
    // Arrange
    (useAppReviewScore as jest.Mock).mockImplementation(() => ({
      showAppReviewModalForEvent: EVENT_TYPE.PHOTOS_UPLOAD_COMPLETE,
      handleDismissModal: mockDismissModal,
    }));

    jest.spyOn(Alert, 'alert');

    // Act
    render(<AppReviewScore />);

    await act(async () => {
      const [firstCall] = (Alert.alert as jest.Mock).mock.calls;
      const [, , , cancelButton] = firstCall;

      await cancelButton.onDismiss();
    });

    // Assert
    expect(mockDismissModal).toHaveBeenCalledTimes(1);
  });

  it('should not render alert if conditions not met', () => {
    // Arrange
    (useAppReviewScore as jest.Mock).mockImplementation(() => ({
      showAppReviewModalForEvent: null,
      handleDismissModal: mockDismissModal,
    }));

    // Act
    render(<AppReviewScore />);

    jest.spyOn(Alert, 'alert');

    // Assert
    expect(Alert.alert).not.toHaveBeenCalled();
    expect(mockDismissModal).not.toHaveBeenCalled();
  });
});
