import { FC, useEffect } from 'react';
import { Alert } from 'react-native';
import { isTruthy } from '@cat-home-experts/react-native-utilities';

import * as StoreReview from 'expo-store-review';
import useLatestCallback from 'use-latest-callback';

import { ANALYTICS_ACTION_TYPE, EVENT_TYPE } from 'src/constants.events';
import { IS_ANDROID } from 'src/constants';

import { useAppReviewScore } from 'src/hooks/useAppReviewScore';
import { useUserContext } from 'src/hooks/useUser';
import { logEvent } from 'src/services/analytics';
import { captureException } from 'src/services/sentry';
import { sendEmail } from 'src/utilities/email/sendEmail';
import type { EventTypeKey } from 'src/services/analytics/logEmitter';

import { emailTo, subject, bodyTemplate } from './emailData';

interface AlertData {
  event: EventTypeKey;
  handleDismiss: (eventType: EVENT_TYPE) => void;
  handleDismissModal: () => void;
  companyId?: number;
}

const prepareLogEvent = (eventName: EventTypeKey) => ({ eventName });

const handleNeedWork = ({
  event,
  handleDismiss,
  companyId,
  handleDismissModal,
}: AlertData) => {
  logEvent(
    `${EVENT_TYPE.NEED_WORK_MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
    prepareLogEvent(event),
  );

  const cancelButton = {
    text: 'Cancel',
    onPress: () => {
      handleDismiss(EVENT_TYPE.NEED_WORK_MODAL);
      logEvent(
        `${EVENT_TYPE.NEED_WORK_MODAL}_negative_${ANALYTICS_ACTION_TYPE.CLICKED}`,
        prepareLogEvent(event),
      );
    },
  };

  const sendButton = {
    text: 'Send',
    onPress: () => {
      sendEmail({
        to: emailTo,
        subject,
        body: bodyTemplate(event, companyId),
      });
      handleDismissModal();
      logEvent(
        `${EVENT_TYPE.NEED_WORK_MODAL}_positive_${ANALYTICS_ACTION_TYPE.CLICKED}`,
        prepareLogEvent(event),
      );
    },
  };

  Alert.alert(
    'Thanks for your feedback',
    "We'd love to hear how we can make your experience better, please send us an email with your thoughts",
    [cancelButton, sendButton],
    {
      onDismiss: () => {
        handleDismiss(EVENT_TYPE.NEED_WORK_MODAL);
      },
    },
  );
};

const createAlertButtons = ({
  event,
  handleDismiss,
  handleDismissModal,
  companyId,
}: AlertData) => {
  const itsBeenGreatButton = {
    text: "It's been great",
    onPress: async () => {
      logEvent(
        `${EVENT_TYPE.REVIEW_MODAL}_positive_${ANALYTICS_ACTION_TYPE.CLICKED}`,
        prepareLogEvent(event),
      );
      handleDismissModal();

      try {
        const isAvailable = await StoreReview.isAvailableAsync();

        if (isAvailable) {
          await StoreReview.requestReview();
          logEvent(
            EVENT_TYPE.REVIEW_MODAL_FLOW_COMPLETE,
            prepareLogEvent(event),
          );
          return;
        }

        logEvent(
          `${EVENT_TYPE.REVIEW_MODAL_REVIEW_ERROR}_not_available`,
          prepareLogEvent(event),
        );
      } catch (error) {
        logEvent(EVENT_TYPE.REVIEW_MODAL_REVIEW_ERROR, prepareLogEvent(event));
        captureException(error);
      }
    },
  };

  const needsWorkButton = {
    text: 'Needs work',
    onPress: () => {
      handleNeedWork({ event, handleDismiss, handleDismissModal, companyId });
      logEvent(
        `${EVENT_TYPE.REVIEW_MODAL}_negative_${ANALYTICS_ACTION_TYPE.CLICKED}`,
        prepareLogEvent(event),
      );
      handleDismissModal();
    },
  };

  const askMeLaterButton = {
    text: 'Ask me later',
    onPress: () => handleDismiss(EVENT_TYPE.REVIEW_MODAL),
  };

  const buttons = [itsBeenGreatButton, needsWorkButton, askMeLaterButton];

  return IS_ANDROID ? buttons.reverse() : buttons;
};

export function AppReviewScore(): ReturnType<FC> {
  // Context
  const { companyId } = useUserContext();

  // Computed Values
  const { showAppReviewModalForEvent, handleDismissModal } =
    useAppReviewScore();

  // Methods
  const handleDismiss = useLatestCallback((eventType: EVENT_TYPE) => {
    if (!showAppReviewModalForEvent) {
      return;
    }

    logEvent(
      `${eventType}_dismissed_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      prepareLogEvent(showAppReviewModalForEvent),
    );
    handleDismissModal();
  });

  // Effects
  useEffect(() => {
    if (!isTruthy(showAppReviewModalForEvent)) {
      return;
    }

    logEvent(
      `${EVENT_TYPE.REVIEW_MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
      prepareLogEvent(showAppReviewModalForEvent),
    );
    Alert.alert(
      'How was your experience using the Checkatrade app today?',
      '',
      createAlertButtons({
        handleDismiss,
        handleDismissModal,
        companyId,
        event: showAppReviewModalForEvent,
      }),
      {
        onDismiss: () => {
          handleDismiss(EVENT_TYPE.REVIEW_MODAL);
        },
      },
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showAppReviewModalForEvent, handleDismiss]);
  return null;
}
