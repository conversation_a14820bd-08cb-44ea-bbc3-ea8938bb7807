import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import {
  PaperPlane,
  CreditCard,
} from '@cat-home-experts/mortar-iconography-native';
import { TextButtonWithIcon } from './TextButtonWithIcon';

describe('TextButtonWithIcon', () => {
  const mockOnPress = jest.fn();

  const props = {
    title: 'Title',
    body: 'Body',
    onPress: mockOnPress,
    mortarIcon: PaperPlane,
  };

  it('can render with minimal information', () => {
    const { getByTestId, getByText, queryByTestId } = render(
      <TextButtonWithIcon {...props} />,
    );

    expect(getByTestId(TextButtonWithIcon.testIds.ROOT)).toBeDefined();
    expect(getByTestId(TextButtonWithIcon.testIds.ICON)).toBeDefined();
    expect(getByText(props.title)).toBeDefined();
    expect(getByText(props.body)).toBeDefined();
    expect(
      queryByTestId(`${TextButtonWithIcon.testIds.RADIO_BUTTON}test-id`),
    ).toBeNull();
    expect(queryByTestId(TextButtonWithIcon.testIds.CHEVRON)).toBeNull();
  });

  it('can accept a different icon to display', () => {
    const { getByTestId } = render(
      <TextButtonWithIcon {...props} mortarIcon={CreditCard} />,
    );

    expect(getByTestId(TextButtonWithIcon.testIds.ROOT)).toBeDefined();
    expect(getByTestId(TextButtonWithIcon.testIds.ICON)).toBeDefined();
  });

  it('calls onPress when pressed', () => {
    const { getByTestId } = render(<TextButtonWithIcon {...props} />);

    fireEvent.press(getByTestId(TextButtonWithIcon.testIds.ROOT));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('renders can render with a radio button if specific props given', () => {
    const radioTestId = 'test-id';
    const { getByTestId } = render(
      <TextButtonWithIcon {...props} isRadioOption id={radioTestId} />,
    );

    expect(
      getByTestId(`${TextButtonWithIcon.testIds.RADIO_BUTTON}test-id`),
    ).toBeOnTheScreen();
  });

  it('will not render a radio option if isRadioOption | id | onPress are missing in props', () => {
    const { queryByTestId } = render(
      <TextButtonWithIcon {...props} onPress={undefined} />,
    );

    expect(
      queryByTestId(`${TextButtonWithIcon.testIds.RADIO_BUTTON}test-id`),
    ).toBeNull();
  });

  it('renders can render with a chevron if specific props given', () => {
    const { getByTestId } = render(
      <TextButtonWithIcon {...props} isNavigatable />,
    );

    expect(getByTestId(TextButtonWithIcon.testIds.CHEVRON)).toBeTruthy();
  });
});
