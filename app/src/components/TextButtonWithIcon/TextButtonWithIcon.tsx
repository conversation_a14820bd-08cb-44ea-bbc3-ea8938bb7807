import React, { ReactElement } from 'react';
import { Pressable, View } from 'react-native';
import {
  adjustMortarIconMargins,
  createMortarStyles,
  createTestIds,
  isTruthy,
  themeObject,
} from '@cat-home-experts/react-native-utilities';
import {
  RadioButton,
  Typography,
} from '@cat-home-experts/react-native-components';
import { ChevronRightSmall } from '@cat-home-experts/mortar-iconography-native';
import type { NativeMortarIcon } from '@cat-home-experts/mortar-types';
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useDerivedValue,
  withTiming,
} from 'react-native-reanimated';

interface TextButtonWithIconProps {
  title: string;
  body?: string;
  mortarIcon: NativeMortarIcon;
  onPress?: () => void;
  id?: string;
  isRadioOption?: boolean;
  isNavigatable?: boolean;
  isChecked?: boolean;
}

const TEST_IDS = createTestIds('payment-option', {
  TITLE: 'title',
  BODY: 'body',
  ICON: 'icon',
  RADIO_BUTTON: 'radio-button-',
  CHEVRON: 'chevron',
});

export function TextButtonWithIcon({
  title,
  body,
  onPress,
  id,
  mortarIcon: MortarIcon,
  isRadioOption = false,
  isNavigatable = false,
  isChecked = false,
}: TextButtonWithIconProps): ReactElement {
  // State
  const [pressed, setPressed] = React.useState<boolean>(false);

  // Animated
  const pressedValue = useDerivedValue(
    () => withTiming(pressed ? 1 : 0, { duration: 150 }),
    [pressed],
  );
  const animatedStyle = useAnimatedStyle(() => ({
    backgroundColor: interpolateColor(
      pressedValue.value,
      [0, 1],
      [
        themeObject.palette.mortarV3.tokenNeutral0,
        themeObject.palette.mortarV3.tokenDefault100,
      ],
    ),
  }));

  return (
    <Pressable
      testID={TEST_IDS.ROOT}
      onPress={onPress}
      onPressIn={() => setPressed(true)}
      onPressOut={() => setPressed(false)}
    >
      <Animated.View style={[styles.container, animatedStyle]}>
        <View style={styles.innerContainer}>
          <View style={styles.iconBubble}>
            {isTruthy(MortarIcon) && (
              <MortarIcon
                size={24}
                color={themeObject.palette.mortarV3.tokenDefault700}
                testID={TEST_IDS.ICON}
                style={styles.icon}
              />
            )}
          </View>
          <View style={styles.textContainer}>
            <Typography useVariant="bodySMSemiBold" testID={TEST_IDS.TITLE}>
              {title}
            </Typography>
            <Typography useVariant="bodySMRegular" testID={TEST_IDS.BODY}>
              {body}
            </Typography>
          </View>
        </View>

        {isNavigatable && (
          <ChevronRightSmall
            testID={TEST_IDS.CHEVRON}
            color={themeObject.palette.mortarV3.tokenDefault900}
            size={16}
          />
        )}

        {isRadioOption && id && onPress && (
          <RadioButton
            testID={`${TEST_IDS.RADIO_BUTTON}${id}`}
            key={id}
            isChecked={isChecked}
            label=""
            onPress={onPress}
            innerStyle={styles.radioButton}
          />
        )}
      </Animated.View>
    </Pressable>
  );
}

TextButtonWithIcon.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderColor: palette.mortar.tokenColorLighterGrey,
    borderBottomWidth: 1,
    borderStyle: 'solid',
    paddingVertical: spacing(2.5),
    justifyContent: 'space-between',
    paddingHorizontal: spacing(2),
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconBubble: {
    backgroundColor: palette.mortar.tokenColorBlueEight,
    padding: spacing(1),
    borderRadius: 8,
    marginRight: spacing(2),
    height: 40,
    width: 40,
  },
  innerContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  radioButton: { borderWidth: 0, paddingRight: 3 },
  icon: {
    marginHorizontal: adjustMortarIconMargins(16),
  },
  textContainer: { flex: 1, alignSelf: 'center' },
  buttonNotPressed: { backgroundColor: palette.mortar.tokenColorPrimaryWhite },
  buttonPressed: { backgroundColor: palette.mortar.tokenColorLightBlue },
}));
