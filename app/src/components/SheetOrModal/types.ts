import type React from 'react';
import type { StyleProp, ViewProps, ViewStyle } from 'react-native';
import type {
  BottomSheetModalProps,
  BottomSheetProps,
} from '@gorhom/bottom-sheet';

export interface SheetOrModalProps extends React.PropsWithChildren {
  visible: boolean;
  onDismiss: () => void;
  style?: StyleProp<ViewStyle>;
  sheetProps?: Partial<BottomSheetProps | BottomSheetModalProps>;
  /**
   * This is here so that we can specify a custom wrapping Bottom Sheet
   * View (BottomSheetView/BottomSheetScrollView, etc.) that will accept
   * the props that automate the sizing of the bottom sheet content.
   *
   * If you would like to handle this in the child component instead,
   * provide an explicit null to this value.
   *
   * Note: Please destructure and add the onLayout prop!
   *
   * @param props
   */
  renderWrapper?:
    | ((
        props: ViewProps & {
          children: React.ReactNode[] | React.ReactNode;
        },
      ) => React.ReactNode)
    | null;
}
