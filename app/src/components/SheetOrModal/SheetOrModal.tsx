import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetProps,
  BottomSheetView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { isTruthy } from '@cat-home-experts/react-native-utilities';
import type { SheetOrModalProps } from './types';

export function SheetOrModal({
  visible,
  style,
  sheetProps = {},
  onDismiss,
  children,
  renderWrapper,
}: SheetOrModalProps): ReturnType<React.FC> {
  // Refs
  const ref = useRef<BottomSheetModal>(null);

  // Computed Values
  const snapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);
  const animatedProps = useMemo<Partial<BottomSheetProps>>(() => {
    if (sheetProps?.snapPoints) {
      return {};
    }

    return {
      snapPoints: animatedSnapPoints,
      handleHeight: animatedHandleHeight,
      contentHeight: animatedContentHeight,
    } as Pick<
      BottomSheetProps,
      'snapPoints' | 'handleHeight' | 'contentHeight'
    >;
  }, [
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    sheetProps?.snapPoints,
  ]);
  const RenderWrapper =
    renderWrapper === undefined ? BottomSheetView : renderWrapper;

  // Effects
  useEffect(() => {
    if (visible) {
      ref.current?.present();
      return;
    }

    ref.current?.dismiss();
  }, [visible]);

  // Renderers
  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
      />
    ),
    [],
  );

  return (
    <BottomSheetModal
      ref={ref}
      index={0}
      accessible={false}
      {...animatedProps}
      backdropComponent={renderBackdrop}
      onDismiss={onDismiss}
      {...sheetProps}
    >
      {isTruthy(RenderWrapper) ? (
        <RenderWrapper onLayout={handleContentLayout} style={style}>
          {children}
        </RenderWrapper>
      ) : (
        children
      )}
    </BottomSheetModal>
  );
}
