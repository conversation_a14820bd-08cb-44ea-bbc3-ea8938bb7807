import React, { type ReactElement } from 'react';
import { Modal } from '@cat-home-experts/react-native-components';
import type { SheetOrModalProps } from './types';

export const SheetOrModal: React.FC<SheetOrModalProps> = ({
  visible,
  style,
  onDismiss,
  children,
}) => {
  return (
    <Modal visible={visible} style={style} onClose={onDismiss}>
      {children as ReactElement /* NEEDS FIXED */}
    </Modal>
  );
};
