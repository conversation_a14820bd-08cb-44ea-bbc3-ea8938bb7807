import React from 'react';
import { Toast } from 'react-native-toast-message/lib/src/Toast';
import { render, cleanup, act } from '@testing-library/react-native';
import { PortalProvider } from '@gorhom/portal';
import { Toaster, showToast } from './Toaster';

describe('Toaster', () => {
  const showToastSpy = jest.spyOn(Toast, 'show');

  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('should render the toast successfully', () => {
    const { getByTestId, getByText } = render(
      <PortalProvider>
        <Toaster />
      </PortalProvider>,
    );

    const expectedText = 'test text';

    act(() => {
      showToast({
        text1: expectedText,
        type: 'success',
      });
    });

    expect(getByTestId('toastTouchableContainer')).toBeVisible();
    expect(getByText(expectedText)).toHaveTextContent(expectedText);

    expect(showToastSpy).toHaveBeenCalled();
  });
});
