import React, { ReactElement, ReactNode, useCallback, useMemo } from 'react';
import Toast, {
  BaseToast,
  ErrorToast,
  type ToastConfigParams,
  type ToastProps as BaseToastProps,
  type ToastShowParams,
} from 'react-native-toast-message';
import { TouchableOpacity, useWindowDimensions, View } from 'react-native';
import type { NavigationProp } from '@react-navigation/native';
import {
  CheckCircleFill,
  Cross,
  InformationCircleFill,
  WarningCircleFill,
} from '@cat-home-experts/mortar-iconography-native';
import {
  createMortarStyles,
  spacing,
  palette as staticPalette,
  adjustMortarIconMargins,
} from '@cat-home-experts/react-native-utilities';
import { useDeviceMediaQuery } from 'src/hooks/useDeviceMediaQuery';
import { Portal, FullWindowOverlay } from 'src/components/Portal';
import { IS_ANDROID, IS_IOS, IS_WEB } from 'src/constants';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { Typography } from '@cat-home-experts/react-native-components';

const NOTIFICATION_MARGIN = spacing(6);
const ICON_SIZE = 16;
const TRAILING_ICON_SIZE = 16;

type ToastType = 'error' | 'success' | 'info' | 'undo';
type ToastConfig = Record<
  ToastType,
  (params: ToastConfigParams<unknown>) => React.ReactNode
>;

interface TrailingTextProps {
  trailingText?: string;
  onTrailingTextPress?: () => void;
}
interface ToastProps extends BaseToastProps {}

export const { hide: hideToast } = Toast;

export type ToastParams = Omit<ToastShowParams, 'type'> & {
  type?: ToastType;
};

export const Toaster = (): ReactElement => {
  const { width } = useWindowDimensions();

  const toastWidth = useDeviceMediaQuery({
    mobile: width - NOTIFICATION_MARGIN,
    tablet: width / 2,
  });

  const isDesktop = useDesktopMediaQuery();

  const toastWidths = useMemo(() => {
    return IS_WEB && isDesktop ? styles.desktopWidths : { width: toastWidth };
  }, [toastWidth, isDesktop]);

  const renderTrailingIcon = ({
    trailingText,
    onTrailingTextPress,
  }: TrailingTextProps): ReactNode => {
    return (
      <View style={styles.trailingWrapper}>
        {trailingText && (
          <TouchableOpacity
            style={styles.trailingText}
            onPress={onTrailingTextPress}
          >
            <Typography useVariant="textLinkSMSemiBold">
              {trailingText}
            </Typography>
          </TouchableOpacity>
        )}
        {IS_WEB && (
          <TouchableOpacity onPress={() => hideToast()}>
            <Cross
              size={TRAILING_ICON_SIZE}
              color={staticPalette.mortarV3.tokenNeutral800}
              style={styles.trailingIcon}
            />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderSuccess = useCallback(
    (props: ToastProps) => (
      <BaseToast
        {...props}
        style={[styles.common, toastWidths]}
        contentContainerStyle={styles.commonContainer}
        text1Style={styles.commonText}
        text1NumberOfLines={2}
        text2Style={styles.commonSubText}
        text2NumberOfLines={2}
        renderLeadingIcon={() => (
          <CheckCircleFill
            size={ICON_SIZE}
            color={staticPalette.mortarV3.tokenSuccess500}
            style={styles.commonIcon}
          />
        )}
        renderTrailingIcon={() => renderTrailingIcon({})}
      />
    ),
    [toastWidths],
  );

  const renderError = useCallback(
    (props: ToastProps) => (
      <ErrorToast
        {...props}
        style={[styles.common, toastWidths]}
        contentContainerStyle={styles.commonContainer}
        text1Style={styles.commonText}
        text1NumberOfLines={2}
        text2Style={styles.commonSubText}
        text2NumberOfLines={2}
        renderLeadingIcon={() => (
          <WarningCircleFill
            size={ICON_SIZE}
            color={staticPalette.mortarV3.tokenAttention500}
            style={styles.commonIcon}
          />
        )}
        renderTrailingIcon={() => renderTrailingIcon({})}
      />
    ),
    [toastWidths],
  );

  const renderInfo = useCallback(
    (props: ToastProps) => (
      <ErrorToast
        {...props}
        style={[styles.common, toastWidths]}
        contentContainerStyle={styles.commonContainer}
        text1Style={styles.commonText}
        text1NumberOfLines={2}
        text2Style={styles.commonSubText}
        text2NumberOfLines={2}
        renderLeadingIcon={() => (
          <InformationCircleFill
            size={ICON_SIZE}
            color={staticPalette.mortarV3.tokenDefault700}
            style={styles.commonIcon}
          />
        )}
        renderTrailingIcon={() => renderTrailingIcon({})}
      />
    ),
    [toastWidths],
  );

  const renderUndo = useCallback(
    ({ ...props }: ToastProps) => (
      <BaseToast
        {...props}
        style={[styles.common, toastWidths]}
        contentContainerStyle={styles.commonContainer}
        text1Style={styles.commonText}
        text1NumberOfLines={2}
        text2Style={styles.commonSubText}
        text2NumberOfLines={2}
        onPress={props.onHide}
        renderTrailingIcon={() =>
          renderTrailingIcon({
            trailingText: 'Undo',
            onTrailingTextPress: props.onPress,
          })
        }
      />
    ),
    [toastWidths],
  );

  const config: ToastConfig = useMemo(
    () => ({
      success: renderSuccess,
      error: renderError,
      info: renderInfo,
      undo: renderUndo,
    }),
    [renderSuccess, renderError, renderInfo, renderUndo],
  );

  return (
    <Portal>
      <FullWindowOverlay>
        <Toast config={config} />
      </FullWindowOverlay>
    </Portal>
  );
};

const getToastVisibilityTime = (toastParams: ToastParams) => {
  if (toastParams.visibilityTime) {
    return toastParams.visibilityTime;
  }

  if (toastParams.type === 'undo') {
    return 5000;
  }

  const totalLength =
    (toastParams.text1?.length || 0) + (toastParams.text2?.length || 0);

  if (totalLength > 50) {
    return 5000;
  }

  return 4000;
};

export const showToast = (toastParams: ToastParams): void => {
  Toast.show({
    ...toastParams,
    autoHide: toastParams.autoHide || true,
    position: toastParams.position || 'bottom',
    visibilityTime: getToastVisibilityTime(toastParams),
    bottomOffset:
      toastParams.bottomOffset ||
      (IS_IOS && spacing(13)) ||
      (IS_ANDROID && spacing(10)) ||
      spacing(2),
  });
};

/*
 * Shows a toast with an undo button that will call the onPress function,
 * and a callback function if the user dismisses the toast or does not press the undo button
 * @param {ToastParams} toastParams - The parameters for the toast
 * @param {NavigationProp} navigation - The navigation prop to add a listener to
 * @param {() => void} callback - The callback function to call if the user does not press the undo button
 */
export const showUndoToast = ({
  navigation,
  callback,
  ...toastParams
}: ToastParams & {
  callback: () => void;
  navigation: Omit<NavigationProp<ReactNavigation.RootParamList>, 'getState'>;
}): void => {
  let timerIndex: NodeJS.Timeout | null;

  showToast({
    ...toastParams,
    type: 'undo',
    onShow: () => {
      timerIndex = setTimeout(() => {
        hideToast();
      }, getToastVisibilityTime(toastParams));
      navigation.addListener('blur', () => hideToast());
    },
    onHide: () => {
      toastParams.onHide?.();
      if (timerIndex) {
        clearTimeout(timerIndex);
        callback();
      }
    },
    onPress: () => {
      toastParams.onPress?.();
      if (timerIndex) {
        clearTimeout(timerIndex);
        timerIndex = null;
      }

      navigation.removeListener('blur', () => hideToast());
      hideToast();
    },
  });
};

const styles = createMortarStyles(({ palette }) => ({
  common: {
    zIndex: 99999,
    borderColor: palette.mortarV3.tokenNeutral400,
    borderRadius: spacing(1),
    borderWidth: 1,
    borderLeftWidth: 1,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 10,
    elevation: 16,
    flexDirection: 'row',
    alignItems: 'center',
    height: 'auto',
    paddingHorizontal: spacing(2),
    paddingVertical: spacing(2),
  },
  commonContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing(1),
  },
  commonText: {
    color: palette.mortarV3.tokenNeutral800,
    fontSize: 14,
    lineHeight: spacing(2.5),
    fontFamily: 'openSans_semi-bold',
    fontWeight: 'normal',
    flex: 3,
  },
  commonSubText: {
    color: palette.mortarV3.tokenNeutral600,
    fontSize: 12,
    lineHeight: spacing(2),
    fontFamily: 'openSans_regular',
    fontWeight: 'normal',
  },
  commonIcon: {
    marginHorizontal: adjustMortarIconMargins(ICON_SIZE),
  },
  trailingText: {},
  trailingWrapper: {
    flexDirection: 'row',
    gap: spacing(1),
    alignItems: 'center',
    justifyContent: 'center',
  },
  trailingIcon: {
    marginHorizontal: adjustMortarIconMargins(TRAILING_ICON_SIZE),
  },
  desktopWidths: {
    minWidth: 320,
    maxWidth: 540,
  },
}));
