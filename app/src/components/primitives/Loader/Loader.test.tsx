import React from 'react';

import { render, cleanup } from '@testing-library/react-native';

import { testIds as spinnerTestIds } from 'src/components/primitives/Spinner/Spinner';
import { Loader } from './Loader';

describe('Components | Loader', () => {
  afterEach(cleanup);

  it('can render successfully', () => {
    const { getByTestId } = render(<Loader />);

    const loader = getByTestId(spinnerTestIds.ROOT);

    expect(loader).toBeDefined();
  });

  it('can be passed a message', () => {
    const testMessage = 'this is a test';
    const { getByText } = render(<Loader message={testMessage} />);

    const loaderText = getByText(testMessage);

    expect(loaderText).toBeDefined();
  });
});
