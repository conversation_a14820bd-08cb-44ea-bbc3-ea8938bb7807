import React, { ReactElement } from 'react';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';

import { Typography } from '@cat-home-experts/react-native-components';

import { tokenColorPrimaryWhite } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { Spinner } from 'src/components/primitives/Spinner';
import { createTestIds } from '@cat-home-experts/react-native-utilities';

const testIds = createTestIds('loader', {});

interface LoaderProps {
  style?: StyleProp<ViewStyle>;
  message?: string;
  spinnerSize?: number;
}

export const Loader = ({
  message,
  spinnerSize,
  style,
}: LoaderProps): ReactElement => {
  return (
    <View style={[styles.container, style]} testID={testIds.ROOT}>
      <Spinner size={spinnerSize} />

      {message && (
        <View style={styles.messageContainer}>
          <Typography use="bodyRegular" isMuted isCentred>
            {message}
          </Typography>
        </View>
      )}
    </View>
  );
};

Loader.testIds = testIds;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    bottom: 0,
    top: 0,
    left: 0,
    right: 0,
    position: 'absolute',
    alignItems: 'center',
    backgroundColor: tokenColorPrimaryWhite,
    justifyContent: 'center',
  },
  messageContainer: {
    marginTop: 28,
    marginHorizontal: 50,
  },
});
