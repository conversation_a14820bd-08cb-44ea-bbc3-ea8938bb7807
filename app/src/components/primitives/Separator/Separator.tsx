import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import React, { ReactElement } from 'react';
import { View, type StyleProp, type ViewStyle } from 'react-native';

export const Separator = ({
  style,
}: {
  style?: StyleProp<ViewStyle>;
}): ReactElement => <View style={[styles.separator, style]} />;

const styles = createMortarStyles(({ palette }) => ({
  separator: {
    backgroundColor: palette.mortar.tokenColorLighterGrey,
    height: 1,
    width: '100%',
  },
}));
