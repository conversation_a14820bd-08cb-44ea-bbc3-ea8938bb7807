import React, { ReactElement, useEffect, useMemo } from 'react';
import Svg, { Path } from 'react-native-svg';
import Animated, {
  cancelAnimation,
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { getTestID } from 'src/utilities/testIds';
import { StyleProp, ViewStyle } from 'react-native';
import { useXLargeDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { tokenColorPrimaryBlue } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';

interface SpinnerProps {
  style?: StyleProp<ViewStyle>;
  size?: number;
}

const rootTestId = 'spinner';

export const testIds = {
  ROOT: rootTestId,
  SVG: `${rootTestId}-svg`,
};

export const Spinner = ({ style, size }: SpinnerProps): ReactElement => {
  const rotation = useSharedValue(0);
  const isLargeScreen = useXLargeDesktopMediaQuery();

  const getSize = useMemo(() => {
    if (!size) {
      return isLargeScreen ? 96 : 64;
    }

    return size;
  }, [isLargeScreen, size]);

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, {
        duration: 800,
        easing: Easing.linear,
      }),
      -1,
    );
    return () => cancelAnimation(rotation);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{ rotateZ: `${rotation.value}deg` }],
    };
  }, [rotation.value]);

  return (
    <Animated.View
      style={[animatedStyles, style]}
      testID={getTestID(testIds.ROOT)}
    >
      <Svg
        width={getSize}
        height={getSize}
        fill="none"
        viewBox="0 0 22 22"
        testID={getTestID(testIds.SVG)}
      >
        <Path
          d="M20 11a9 9 0 1 1-9-9"
          stroke={tokenColorPrimaryBlue}
          strokeWidth={3}
          strokeLinecap="round"
        />
      </Svg>
    </Animated.View>
  );
};
