import React from 'react';
import { StyleSheet } from 'react-native';
import { render, cleanup } from '@testing-library/react-native';
import { testIds, Spinner } from './Spinner';

describe('Spinner', () => {
  afterEach(cleanup);

  it('can render successfully', () => {
    const { getByTestId } = render(<Spinner />);

    const spinner = getByTestId(testIds.ROOT);

    expect(spinner).toBeTruthy();
  });

  it('can accept a size prop successfully', () => {
    const { getByTestId } = render(<Spinner size={666} />);

    const spinnerSvg = getByTestId(testIds.SVG);

    expect(spinnerSvg.props).toHaveProperty('height', 666);
    expect(spinnerSvg.props).toHaveProperty('width', 666);
  });

  it('can accept a style prop successfully', () => {
    const { getByTestId } = render(
      // eslint-disable-next-line react-native/no-color-literals, react-native/no-inline-styles
      <Spinner style={{ backgroundColor: 'red' }} />,
    );

    const spinner = getByTestId(testIds.ROOT);
    const containerStyle = StyleSheet.flatten(spinner.props.style);

    expect(containerStyle).toHaveProperty('backgroundColor', 'red');
  });
});
