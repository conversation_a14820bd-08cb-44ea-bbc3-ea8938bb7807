import React, { useState, ReactElement } from 'react';
import { Keyboard, StyleSheet, View } from 'react-native';
import NativeDatePicker from 'react-native-date-picker';
import { format } from 'date-fns';

import {
  tokenColorBlack,
  tokenColorBlueGrey,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { Typography } from '@cat-home-experts/react-native-components';
import { createTestIds } from '@cat-home-experts/react-native-utilities';
import { IS_ANDROID } from 'src/constants';
import { IconInput } from '../IconInput';
import type { SimpleDatePickerProps } from './SimpleDatePicker.types';

const TEST_IDS = createTestIds('simple-date-picker', {
  DATE_PICKER: 'date-picker',
  VALUE: 'input-value',
});

export const SimpleDatePicker = ({
  onConfirm,
  shownDate = new Date(),
  testID,
  sectionHeader,
  minimumDate,
  maximumDate,
  placeholder,
  contentContainerStyle,
  containerStyle,
}: SimpleDatePickerProps): ReactElement => {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState<boolean>(false);

  const handleConfirm = (selectedDate: Date) => {
    onConfirm(selectedDate);
    setIsDatePickerOpen(false);
  };

  const handleDatePickerCancel = () => {
    setIsDatePickerOpen(false);
  };

  const handleDatePickerOpen = () => {
    setIsDatePickerOpen(true);
    Keyboard.dismiss();
  };

  return (
    <View style={containerStyle}>
      {sectionHeader && (
        <Typography use="bodySmall" style={styles.sectionHeader}>
          {sectionHeader}
        </Typography>
      )}
      <IconInput
        style={contentContainerStyle}
        iconName="calendar"
        onPress={handleDatePickerOpen}
      >
        <View style={styles.container} testID={testID}>
          <Typography
            use="bodyRegular"
            style={[styles.text, styles.darkText]}
            testID={TEST_IDS.VALUE}
          >
            {placeholder ? placeholder : format(shownDate, 'dd MMMM yyyy')}
          </Typography>
          <NativeDatePicker
            modal
            theme={IS_ANDROID ? 'light' : 'auto'}
            open={isDatePickerOpen}
            date={shownDate}
            mode="date"
            onConfirm={handleConfirm}
            onCancel={handleDatePickerCancel}
            minimumDate={minimumDate}
            maximumDate={maximumDate}
          />
        </View>
      </IconInput>
    </View>
  );
};

SimpleDatePicker.testIds = TEST_IDS;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
  },
  text: { alignSelf: 'center' },
  darkText: {
    color: tokenColorBlack,
  },
  sectionHeader: {
    color: tokenColorBlueGrey,
    paddingTop: 16,
    paddingBottom: 4,
    paddingHorizontal: 24,
  },
});
