import React, { ReactElement, useCallback, useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { format } from 'date-fns';
import {
  tokenColorBlack,
  tokenColorBlueGrey,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { Typography } from '@cat-home-experts/react-native-components';
import { IconInput } from '../IconInput';
import type { SimpleDatePickerProps } from './SimpleDatePicker.types';
import './dateInput.css';

export function SimpleDatePicker({
  onConfirm,
  shownDate = new Date(),
  testID,
  sectionHeader,
  containerStyle,
  contentContainerStyle,
  placeholder,
  maximumDate,
  minimumDate,
}: SimpleDatePickerProps): ReactElement {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setIsOpen(false);
  }, []);

  const handleOnPress = useCallback(() => {
    setIsOpen(true);
  }, []);

  const onDatePickerChange = useCallback(
    (newDate: Date | null) => {
      if (newDate) {
        onConfirm(newDate);
      }
    },
    [onConfirm],
  );

  return (
    <View style={(styles.container, containerStyle)}>
      {sectionHeader && (
        <Typography use="bodySmall" style={styles.sectionHeader}>
          {sectionHeader}
        </Typography>
      )}

      <IconInput
        style={contentContainerStyle}
        iconName="calendar"
        onPress={handleOnPress}
      >
        <View>
          <Typography use="bodyRegular" style={styles.darkText} testID={testID}>
            {placeholder ? placeholder : format(shownDate, 'dd MMMM yyyy')}
          </Typography>

          <DatePicker
            open={isOpen}
            selected={shownDate}
            onChange={onDatePickerChange}
            onClickOutside={() => setIsOpen(false)}
            minDate={minimumDate}
            maxDate={maximumDate}
          />
        </View>
      </IconInput>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { zIndex: 12 },
  darkText: {
    color: tokenColorBlack,
  },
  sectionHeader: {
    color: tokenColorBlueGrey,
    paddingTop: 16,
    paddingBottom: 4,
    paddingHorizontal: 24,
  },
});
