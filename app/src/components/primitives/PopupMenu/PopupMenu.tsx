/* eslint-disable react/no-array-index-key */
import React, { PropsWithChildren, ReactElement } from 'react';
import { StyleSheet, View } from 'react-native';
import { Icon, Typography } from '@cat-home-experts/react-native-components';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from 'react-native-popup-menu';
import {
  tokenColorBlueGrey,
  tokenColorPrimaryBlue,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';

const rootTestId = 'popup-menu';
const testIds = {
  ROOT: rootTestId,
  MENU_OPTION: `${rootTestId}-menu-option`,
  MENU_TRIGGER: `${rootTestId}-menu-trigger`,
};

type PopupMenuProps<T = string | number> = PropsWithChildren<{
  options: ReadonlyArray<T>;
  selectedValue: T;
  onSelectOption: (index: number) => void;
  placement?: 'top' | 'right' | 'bottom' | 'left' | 'auto';
  testID?: string;
}>;

export function PopupMenu({
  options,
  children,
  selectedValue,
  onSelectOption,
  placement = 'auto',
  testID,
}: PopupMenuProps): ReactElement {
  const selectedValueIndex = options.findIndex(
    (option) => option === selectedValue,
  );

  return (
    <View testID={testID || testIds.ROOT}>
      <Menu
        onSelect={(value) => onSelectOption(value)}
        renderer={renderers.Popover}
        rendererProps={{ placement }}
      >
        <MenuOptions>
          {options.map((optionValue, idx) => (
            <MenuOption value={idx} key={idx}>
              <View style={styles.option} testID={testIds.MENU_OPTION}>
                <Typography use="bodyBold">{optionValue}</Typography>
                {selectedValueIndex === idx ? (
                  <Icon name="circle-dot" style={styles.activeOptionIcon} />
                ) : (
                  <Icon
                    name="circle-outline"
                    style={styles.inactiveOptionIcon}
                  />
                )}
              </View>
            </MenuOption>
          ))}
        </MenuOptions>
        <MenuTrigger testID={testIds.MENU_TRIGGER}>{children}</MenuTrigger>
      </Menu>
    </View>
  );
}

PopupMenu.testIds = testIds;

const styles = StyleSheet.create({
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 6,
    width: 200,
  },
  activeOptionIcon: {
    color: tokenColorPrimaryBlue,
  },
  inactiveOptionIcon: {
    color: tokenColorBlueGrey,
  },
});
