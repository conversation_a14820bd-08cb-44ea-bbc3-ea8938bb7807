import React from 'react';
import { Text } from 'react-native';
import { cleanup, render } from '@testing-library/react-native';
import { PopupMenu } from './PopupMenu';

const mockOptions = ['foo', 'bar', 'baz'];
const triggerText = 'triggerText';

const mockHandleSelectOption = jest.fn();

// There is a known issue of menu options not coming up in testing, hence just this limited test
// gh issues:
// https://github.com/instea/react-native-popup-menu/issues/253
// https://github.com/instea/react-native-popup-menu/issues/243

describe('Components | PopupMenu', () => {
  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
    cleanup();
  });
  it('renders the component and trigger area', () => {
    const { getByTestId } = render(
      <PopupMenu
        options={mockOptions}
        selectedValue={mockOptions[0]}
        onSelectOption={mockHandleSelectOption}
      >
        <Text>{triggerText}</Text>
      </PopupMenu>,
    );

    expect(getByTestId(PopupMenu.testIds.ROOT)).toBeDefined();
    expect(getByTestId(PopupMenu.testIds.MENU_TRIGGER)).toBeDefined();
  });
});
