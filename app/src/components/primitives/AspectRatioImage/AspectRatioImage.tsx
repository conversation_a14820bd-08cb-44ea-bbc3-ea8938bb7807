/* eslint-disable react-native/no-inline-styles */
import React, { ReactElement, useCallback, useState } from 'react';
import { Image, ImageProps, LayoutChangeEvent } from 'react-native';

type AspectRatioImageProps = {
  aspectRatio: number;
  dimensionToCalculate: 'width' | 'height';
} & ImageProps;

/**
 * Image component which calculates the other dimension respecting the aspect ratio
 */
export function AspectRatioImage({
  dimensionToCalculate,
  aspectRatio,
  ...restProps
}: AspectRatioImageProps): ReactElement {
  const [calculatedWidth, setCalculatedWidth] = useState<number>(0);
  const [calculatedHeight, setCalculatedHeight] = useState<number>(0);
  const onLayout = useCallback(
    (event: LayoutChangeEvent) => {
      if (dimensionToCalculate === 'width') {
        setCalculatedWidth(event.nativeEvent.layout.height * aspectRatio);
      } else if (dimensionToCalculate === 'height') {
        setCalculatedHeight(event.nativeEvent.layout.width / aspectRatio);
      }
    },
    [aspectRatio, dimensionToCalculate],
  );

  return (
    <Image
      {...restProps}
      style={[
        restProps.style,
        dimensionToCalculate === 'height' && {
          width: '100%',
          height: calculatedHeight,
        },
        dimensionToCalculate === 'width' && {
          width: calculatedWidth,
          height: '100%',
        },
      ]}
      onLayout={onLayout}
    />
  );
}
