import React from 'react';
import { View } from 'react-native';
import { render, cleanup, fireEvent } from '@testing-library/react-native';
import { IconInput } from './IconInput';

describe('components | primitives | IconInput', () => {
  const mockHandlePress = jest.fn();

  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('renders the component', () => {
    const childTestId = 'fakeChildComponent';
    const { getByTestId } = render(
      <IconInput iconName="hash">
        <View testID={childTestId} />
      </IconInput>,
    );

    expect(getByTestId(IconInput.testIds.ROOT)).toBeDefined();
    expect(getByTestId(childTestId)).toBeDefined();
  });

  it('calls onPress if available', () => {
    const childTestId = 'fakeChildComponent';

    const { getByTestId } = render(
      <IconInput iconName="hash" showIcon={false} onPress={mockHandlePress}>
        <View testID={childTestId} />
      </IconInput>,
    );

    const iconInput = getByTestId(IconInput.testIds.ROOT);

    fireEvent.press(iconInput);

    expect(mockHandlePress).toHaveBeenCalledTimes(1);
  });

  it('renders the icon with custom styles', () => {
    const childTestId = 'fakeChildComponent';
    const customStyles = { backgroundColor: 'red' };
    const { getByTestId } = render(
      <IconInput iconName="hash" style={customStyles}>
        <View testID={childTestId} />
      </IconInput>,
    );

    expect(getByTestId(IconInput.testIds.ROOT)).toHaveStyle(customStyles);
  });
});
