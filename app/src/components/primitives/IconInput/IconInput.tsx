import React, { ReactElement } from 'react';
import { Pressable, StyleProp, ViewStyle } from 'react-native';
import { Icon } from '@cat-home-experts/react-native-components';
import { createTestIds } from 'src/utilities/testIds';
import { IconsId } from '@cat-home-experts/iconography/dist/icons';
import {
  createMortarStyles,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';

interface IconInputProps {
  /**
   * Body of the input
   */
  children: ReactElement;
  /**
   * Icon identifier
   */
  iconName: IconsId;
  /**
   * flag for showing/hiding icon
   */
  showIcon?: boolean;
  /**
   * Optional pressable action
   */
  onPress?: () => void;
  /**
   * Test ID
   */
  testID?: string;
  /**
   * Custom styles
   */
  style?: StyleProp<ViewStyle>;
  /**
   * Custom accessibility label
   */
  accessibilityLabel?: string;
}

const TEST_IDS = createTestIds('icon-input', {
  ICON: 'icon',
});

export const IconInput = ({
  children,
  iconName,
  onPress,
  showIcon = true,
  style,
  testID,
  accessibilityLabel,
}: IconInputProps): ReactElement => {
  return (
    <Pressable
      onPress={onPress}
      style={[styles.container, style]}
      testID={testID || TEST_IDS.ROOT}
      accessibilityLabel={accessibilityLabel}
    >
      {showIcon ? (
        <Icon
          style={styles.icon}
          testID={TEST_IDS.ICON}
          name={iconName}
          color={staticPalette.mortarV3.tokenDefault500}
        />
      ) : null}

      {children}
    </Pressable>
  );
};

IconInput.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    textAlignVertical: 'center',
    flexDirection: 'row',
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(2),
    alignItems: 'center',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
  },
  icon: {
    paddingRight: spacing(1),
    alignSelf: 'center',
  },
}));
