import React, { ComponentProps } from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import { Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { ContactType, handlePhoneContact } from 'src/utilities/phone';

interface PhoneLinkProps extends ComponentProps<typeof Typography> {
  phoneNumber: string;
  onPress?: () => void;
}

const TEST_IDS = createTestIds('phone-link', {});

export const PhoneLink: React.NativeFC<PhoneLinkProps, typeof TEST_IDS> = ({
  phoneNumber,
  onPress,
  style,
  testID,
  ...typographyProps
}) => {
  // Methods
  const handlePress = async () => {
    onPress?.();

    handlePhoneContact(ContactType.call, phoneNumber.replace(/\s/g, ''));
  };

  return (
    <TouchableWithoutFeedback
      onPress={handlePress}
      testID={testID ?? TEST_IDS.ROOT}
      accessible
      accessibilityLabel={`Call ${phoneNumber}`}
    >
      <Typography {...typographyProps} style={[styles.link, style]}>
        {phoneNumber}
      </Typography>
    </TouchableWithoutFeedback>
  );
};

PhoneLink.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette }) => ({
  root: {
    alignItems: 'center',
  },
  link: {
    color: palette.mortar.tokenColorSystemLinkBlue,
  },
}));
