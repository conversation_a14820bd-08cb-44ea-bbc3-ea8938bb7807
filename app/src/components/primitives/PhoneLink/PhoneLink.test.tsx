import React from 'react';
import {
  fireEvent,
  render,
  waitFor,
  cleanup,
} from '@testing-library/react-native';
import { handlePhoneContact } from 'src/utilities/phone';
import { PhoneLink } from './PhoneLink';

jest.mock('src/utilities/phone', () => ({
  ContactType: jest.requireActual('src/utilities/phone').ContactType,
  handlePhoneContact: jest.fn(),
}));

describe('Components | Primitives | PhoneLink', () => {
  afterEach(cleanup);
  it('renders as expected', () => {
    // Arrange
    const phoneNumber = '01383 123123';

    // Act
    const { getByTestId } = render(
      <PhoneLink phoneNumber={phoneNumber} onPress={jest.fn()} />,
    );
    const root = getByTestId(PhoneLink.testIds.ROOT);

    // Assert
    expect(root).toHaveProp('accessibilityLabel', `Call ${phoneNumber}`);
    expect(root).toHaveTextContent(phoneNumber);
  });

  it('Calls onPress in addition to opening the call link', async () => {
    // Arrange
    const phoneNumber = '01383 123123';
    const onPress = jest.fn();

    // Act
    const { root } = render(
      <PhoneLink phoneNumber={phoneNumber} onPress={onPress} />,
    );

    fireEvent.press(root);

    // Assert
    expect(onPress).toHaveBeenCalled();
    await waitFor(() => {
      expect(handlePhoneContact).toHaveBeenCalledWith(
        'call',
        phoneNumber.replace(/\s/g, ''),
      );
    });
  });
});
