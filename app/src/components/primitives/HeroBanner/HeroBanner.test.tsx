import React from 'react';

import { render, fireEvent, cleanup } from '@testing-library/react-native';

import { HeroBanner } from './HeroBanner';

describe('Components | HeroBanner', () => {
  const onPressSpy = jest.fn();
  const logoUrl = 'logourl';

  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('renders the banner', async () => {
    const { findByTestId } = render(
      <HeroBanner
        logoUrl={logoUrl}
        onPress={onPressSpy}
        isLogoFlagged={false}
      />,
    );
    const bannerElement = await findByTestId(HeroBanner.testIds.ROOT);

    expect(bannerElement).toBeDefined();
  });

  it('renders the default camera logo', async () => {
    const { findByTestId } = render(
      <HeroBanner
        logoUrl={logoUrl}
        onPress={onPressSpy}
        isLogoFlagged={false}
      />,
    );
    const bannerLogoElement = await findByTestId(HeroBanner.testIds.ADD_LOGO);

    expect(bannerLogoElement).toBeDefined();
  });

  it('can trigger onPress when pressing logo', async () => {
    const { findByTestId } = render(
      <HeroBanner
        logoUrl={logoUrl}
        onPress={onPressSpy}
        isLogoFlagged={false}
      />,
    );
    const bannerLogoElement = await findByTestId(HeroBanner.testIds.ADD_LOGO);

    fireEvent.press(bannerLogoElement);

    expect(onPressSpy).toHaveBeenCalledTimes(1);
  });

  it('logo image has lower opacity if it is flagged', async () => {
    const { getByTestId } = render(
      <HeroBanner isLogoFlagged logoUrl={logoUrl} onPress={onPressSpy} />,
    );

    expect(getByTestId(HeroBanner.testIds.COMPANY_LOGO)).toHaveStyle({
      opacity: 0.5,
    });
  });
});
