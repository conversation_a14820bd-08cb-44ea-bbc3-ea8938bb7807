import React, { ReactElement } from 'react';
import { Image, Pressable, StyleSheet, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

import { Typography, Icon } from '@cat-home-experts/react-native-components';

import {
  tokenLegacyColorBrandPrimaryLight,
  tokenLegacyColorBrandPrimaryLighter,
} from '@cat-home-experts/design-tokens/dist/colours/brand/light/js/brand';
import {
  tokenColorPrimaryBlue,
  tokenColorSystemOrange,
  tokenColorPrimaryWhite,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';

import { getTestID } from 'src/utilities/testIds';

import { dateToDayParting } from 'src/utilities/dates/dateToDayParting';
import { dayPartingToGreeting } from 'src/utilities/greetings/dayPartingToGreeting';
import { useMobileMediaQuery } from 'src/hooks/useMediaQuery';

interface HeroBannerProps {
  logoUrl: string | undefined;
  isLogoFlagged: boolean;
  onPress: () => void;
}

const rootTestId = 'hero-banner';

const testIds = {
  ROOT: rootTestId,
  ADD_LOGO: `${rootTestId}-add-logo`,
  COMPANY_LOGO: `${rootTestId}-company-logo`,
};

export const HeroBanner = ({
  onPress,
  logoUrl,
  isLogoFlagged,
}: HeroBannerProps): ReactElement => {
  const isMobile = useMobileMediaQuery();

  const dayParting = dateToDayParting(new Date());
  const greeting = dayPartingToGreeting(dayParting);

  const setMarginTop = () => ({
    marginTop: isMobile ? 30 : 20,
  });

  return (
    <View testID={getTestID(testIds.ROOT)} style={styles.container}>
      <LinearGradient
        start={{ x: 0.0, y: 0.25 }}
        end={{ x: 0.5, y: 1.0 }}
        locations={[0, 0.7]}
        colors={[
          tokenColorPrimaryBlue as string,
          tokenLegacyColorBrandPrimaryLighter as string,
        ]}
        style={styles.gradient}
      />
      <View
        style={[
          styles.contentContainer,
          !isMobile && styles.contentContainerDesktop,
        ]}
      >
        <Typography
          use="header"
          style={[
            styles.greeting,
            isMobile ? styles.greetingMobile : styles.greetingDesktop,
          ]}
        >
          {greeting}
        </Typography>
        <Pressable testID={getTestID(testIds.ADD_LOGO)} onPress={onPress}>
          {logoUrl ? (
            <>
              <Image
                testID={getTestID(testIds.COMPANY_LOGO)}
                source={{
                  uri: logoUrl,
                }}
                resizeMode="contain"
                // eslint-disable-next-line react-native/no-inline-styles
                style={[styles.bubble, { opacity: isLogoFlagged ? 0.5 : 1 }]}
              />
              {isLogoFlagged && (
                <Icon
                  name="warning-circle-fill"
                  size={24}
                  style={styles.warningIcon}
                />
              )}
            </>
          ) : (
            <View style={[styles.bubble, setMarginTop()]}>
              <Icon
                size={26}
                name="upload"
                color={tokenLegacyColorBrandPrimaryLight}
              />
              <Typography
                use="bodySmall"
                isCentred
                isMuted
                allowFontScaling={false}
              >
                {'Company logo'}
              </Typography>
            </View>
          )}
        </Pressable>
      </View>
    </View>
  );
};

HeroBanner.testIds = testIds;

const styles = StyleSheet.create({
  container: {
    zIndex: 4,
    height: 70,
  },
  contentContainer: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginHorizontal: 16,
  },
  contentContainerDesktop: {
    marginHorizontal: 24,
  },
  gradient: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    zIndex: -1,
  },
  greeting: {
    color: tokenColorPrimaryWhite,
  },
  greetingMobile: {
    marginLeft: 0,
    marginBottom: 0,
  },
  greetingDesktop: {
    marginLeft: 20,
    marginBottom: 20,
  },
  bubble: {
    width: 80,
    height: 80,
    borderRadius: 16,
    backgroundColor: tokenColorPrimaryWhite,
    borderColor: tokenLegacyColorBrandPrimaryLight,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    top: 10,
  },
  warningIcon: {
    color: tokenColorSystemOrange,
    position: 'absolute',
    top: 5,
    right: 5,
  },
});
