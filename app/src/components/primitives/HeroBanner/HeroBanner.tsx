import React, { ReactElement } from 'react';
import {
  StyleSheet,
  View,
  useWindowDimensions,
  Pressable,
  Image,
  PixelRatio,
} from 'react-native';

import { Typography, Icon } from '@cat-home-experts/react-native-components';
import { tokenLegacyColorBrandPrimaryLight } from '@cat-home-experts/design-tokens/dist/colours/brand/light/js/brand';
import {
  tokenColorSystemOrange,
  tokenColorPrimaryWhite,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';

import Banner from 'src/assets/images/hero/hero-banner.svg';

import { dateToDayParting } from 'src/utilities/dates/dateToDayParting';
import { dayPartingToGreeting } from 'src/utilities/greetings/dayPartingToGreeting';

import { IS_IOS } from 'src/constants';
import { getTestID } from 'src/utilities/testIds';
import { useMobileMediaQuery } from 'src/hooks/useMediaQuery';

interface HeroBannerProps {
  logoUrl: string | undefined;
  isLogoFlagged: boolean;
  onPress: () => void;
}

const rootTestId = 'hero-banner';

const testIds = {
  ROOT: rootTestId,
  ADD_LOGO: `${rootTestId}-add-logo`,
  COMPANY_LOGO: `${rootTestId}-company-logo`,
};

export const HeroBanner = ({
  logoUrl,
  onPress,
  isLogoFlagged,
}: HeroBannerProps): ReactElement => {
  const { width } = useWindowDimensions();
  const isMobile = useMobileMediaQuery();
  const svgWidth = 320;
  const svgHeight = isMobile ? 128 : 64;
  const aspectRatio = svgWidth / svgHeight;

  const dayParting = dateToDayParting(new Date());
  const greeting = dayPartingToGreeting(dayParting);
  const fontScale = PixelRatio.getFontScale();

  const setGreetingStyles = () => ({
    marginLeft: isMobile ? 0 : 20,
    marginBottom: isMobile ? 10 : 20,
  });

  const setFontStyles = (): { fontSize: number } | Record<string, unknown> => {
    if (fontScale > 1.3 && isMobile) {
      return { fontSize: 15 };
    }

    return {};
  };

  const setContainerSizing = () => ({
    width,
    aspectRatio,
  });

  const setMarginTop = () => ({
    marginTop: isMobile ? 30 : 20,
  });

  const viewBox = `0 ${isMobile ? 0 : 46} ${svgWidth} ${svgHeight}`;

  return (
    <View
      testID={getTestID(testIds.ROOT)}
      style={[styles.container, setContainerSizing()]}
    >
      <Banner width="100%" height="100%" viewBox={viewBox} />
      <View style={styles.contentContainer}>
        <Typography
          use="subHeader"
          style={[styles.heading, setGreetingStyles(), setFontStyles()]}
          isInverse
        >
          {greeting}
        </Typography>
        <Pressable testID={getTestID(testIds.ADD_LOGO)} onPress={onPress}>
          {logoUrl ? (
            <>
              <Image
                testID={getTestID(testIds.COMPANY_LOGO)}
                source={{ uri: logoUrl }}
                // eslint-disable-next-line react-native/no-inline-styles
                style={[styles.bubble, { opacity: isLogoFlagged ? 0.5 : 1 }]}
                resizeMode="contain"
              />
              {isLogoFlagged && (
                <Icon
                  name="warning-circle-fill"
                  size={24}
                  style={styles.warningIcon}
                />
              )}
            </>
          ) : (
            <View style={[styles.bubble, setMarginTop()]}>
              <Icon
                size={26}
                name="upload"
                color={tokenLegacyColorBrandPrimaryLight}
              />
              <Typography
                use="bodySmall"
                isCentred
                isMuted
                allowFontScaling={false}
              >
                {'Company logo'}
              </Typography>
            </View>
          )}
        </Pressable>
      </View>
    </View>
  );
};

HeroBanner.testIds = testIds;

const styles = StyleSheet.create({
  container: {
    backgroundColor: tokenColorPrimaryWhite,
    zIndex: 4,
  },
  contentContainer: {
    position: 'absolute',
    top: '10%',
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginHorizontal: 24,
  },
  heading: {
    flex: 1,
  },
  bubble: {
    width: 80,
    height: 80,
    backgroundColor: tokenColorPrimaryWhite,
    borderRadius: IS_IOS ? 16 : 0,
    borderColor: tokenLegacyColorBrandPrimaryLight,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    overlayColor: tokenColorPrimaryWhite,
  },
  warningIcon: {
    color: tokenColorSystemOrange,
    position: 'absolute',
    top: 5,
    right: 5,
  },
});
