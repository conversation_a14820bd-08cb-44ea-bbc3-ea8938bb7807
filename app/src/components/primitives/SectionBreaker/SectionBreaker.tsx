import React, { ReactElement } from 'react';
import { View, StyleSheet } from 'react-native';
import {
  tokenColorLightBlue,
  tokenColorLightGrey,
} from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { Typography } from '@cat-home-experts/react-native-components';

import { getTestID } from 'src/utilities/testIds';

const rootTestId = 'section-breaker';

const testIds = {
  ROOT: rootTestId,
  LABEL: `${rootTestId}-label`,
};

interface SectionBreakerProps {
  label?: string;
}

export const SectionBreaker = ({
  label,
}: SectionBreakerProps): ReactElement => {
  return (
    <View testID={getTestID(testIds.ROOT)} style={styles.breaker}>
      <View
        style={[styles.middleBorder, label ? styles.labelSpacing : undefined]}
      />
      {label ? (
        <Typography
          testID={getTestID(testIds.LABEL)}
          style={styles.middleBorderText}
          isCentred
          accessibilityLabel={label}
        >
          {label}
        </Typography>
      ) : null}
    </View>
  );
};

SectionBreaker.testIds = testIds;

const styles = StyleSheet.create({
  breaker: {
    marginVertical: 24,
    alignItems: 'center',
  },
  middleBorder: {
    width: '100%',
    backgroundColor: tokenColorLightGrey,
    height: 1,
    position: 'absolute',
  },
  labelSpacing: {
    top: 12,
  },
  middleBorderText: {
    width: 230,
    backgroundColor: tokenColorLightBlue,
    zIndex: 2,
  },
});
