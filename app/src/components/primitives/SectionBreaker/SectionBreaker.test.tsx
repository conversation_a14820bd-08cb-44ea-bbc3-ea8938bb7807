import React from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { SectionBreaker } from './SectionBreaker';

describe('Components | Primatives | SectionBreaker', () => {
  afterEach(cleanup);

  it('can render successfully without label', () => {
    const { getByTestId, queryByTestId } = render(<SectionBreaker />);

    const fullWidthSectionBreaker = getByTestId(SectionBreaker.testIds.ROOT);
    const optionalLabel = queryByTestId(SectionBreaker.testIds.LABEL);

    expect(fullWidthSectionBreaker).toBeDefined();
    expect(optionalLabel).toBeNull();
  });

  it('can render with a label', () => {
    const { getByTestId } = render(<SectionBreaker label="test label" />);

    const fullWidthSectionBreaker = getByTestId(SectionBreaker.testIds.ROOT);
    const optionalLabel = getByTestId(SectionBreaker.testIds.LABEL);

    expect(fullWidthSectionBreaker).toBeDefined();
    expect(optionalLabel).toBeDefined();
  });
});
