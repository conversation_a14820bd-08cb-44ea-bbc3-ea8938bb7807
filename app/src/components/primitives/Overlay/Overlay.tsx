import React, { ReactElement } from 'react';
import { View, StyleSheet, ViewStyle, StyleProp } from 'react-native';

interface OverlayProps {
  isEnabled: boolean;
  opacity: number;
  children: ReactElement;
  wrapperStyles?: StyleProp<ViewStyle>;
}

export const Overlay = ({
  isEnabled,
  opacity,
  children,
  wrapperStyles,
}: OverlayProps): ReactElement => {
  return (
    <>
      {isEnabled ? (
        <View
          style={[styles.overlay, { opacity }, wrapperStyles]}
          accessible={false}
        >
          {children}
        </View>
      ) : (
        children
      )}
    </>
  );
};

const styles = StyleSheet.create({
  // TODO: upgrade-eslint - fix color literals
  // eslint-disable-next-line react-native/no-color-literals
  overlay: {
    flex: 1,
    backgroundColor: 'white',
  },
});
