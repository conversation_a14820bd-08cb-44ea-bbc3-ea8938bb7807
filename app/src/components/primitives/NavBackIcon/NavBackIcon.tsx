import React, { ReactElement } from 'react';
import {
  GestureResponderEvent,
  Insets,
  Pressable,
  StyleProp,
  StyleSheet,
  TextStyle,
  ViewStyle,
} from 'react-native';
import { tokenColorLightGrey } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { IconsId } from '@cat-home-experts/iconography/dist/icons';
import { Icon } from '@cat-home-experts/react-native-components';
import { getTestID } from 'src/utilities/testIds';
import { IS_ANDROID, IS_WEB } from 'src/constants';

type NavBackIconProps = {
  /**
   * Optional custom testId
   */
  testID?: string;
  /**
   * color override
   */
  color?: string;
  /**
   * onPress override
   */
  onPress?: (e?: GestureResponderEvent) => void;
  /**
   * override NavBackIcon styles
   */
  style?: StyleProp<TextStyle>;
  /**
   * override iconId
   */
  iconName?: IconsId;
  /**
   * disable press: default false
   */
  isDisabled?: boolean;
  /**
   * custom hitSlop
   */
  hitSlop?: number | Insets;
  /**
   * custom container style
   */
  containerStyle?: StyleProp<ViewStyle>;
};

const rootTestId = 'nav-back-icon';

const testIds = {
  ROOT: rootTestId,
};

export const NavBackIcon = ({
  testID,
  color = 'black',
  onPress,
  hitSlop = 10,
  containerStyle,
  style,
  iconName,
  isDisabled = false,
}: NavBackIconProps): ReactElement => {
  return (
    <Pressable
      testID={getTestID(testID || testIds.ROOT)}
      onPress={onPress}
      style={containerStyle}
      accessible
      disabled={isDisabled}
      accessibilityLabel="Back button"
      accessibilityHint="Navigates back to previous screen "
      accessibilityRole="link"
      hitSlop={hitSlop}
    >
      {iconName ? (
        <Icon
          name={iconName}
          color={isDisabled ? tokenColorLightGrey : color}
          size={16}
          style={[styles.icon, style as TextStyle]}
        />
      ) : (
        <Icon
          name={IS_ANDROID ? 'arrow-left' : 'chevron-left'}
          color={color}
          style={[styles.icon, style as TextStyle]}
        />
      )}
    </Pressable>
  );
};

NavBackIcon.testIds = testIds;

const styles = StyleSheet.create({
  icon: {
    marginLeft: IS_WEB ? 15 : 0,
  },
});
