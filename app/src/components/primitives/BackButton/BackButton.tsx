import React, { ReactElement } from 'react';
import { StyleSheet, Pressable, ViewStyle } from 'react-native';
import { tokenLegacyColorTextLinkDefault } from '@cat-home-experts/design-tokens/dist/colours/system/light/js/system';
import { IconLabel, Icon } from '@cat-home-experts/react-native-components';
import { useNavigation } from '@react-navigation/native';
import { IS_ANDROID } from 'src/constants';
import { getTestID } from 'src/utilities/testIds';

type BackButtonProps = {
  /**
   * Optional custom testId
   */
  testID?: string;
  /**
   * Optional title
   */
  title?: string;
  /**
   * override button styles
   */
  style?: ViewStyle;
  /**
   * color override
   */
  color?: string;
  /**
   * onPress override
   */
  onPress?: () => void;
};

const rootTestId = 'back-button';

const testIds = {
  ROOT: rootTestId,
};

export const BackButton = ({
  testID,
  style,
  onPress,
  title = 'Back',
  color = tokenLegacyColorTextLinkDefault,
}: BackButtonProps): ReactElement => {
  const { goBack } = useNavigation();

  const iconName = IS_ANDROID ? 'arrow-left' : 'chevron-left';
  return (
    <Pressable
      testID={getTestID(testID || testIds.ROOT)}
      onPress={onPress || goBack}
      style={[styles.button, style]}
    >
      {title ? (
        <IconLabel icon={iconName} label={title} color={color} />
      ) : (
        <Icon name={iconName} color={color} />
      )}
    </Pressable>
  );
};

BackButton.testIds = testIds;

const styles = StyleSheet.create({
  button: {
    marginLeft: 10,
  },
});
