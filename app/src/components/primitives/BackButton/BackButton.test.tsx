import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';

import { BackButton } from './BackButton';

const mockGoBack = jest.fn();
const mockNavigate = jest.fn();

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');

  return {
    ...actualNav,
    useNavigation: () => {
      return {
        goBack: mockGoBack,
        navigate: mockNavigate,
      };
    },
  };
});

describe('Components | BackButton', () => {
  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('renders', () => {
    const { getByTestId } = render(<BackButton />);

    const backButton = getByTestId(BackButton.testIds.ROOT);

    expect(backButton).toBeDefined();
  });
  it('accepts a custom testID', () => {
    const id = 'myId';
    const { getByTestId } = render(<BackButton testID={id} />);

    const backButton = getByTestId(id);

    expect(backButton).toBeDefined();
  });

  it('calls goBack by default', () => {
    const { getByTestId } = render(<BackButton />);

    const backButton = getByTestId(BackButton.testIds.ROOT);

    fireEvent.press(backButton);

    expect(mockGoBack).toHaveBeenCalled();
  });

  it('can pass custom onPress', () => {
    const mockOnPress = jest.fn();
    const { getByTestId } = render(<BackButton onPress={mockOnPress} />);

    const backButton = getByTestId(BackButton.testIds.ROOT);

    fireEvent.press(backButton);

    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('can override styles', () => {
    const style = { backgroundColor: 'pink' };
    const { getByTestId } = render(<BackButton style={style} />);

    const backButton = getByTestId(BackButton.testIds.ROOT);

    fireEvent.press(backButton);

    expect(backButton.props.style[1]).toEqual(style);
  });
});
