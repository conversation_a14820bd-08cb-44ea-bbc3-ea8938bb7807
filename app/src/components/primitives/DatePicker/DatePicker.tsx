import React, {
  useCallback,
  useMemo,
  useState,
  forwardRef,
  useEffect,
} from 'react';
import { Pressable, View } from 'react-native';
import { format, formatISO, parseISO } from 'date-fns';
import NativeDatePicker from 'react-native-date-picker';

import {
  InputField,
  Typography,
} from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';

import { getNearestDateInRange } from 'src/utilities/dates/getNearestDateInRange';
import { DATE_PICKER_TEST_IDS } from './constants';
import type { DatePickerProps } from './DatePickerProps';

export const DatePickerComponent = forwardRef<
  { focus: () => void },
  DatePickerProps
>(
  (
    {
      testID,
      label,
      placeholder,
      value,
      onDateChanged,
      onDatePickerShown,
      minimumDate = new Date(),
      maximumDate,
      isDisabled = false,
      isInvalid = false,
      errorText,
    },
    ref,
  ) => {
    // State
    const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

    // Computed Values
    const selectedDateValue = useMemo(() => {
      if (value == null) {
        return value;
      }

      return getNearestDateInRange(value, { minimumDate, maximumDate });
    }, [value, minimumDate, maximumDate]);

    // Methods
    const handlePressDateInput = useCallback(() => {
      setIsDatePickerOpen(true);
      onDatePickerShown();
    }, [onDatePickerShown]);

    const handleDateChanged = useCallback(
      (selectedDate: Date) => {
        setIsDatePickerOpen(false);

        // Fix for date parsing on hermes
        // https://github.com/henninghall/react-native-date-picker/issues/416
        // https://github.com/facebook/hermes/issues/647
        const parsedDate = parseISO(formatISO(selectedDate));
        onDateChanged(parsedDate);
      },
      [onDateChanged],
    );

    // Effects
    useEffect(() => {
      if (!ref) {
        return;
      }

      if (typeof ref === 'function') {
        ref({ focus: handlePressDateInput });
      }

      if ('current' in ref) {
        ref.current = { focus: handlePressDateInput };
      }
    }, [handlePressDateInput, ref]);

    return (
      <View testID={testID ?? DATE_PICKER_TEST_IDS.ROOT}>
        <InputField
          testID={DATE_PICKER_TEST_IDS.INPUT}
          label={label}
          placeholder={placeholder}
          hideFocusBorder
          onChangeText={() => null} // we just use the InputField to display the data
          value={value == null ? value : format(value, 'dd/MM/yyy')}
          editable={false}
          showSoftInputOnFocus={false}
          isDisabled={isDisabled}
          inputStyle={isInvalid ? styles.invalid : undefined}
          containerStyle={isDatePickerOpen && styles.focused}
        />
        <Pressable
          testID={DATE_PICKER_TEST_IDS.BUTTON}
          onPress={handlePressDateInput}
          disabled={isDisabled}
          style={styles.dateInputButton}
        />

        {!isDisabled && (
          <NativeDatePicker
            testID={DATE_PICKER_TEST_IDS.PICKER}
            modal
            theme="auto"
            open={isDatePickerOpen}
            minimumDate={minimumDate}
            maximumDate={maximumDate}
            date={selectedDateValue || minimumDate}
            mode="date"
            onConfirm={handleDateChanged}
            onCancel={() => {
              setIsDatePickerOpen(false);
            }}
          />
        )}

        {errorText && (
          <Typography
            testID={DATE_PICKER_TEST_IDS.ERROR_TEXT}
            style={styles.errorText}
            useVariant="labelRegular"
          >
            {errorText}
          </Typography>
        )}
      </View>
    );
  },
);

export const DatePicker = DatePickerComponent as typeof DatePickerComponent & {
  testIds: typeof DATE_PICKER_TEST_IDS;
};

DatePicker.testIds = DATE_PICKER_TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  dateInputButton: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  invalid: {
    color: palette.mortar.tokenColorPrimaryRed,
  },
  focused: {
    borderColor: palette.mortarV3.tokenDefault700,
  },
  errorText: {
    marginTop: spacing(1),
    color: palette.system.tokenLegacyColorTextError,
  },
}));
