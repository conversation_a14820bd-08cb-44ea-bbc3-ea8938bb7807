import React from 'react';
import { View } from 'react-native';
import { <PERSON>ton, InputField } from '@cat-home-experts/react-native-components';
import { DATE_PICKER_TEST_IDS } from 'src/components/primitives/DatePicker/constants';
import { fireEvent, within } from '@testing-library/react-native';
import type { DatePickerProps } from './DatePickerProps';
import { DatePicker } from './DatePicker';

/**
 * Better way of testing date picker.
 * @param testInstance
 * @param date
 * @example
 * ```ts
 * const dateField = getByTestId('some-date-field');
 * fireChangeDateEvent(dateField, new Date());
 * ```
 */
export function fireChangeDateEvent(
  testInstance: Parameters<typeof within>[0],
  date: Date,
): void {
  const picker = within(testInstance).getByTestId(DatePicker.testIds.PICKER);
  fireEvent(picker, 'confirm', date);
}

/**
 * Get test instance of input containing value of date field
 * @param testInstance
 * @example
 * ```ts
 * const datePicker = getByTestId('date-picker');
 * expect(getDateInput(datePicker)).toHaveProp('value', '12/11/1993');
 * ```
 */
export function getDateInput(
  testInstance: Parameters<typeof within>[0],
): Parameters<typeof within>[0] {
  return within(testInstance).getByTestId(InputField.testIds.INPUT);
}

/**
 * @deprecated please use fireChangeDateEvent instead
 * @param dateToSet
 */
export const generateDatePickerMock = (
  dateToSet: Date,
): React.FC<DatePickerProps> => {
  // eslint-disable-next-line @typescript-eslint/no-shadow
  function DatePicker({ onDateChanged }: DatePickerProps) {
    const handleDateChanged = () => onDateChanged(dateToSet);

    return (
      <View testID={DATE_PICKER_TEST_IDS.ROOT}>
        <Button
          testID={DATE_PICKER_TEST_IDS.BUTTON}
          onPress={handleDateChanged}
          label=""
        />
      </View>
    );
  }

  DatePicker.testIds = DATE_PICKER_TEST_IDS;

  return DatePicker;
};
