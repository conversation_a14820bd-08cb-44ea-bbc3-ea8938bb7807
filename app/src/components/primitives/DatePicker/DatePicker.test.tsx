import React from 'react';
import { fireEvent, render } from '@testing-library/react-native';
import { startOfDay } from 'date-fns';
import { DatePicker } from './DatePicker';

describe('Components | Primitive | DatePicker', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    // Act
    const { getByTestId } = render(
      <DatePicker
        label="pick a date"
        onDatePickerShown={jest.fn()}
        onDateChanged={jest.fn()}
      />,
    );

    // Assert
    expect(getByTestId(DatePicker.testIds.INPUT)).toBeDefined();
    expect(getByTestId(DatePicker.testIds.BUTTON)).toBeDefined();
    expect(getByTestId(DatePicker.testIds.PICKER)).toBeDefined();
  });

  it('calls onDatePickerShown when button pressed', () => {
    // Arrange
    const onDatePickerShown = jest.fn();

    // Act
    const { getByTestId } = render(
      <DatePicker
        label="pick a date"
        onDatePickerShown={onDatePickerShown}
        onDateChanged={jest.fn()}
      />,
    );

    fireEvent.press(getByTestId(DatePicker.testIds.BUTTON));

    // Assert
    expect(onDatePickerShown).toHaveBeenCalledTimes(1);
  });

  it('calls onDateChanged when confirm event is fired', () => {
    // Arrange
    const date = startOfDay(new Date());
    const onDateChanged = jest.fn();

    // Act
    const { getByTestId } = render(
      <DatePicker
        label="pick a date"
        onDatePickerShown={jest.fn()}
        onDateChanged={onDateChanged}
      />,
    );

    fireEvent(getByTestId(DatePicker.testIds.PICKER), 'confirm', date);

    // Assert
    expect(onDateChanged).toHaveBeenCalledWith(date);
  });

  it('shows error message when passed to component', () => {
    // Arrange
    const errorText = 'You cannot do that thing';

    // Act
    const { getByTestId, getByText } = render(
      <DatePicker
        label="pick a date"
        onDatePickerShown={jest.fn()}
        onDateChanged={jest.fn()}
        errorText={errorText}
      />,
    );

    // Assert
    expect(getByTestId(DatePicker.testIds.ERROR_TEXT)).toBeOnTheScreen();
    expect(getByText(errorText)).toBeOnTheScreen();
  });
});
