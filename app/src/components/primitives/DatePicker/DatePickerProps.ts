import type { ReactDatePickerCustomHeaderProps } from 'react-datepicker';

export type DatePickerProps = {
  testID?: string | undefined;
  label: string;
  placeholder?: string;
  value?: Date;
  onDateChanged: (date: Date) => void;
  onDatePickerShown: () => void;
  minimumDate?: Date;
  maximumDate?: Date;
  isDisabled?: boolean;
  isInvalid?: boolean;
  showYearDropdown?: boolean;
  showMonthDropdown?: boolean;
  renderCustomHeader?: (
    props: ReactDatePickerCustomHeaderProps,
  ) => React.ReactElement;
  errorText?: string;
};
