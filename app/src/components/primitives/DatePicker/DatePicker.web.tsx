import React, {
  useMemo,
  ReactElement,
  useCallback,
  useState,
  useEffect,
} from 'react';
import { createPortal } from 'react-dom';
import { Pressable, View, StyleSheet } from 'react-native';
import ReactDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { format, parse, isValid } from 'date-fns';
import { InputField } from '@cat-home-experts/react-native-components';
import { getNearestDateInRange } from 'src/utilities/dates/getNearestDateInRange';
import type { DatePickerProps } from './DatePickerProps';

export function DatePicker({
  label,
  placeholder,
  value,
  onDateChanged,
  onDatePickerShown,
  minimumDate = new Date(),
  maximumDate,
  isDisabled = false,
  renderCustomHeader,
  showYearDropdown = false,
  showMonthDropdown = false,
}: DatePickerProps): ReactElement {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState<boolean>(false);
  const [inputText, setInputText] = useState<string>('');

  const selectedDateValue = useMemo(
    () =>
      value == null
        ? value
        : getNearestDateInRange(value, { minimumDate, maximumDate }),
    [value, minimumDate, maximumDate],
  );

  const presentedDateValue = useMemo(
    () => (value ? format(value, 'dd/MM/yyyy') : ''),
    [value],
  );

  useEffect(() => {
    setInputText(presentedDateValue);
  }, [presentedDateValue]);

  const onPressDateInput = useCallback(() => {
    setIsDatePickerOpen(true);
    onDatePickerShown();
  }, [onDatePickerShown]);

  const handleDateChanged = useCallback(
    (selectedDate: Date | null) => {
      setIsDatePickerOpen(false);
      if (selectedDate) {
        onDateChanged(selectedDate);
      }
    },
    [onDateChanged],
  );

  const handleTextInput = useCallback(
    (text: string) => {
      setInputText(text);
      const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;

      if (!dateRegex.test(text)) {
        return;
      }

      const parsedDate = parse(text, 'dd/MM/yyyy', new Date());

      if (!isValid(parsedDate)) {
        return;
      }

      const dateInRange = getNearestDateInRange(parsedDate, {
        minimumDate,
        maximumDate,
      });

      onDateChanged(dateInRange);
    },
    [minimumDate, maximumDate, onDateChanged],
  );

  const handleInputBlur = useCallback(() => {
    setInputText(presentedDateValue);
  }, [presentedDateValue]);

  return (
    <View>
      <Pressable onPress={onPressDateInput} disabled={isDisabled}>
        <InputField
          label={label}
          placeholder={placeholder}
          hideFocusBorder
          onChangeText={handleTextInput}
          onBlur={handleInputBlur}
          value={inputText}
          isDisabled={isDisabled}
          style={styles.input}
        />
      </Pressable>
      <ReactDatePicker
        open={isDatePickerOpen}
        selected={selectedDateValue}
        onChange={handleDateChanged}
        onClickOutside={() => setIsDatePickerOpen(false)}
        minDate={minimumDate}
        maxDate={maximumDate}
        showPopperArrow={false}
        popperPlacement="bottom-start"
        popperContainer={({ children }) =>
          createPortal(children, document.body)
        }
        showYearDropdown={showYearDropdown}
        showMonthDropdown={showMonthDropdown}
        renderCustomHeader={renderCustomHeader}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  input: {
    cursor: 'pointer',
  },
});
