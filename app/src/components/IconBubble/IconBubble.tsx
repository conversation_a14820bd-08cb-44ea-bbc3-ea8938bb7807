import { tokenColorPrimaryBlue } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { IconsId } from '@cat-home-experts/iconography';
import { Icon, Typography } from '@cat-home-experts/react-native-components';
import { TypographyProps } from '@cat-home-experts/react-native-components/src/ui/Typography';
import {
  createMortarStyles,
  spacing,
} from '@cat-home-experts/react-native-utilities';
import React, { ReactElement } from 'react';
import { View } from 'react-native';
import { applyAlpha } from 'src/utilities/styles/applyAlpha';

type IconBubbleProps = {
  testId?: string;
  text?: string | number;
  iconName?: IconsId;
  radius?: number;
  variant?: TypographyProps['useVariant'];
};

export const IconBubble = ({
  testId,
  text,
  iconName,
  radius = 6,
  variant = 'bodySemiBold',
}: IconBubbleProps): ReactElement => {
  const radiusStyle = { width: spacing(radius), height: spacing(radius) };
  return (
    <View testID={testId} style={[styles.iconWrapper, radiusStyle]}>
      <View style={styles.iconContainer}>
        {iconName && <Icon name={iconName} color={tokenColorPrimaryBlue} />}
        {text && <Typography useVariant={variant}>{text}</Typography>}
      </View>
    </View>
  );
};

const styles = createMortarStyles(() => ({
  iconWrapper: {
    backgroundColor: applyAlpha(tokenColorPrimaryBlue, 0.1),
    marginHorizontal: spacing(1.5),
    borderRadius: 70,
    alignSelf: 'center',
  },
  iconContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
