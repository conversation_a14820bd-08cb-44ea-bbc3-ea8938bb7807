import React from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { IconBubble } from './IconBubble';

describe('components | IconBubble', () => {
  afterEach(cleanup);

  it('renders on the screen', () => {
    const { getByTestId } = render(
      <IconBubble testId="icon-bubble" iconName="question" />,
    );
    expect(getByTestId('icon-bubble')).toBeTruthy();
  });
});
