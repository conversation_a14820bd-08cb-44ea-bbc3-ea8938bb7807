import React from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { DISABLED_SCREEN_TEXT, DisabledScreen } from './DisabledScreen';

describe('components | DisabledScreen', () => {
  afterEach(() => {
    jest.resetAllMocks();
    jest.clearAllMocks();
    cleanup();
  });

  it('renders Disabled Screen', () => {
    const { getByText } = render(<DisabledScreen />);

    expect(getByText(DISABLED_SCREEN_TEXT.TITLE)).toBeDefined();
  });
});
