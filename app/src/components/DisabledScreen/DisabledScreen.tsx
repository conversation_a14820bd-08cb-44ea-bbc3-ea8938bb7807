import React, { ReactElement } from 'react';
import { Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { ErrorScreen } from 'src/screens/ErrorScreen';

export const DISABLED_SCREEN_TEXT = {
  TITLE: 'This feature is no longer available.',
  DESCRIPTION:
    'Please update your app to the latest version to continue using this feature',
};

const TEST_IDS = createTestIds('disable-feature', {
  TITLE: 'title',
});

export const DisabledScreen = (): ReactElement => {
  return (
    <ErrorScreen
      title={DISABLED_SCREEN_TEXT.TITLE}
      testID={TEST_IDS.TITLE}
      style={styles.container}
    >
      <Typography use="bodyRegular" isCentred style={styles.text} isMuted>
        {DISABLED_SCREEN_TEXT.DESCRIPTION}
      </Typography>
    </ErrorScreen>
  );
};

DisabledScreen.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    paddingTop: spacing(10),
  },
  text: {
    color: palette.mortar.tokenColorBlueGrey,
    marginVertical: spacing(1),
    padding: spacing(1),
  },
}));
