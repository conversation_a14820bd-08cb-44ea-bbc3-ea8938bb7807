import { render, fireEvent, cleanup } from '@testing-library/react-native';
import React from 'react';
import { Image } from 'react-native';
import { ANALYTICS_ACTION_TYPE, COMPONENT_TYPE } from 'src/constants.events';
import { InformationModal } from './InformationModal';

const dashboardImageTestId = 'dashboard-image';
const mockOnPressPrimaryButton = jest.fn();
const mockOnPressMutedButton = jest.fn();
const mockLogEvents = jest.fn();

jest.mock('src/services/analytics', () => {
  return {
    logEvent: (...args: unknown[]) => mockLogEvents(...args),
  };
});

describe('components | InformationModal', () => {
  const eventName = 'test-me-please';
  const defaultProps = {
    onPressPrimaryButton: mockOnPressPrimaryButton,
    onPressMutedButton: mockOnPressMutedButton,
    iconColor: '#DC143C',
    primaryButtonLabel: 'Primary button',
    mutedButtonLabel: 'Muted button',
    eventName,
    modalTitle: 'Test title',
    modalDescription: 'Test description',
  };

  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('renders modal', () => {
    const { getByTestId, queryByTestId } = render(
      <InformationModal icon="nut" {...defaultProps} />,
    );

    const informationModal = getByTestId(InformationModal.testIds.ROOT);

    expect(informationModal).toBeDefined();
    expect(informationModal.props.visible).toBe(true);
    expect(queryByTestId(dashboardImageTestId)).toBeFalsy();
  });

  it('should render image on the modal when using renderImage property', () => {
    const { getByTestId } = render(
      <InformationModal
        icon="nut"
        {...defaultProps}
        renderImage={
          <Image
            source={{ uri: 'https://example.com/image.png' }}
            testID={dashboardImageTestId}
          />
        }
      />,
    );

    expect(getByTestId(dashboardImageTestId)).toBeDefined();
  });

  it('should render overrideTitle with success', () => {
    const { getByTestId } = render(
      <InformationModal
        icon="nut"
        {...defaultProps}
        renderImage={
          <Image
            source={{ uri: 'https://example.com/image.png' }}
            testID={dashboardImageTestId}
          />
        }
        overrideTitle="overrideTitle"
      />,
    );
    const informationModal = getByTestId(
      InformationModal.testIds.OVERRIDE_TITLE,
    );

    expect(informationModal).toBeDefined();
  });

  it('should not render modalTitle when included', () => {
    const { getByTestId } = render(
      <InformationModal
        icon="nut"
        {...defaultProps}
        renderImage={
          <Image
            source={{ uri: 'https://example.com/image.png' }}
            testID={dashboardImageTestId}
          />
        }
        modalTitle="modalTitle"
        overrideTitle="overrideTitle"
      />,
    );
    const informationModal = getByTestId(
      InformationModal.testIds.OVERRIDE_TITLE,
    );

    expect(informationModal).toBeDefined();
  });

  it('logs an event when modal is visible', () => {
    const { getByTestId } = render(
      <InformationModal icon="nut" {...defaultProps} />,
    );

    const informationModal = getByTestId(InformationModal.testIds.ROOT);

    expect(informationModal).toBeDefined();
    expect(mockLogEvents).toHaveBeenNthCalledWith(
      1,
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
      undefined,
    );
  });

  it('logs an event with analytics option', () => {
    const { getByTestId } = render(
      <InformationModal
        icon="nut"
        {...defaultProps}
        analyticsOptions={{ productId: '2' }}
      />,
    );

    const informationModal = getByTestId(InformationModal.testIds.ROOT);

    expect(informationModal).toBeDefined();
    expect(mockLogEvents).toHaveBeenNthCalledWith(
      1,
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
      { productId: '2' },
    );
  });

  it('can accept primary button pressed', () => {
    const { getByTestId } = render(
      <InformationModal icon="nut" {...defaultProps} />,
    );

    const primaryButton = getByTestId(InformationModal.testIds.PRIMARY_BUTTON);

    fireEvent.press(primaryButton);

    expect(mockOnPressPrimaryButton).toHaveBeenCalledTimes(1);
  });

  it('logs an event when primary button pressed', () => {
    const { getByTestId } = render(
      <InformationModal icon="nut" {...defaultProps} />,
    );

    const primaryButton = getByTestId(InformationModal.testIds.PRIMARY_BUTTON);

    fireEvent.press(primaryButton);

    expect(mockLogEvents).toHaveBeenNthCalledWith(
      2,
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.GOTO}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      undefined,
    );
  });
  it('show muted button if onPressMutedButton and mutedButtonLabel passed', () => {
    const { getByTestId } = render(
      <InformationModal icon="nut" {...defaultProps} />,
    );

    const mutedButton = getByTestId(InformationModal.testIds.MUTED_BUTTON);
    expect(mutedButton).toBeDefined();
  });

  it('can accept muted button pressed', () => {
    const { getByTestId } = render(
      <InformationModal icon="nut" {...defaultProps} />,
    );

    const mutedButton = getByTestId(InformationModal.testIds.MUTED_BUTTON);

    fireEvent.press(mutedButton);

    expect(mockOnPressMutedButton).toHaveBeenCalled();
  });

  it('logs an event when muted button pressed', () => {
    const { getByTestId } = render(
      <InformationModal icon="nut" {...defaultProps} />,
    );

    const mutedButton = getByTestId(InformationModal.testIds.MUTED_BUTTON);

    fireEvent.press(mutedButton);

    expect(mockLogEvents).toHaveBeenNthCalledWith(
      2,
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.CLOSE}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      undefined,
    );
  });
});
