import { EVENT_MESSAGE_TYPE } from 'src/constants.events';
import { IconsId } from '@cat-home-experts/iconography/dist/icons';
import { ReactElement } from 'react';
import { StyleProp, TextStyle } from 'react-native';

export interface InformationModalProps {
  onPressPrimaryButton: () => void;
  onPressMutedButton?: () => void;
  onPressCrossButton?: () => void;
  onClose?: () => void;
  showNewBadge?: boolean;
  showUpdateBadge?: boolean;
  primaryButtonLabel: string;
  mutedButtonLabel?: string;
  loading?: boolean;
  icon?: IconsId;
  iconColor?: string;
  eventName: string;
  modalTitle?: string;
  modalSubtitle?: string;
  modalDescription?: string | ReactElement;
  analyticsOptions?: EVENT_MESSAGE_TYPE;
  renderImage?: ReactElement;
  overrideTitle?: string;
  modalTitleStyle?: StyleProp<TextStyle>;
  isCloseButtonVisible?: boolean;
}
