import React, { ReactElement, useCallback, useEffect } from 'react';
import { Pressable, TouchableOpacity, View } from 'react-native';
import {
  Button,
  Modal,
  Typography,
} from '@cat-home-experts/react-native-components';
import { logEvent } from 'src/services/analytics';
import { getTestID, createTestIds } from 'src/utilities/testIds';
import { ANALYTICS_ACTION_TYPE, COMPONENT_TYPE } from 'src/constants.events';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { Cross } from '@cat-home-experts/mortar-iconography-native';
import { palette as staticPalette } from '@cat-home-experts/react-native-utilities/dist/styles/styles';
import { InformationModalProps } from './InformationModal.types';

const TEST_IDS = createTestIds('information-modal', {
  PRIMARY_BUTTON: 'primary-button',
  MUTED_BUTTON: 'muted-button',
  CROSS_BUTTON: 'cross-button',
  TITLE: 'title',
  SUBTITLE: 'subtitle',
  PARAGRAPH: 'paragraph',
  CLOSE_BUTTON: 'close-button',
});

export const InformationModalV2 = ({
  onPressPrimaryButton,
  onPressMutedButton,
  onPressCrossButton,
  onClose,
  icon,
  iconColor,
  primaryButtonLabel,
  mutedButtonLabel,
  eventName,
  modalTitle,
  modalSubtitle,
  modalDescription,
  analyticsOptions,
  showNewBadge,
  showUpdateBadge,
  renderImage,
  loading,
  modalTitleStyle,
  isCloseButtonVisible,
}: InformationModalProps): ReactElement => {
  useEffect(() => {
    logEvent(
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
      analyticsOptions,
    );
  }, [eventName, analyticsOptions]);

  const handleOnPressPrimaryButton = useCallback(() => {
    onPressPrimaryButton();

    logEvent(
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.GOTO}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      analyticsOptions,
    );
  }, [onPressPrimaryButton, eventName, analyticsOptions]);

  const handleOnPressMutedButton = useCallback(() => {
    onPressMutedButton?.();
    logEvent(
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.CLOSE}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      analyticsOptions,
    );
  }, [analyticsOptions, eventName, onPressMutedButton]);

  const handleOnPressCrossButton = useCallback(() => {
    onPressCrossButton?.();

    logEvent(
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.CLOSE}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      analyticsOptions,
    );
  }, [onPressCrossButton, eventName, analyticsOptions]);

  return (
    <Modal
      style={styles.noPadding}
      testID={getTestID(TEST_IDS.ROOT)}
      iconColor={iconColor}
      icon={icon}
      visible
      onClose={onClose}
    >
      <>
        {isCloseButtonVisible && (
          <Pressable
            onPress={onClose}
            style={styles.closeButton}
            testID={TEST_IDS.CLOSE_BUTTON}
          >
            <Cross color={staticPalette.mortar.tokenColorBlack} size={18} />
          </Pressable>
        )}
        <View style={[styles.noOverflow, styles.fullWidth, styles.largeGap]}>
          {showNewBadge && (
            <View style={styles.newBadge}>
              <Typography
                useVariant="bodySMSemiBold"
                style={styles.newBadgeText}
                allowFontScaling={false}
              >
                {'New'}
              </Typography>
            </View>
          )}
          {showUpdateBadge && (
            <View style={[styles.newBadge, styles.updateBadge]}>
              <Typography
                useVariant="bodySMSemiBold"
                style={styles.newBadgeText}
                allowFontScaling={false}
              >
                {'Update'}
              </Typography>
            </View>
          )}
          {renderImage ?? null}
          {onPressCrossButton && (
            <View style={styles.crossButton}>
              <TouchableOpacity
                testID={TEST_IDS.CROSS_BUTTON}
                onPress={handleOnPressCrossButton}
              >
                <Cross
                  color={staticPalette.mortar.tokenColorPrimaryWhite}
                  size={18}
                />
              </TouchableOpacity>
            </View>
          )}
          <View style={[styles.gap, styles.padding]}>
            {modalTitle && (
              <Typography
                testID={getTestID(TEST_IDS.TITLE)}
                use="header"
                style={[styles.modalMessage, modalTitleStyle]}
                allowFontScaling={false}
              >
                {modalTitle}
              </Typography>
            )}
            {modalSubtitle && (
              <Typography
                testID={getTestID(TEST_IDS.SUBTITLE)}
                use="bodyBold"
                style={styles.modalMessage}
                allowFontScaling={false}
              >
                {modalSubtitle}
              </Typography>
            )}
            {typeof modalDescription === 'string' ? (
              <Typography
                testID={getTestID(TEST_IDS.PARAGRAPH)}
                use="bodyRegular"
                style={styles.modalMessage}
                allowFontScaling={false}
              >
                {modalDescription}
              </Typography>
            ) : (
              modalDescription
            )}
            <View style={[styles.buttons, styles.gap]}>
              <Button
                isDisabled={loading}
                testID={getTestID(TEST_IDS.PRIMARY_BUTTON)}
                label={primaryButtonLabel}
                onPress={handleOnPressPrimaryButton}
                block
                variant="secondary"
              />
              {mutedButtonLabel && onPressMutedButton && (
                <Button
                  isDisabled={loading}
                  testID={getTestID(TEST_IDS.MUTED_BUTTON)}
                  label={mutedButtonLabel}
                  onPress={handleOnPressMutedButton}
                  block
                  variant="outlineMuted"
                />
              )}
            </View>
          </View>
        </View>
      </>
    </Modal>
  );
};

InformationModalV2.testIds = TEST_IDS;
InformationModalV2.screenName = 'info';

const styles = createMortarStyles(({ palette, spacing }) => ({
  modalMessage: {
    textAlign: 'center',
    color: palette.mortar.tokenColorBlack,
  },
  fullWidth: { width: '100%' },
  gap: { gap: spacing(1.5) },
  largeGap: { gap: spacing(2) },
  padding: { paddingHorizontal: spacing(3) },
  noPadding: { padding: 0, paddingTop: spacing(2) },
  noOverflow: { overflow: 'hidden' },
  borderRadius: { borderRadius: spacing(1) },
  buttons: {
    paddingVertical: spacing(1),
    paddingBottom: spacing(3),
  },
  crossButton: {
    position: 'absolute',
    height: spacing(7.5),
    right: spacing(3),
    justifyContent: 'center',
  },
  newBadge: {
    position: 'absolute',
    top: spacing(6),
    left: spacing(0),
    backgroundColor: palette.mortarV3.tokenPrimary700,
    paddingVertical: spacing(0.5),
    paddingHorizontal: spacing(1),
    borderTopRightRadius: spacing(1),
    borderBottomRightRadius: spacing(1),
    zIndex: 9,
  },
  updateBadge: {
    backgroundColor: palette.mortarV3.tokenDefault500,
  },
  newBadgeText: {
    color: palette.mortarV3.tokenNeutral0,
  },
  closeButton: {
    position: 'absolute',
    top: spacing(3),
    right: spacing(3),
    cursor: 'pointer',
    outlineWidth: 0,
  },
}));
