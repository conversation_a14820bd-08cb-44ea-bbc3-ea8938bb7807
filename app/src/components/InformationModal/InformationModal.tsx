import React, { ReactElement, useCallback, useEffect } from 'react';
import { Modal, Typography } from '@cat-home-experts/react-native-components';
import { logEvent } from 'src/services/analytics';
import { getTestID, createTestIds } from 'src/utilities/testIds';
import { useMobileSmallScreenMediaQuery } from 'src/hooks/useMediaQuery';
import { tokenColorBlack } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { ANALYTICS_ACTION_TYPE, COMPONENT_TYPE } from 'src/constants.events';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { InformationModalProps } from './InformationModal.types';

const TEST_IDS = createTestIds('information-modal', {
  PRIMARY_BUTTON: 'primary-button',
  MUTED_BUTTON: 'muted-button',
  OVERRIDE_TITLE: 'override-title',
});

export const InformationModal = ({
  onPressPrimaryButton,
  onPressMutedButton,
  icon,
  iconColor,
  primaryButtonLabel,
  mutedButtonLabel,
  eventName,
  modalTitle,
  modalDescription,
  analyticsOptions,
  renderImage,
  overrideTitle,
}: InformationModalProps): ReactElement => {
  const isMobileSmallScreen = useMobileSmallScreenMediaQuery();

  useEffect(() => {
    logEvent(
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.VIEWED}`,
      analyticsOptions,
    );
  }, [eventName, analyticsOptions]);

  const handleOnPressPrimaryButton = useCallback(() => {
    onPressPrimaryButton();

    logEvent(
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.GOTO}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      analyticsOptions,
    );
  }, [onPressPrimaryButton, eventName, analyticsOptions]);

  const handleOnPressMutedButton = useCallback(() => {
    onPressMutedButton?.();

    logEvent(
      `${eventName}_${COMPONENT_TYPE.MODAL}_${ANALYTICS_ACTION_TYPE.CLOSE}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      analyticsOptions,
    );
  }, [onPressMutedButton, eventName, analyticsOptions]);

  return (
    <Modal
      buttons={[
        {
          buttonLabel: primaryButtonLabel,
          buttonOnPress: handleOnPressPrimaryButton,
          variant: 'secondary',
          testId: getTestID(TEST_IDS.PRIMARY_BUTTON),
        },
        ...(onPressMutedButton && mutedButtonLabel
          ? ([
              {
                buttonLabel: mutedButtonLabel,
                buttonOnPress: handleOnPressMutedButton,
                variant: 'muted',
                buttonStyle: styles.buttonStyle,
                testId: getTestID(TEST_IDS.MUTED_BUTTON),
              },
            ] as const)
          : []),
      ]}
      testID={getTestID(TEST_IDS.ROOT)}
      icon={icon}
      iconColor={iconColor}
      visible
      title={overrideTitle ? undefined : modalTitle}
      style={styles.container}
    >
      <>
        {renderImage ?? null}
        {overrideTitle && (
          <Typography
            testID={getTestID(TEST_IDS.OVERRIDE_TITLE)}
            use="header"
            style={styles.modalMessage}
          >
            {overrideTitle}
          </Typography>
        )}
        <Typography
          use="bodyRegular"
          style={[
            styles.modalMessage,
            // eslint-disable-next-line react-native/no-inline-styles
            { marginHorizontal: isMobileSmallScreen ? 12 : 24 },
          ]}
        >
          {modalDescription}
        </Typography>
      </>
    </Modal>
  );
};

InformationModal.testIds = TEST_IDS;
InformationModal.screenName = 'info';

const styles = createMortarStyles(({ spacing }) => ({
  container: { paddingHorizontal: spacing(3) },
  buttonStyle: {
    marginTop: 16,
  },
  modalMessage: {
    textAlign: 'center',
    color: tokenColorBlack,
    marginTop: 16,
  },
}));
