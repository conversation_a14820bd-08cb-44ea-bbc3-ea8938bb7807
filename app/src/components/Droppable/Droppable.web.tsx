import React, { useState, type DragEvent } from 'react';
import { View, type ViewStyle } from 'react-native';
import { DownloadFill } from '@cat-home-experts/mortar-iconography-native';
import {
  alpha,
  createMortarStyles,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';
import { TEST_IDS } from './constants';
import { useDroppableActive } from './DroppableContext';
import { DropSupportingView } from './DropSupportingView';
import type { DroppableProps } from './types';

export const Droppable: React.FC<DroppableProps> = ({ onDrop }) => {
  // Context
  const droppableActive = useDroppableActive();

  // State
  const [isCurrentTarget, setIsCurrentTarget] = useState<boolean>(false);

  // Methods
  const handleDragEnter = (event: DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsCurrentTarget(true);
  };

  const handleDragLeave = () => setIsCurrentTarget(false);
  const handleDrop = async (event: DragEvent<HTMLDivElement>) => {
    event.preventDefault();

    if (
      event.dataTransfer?.items &&
      event.dataTransfer.items[0].kind === 'file'
    ) {
      const files = new Array<File>();

      // Annoyingly, DataTransferItemList is not iterable so, whilst it
      // has a length, we can't `for const` or `.filter`.
      for (let x = 0; x < event.dataTransfer.items.length; x += 1) {
        const item = event.dataTransfer.items[x];
        if (item.kind === 'file') {
          files.push(item.getAsFile()!);
        }
      }

      onDrop?.(files);
    }
  };

  return (
    <DropSupportingView
      style={[styles.root, styles.transition, droppableActive && styles.active]}
      onDropCapture={handleDrop}
      onDragOver={handleDragEnter}
      onDragLeave={handleDragLeave}
      draggable
      testID={TEST_IDS.ROOT}
    >
      <View
        testID={TEST_IDS.INNER}
        style={[
          styles.inner,
          styles.transition,
          isCurrentTarget && styles.currentTarget,
        ]}
      >
        <DownloadFill
          size={48}
          style={styles.transition}
          testID={TEST_IDS.ICON}
          color={
            isCurrentTarget
              ? staticPalette.mortar.tokenColorSystemLinkBlue
              : alpha(staticPalette.mortar.tokenColorBlueGrey, 0.5)
          }
        />
      </View>
    </DropSupportingView>
  );
};

Droppable.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    borderRadius: spacing(1),
    pointerEvents: 'none',
    backgroundColor: alpha(palette.mortar.tokenColorLightBlue, 0.75),
    backdropFilter: `blur(${spacing(0.5)}px)`,
    position: 'absolute',
    top: spacing(0.5),
    left: spacing(0.5),
    right: spacing(0.5),
    bottom: spacing(0.5),
    padding: spacing(0.5),
    opacity: 0,
  },
  transition: {
    transition: 'all 150ms ease-out',
  } as ViewStyle,
  inner: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: spacing(0.25),
    borderStyle: 'dashed',
    borderColor: alpha(palette.mortar.tokenColorBlueGrey, 0.5),
    borderRadius: spacing(1),
  },
  currentTarget: {
    borderColor: palette.mortar.tokenColorSystemLinkBlue,
  },
  active: {
    opacity: 1,
    pointerEvents: 'auto',
  },
}));
