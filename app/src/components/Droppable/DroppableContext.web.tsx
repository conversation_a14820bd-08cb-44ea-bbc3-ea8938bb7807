import React, { createContext, useContext, useEffect, useState } from 'react';

const DroppableContext = createContext<boolean>(false);

export const useDroppableActive = (): boolean => useContext(DroppableContext);

export const DroppableProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  // State
  const [droppableActive, setDroppableActive] = useState<boolean>(false);

  // Method
  const dragIn = () => setDroppableActive(true);
  const dragOut = () => setDroppableActive(false);

  // Effects
  useEffect(() => {
    document.addEventListener('dragenter', dragIn);
    document.addEventListener('dragover', dragIn);
    document.addEventListener('dragleave', dragOut);
    document.addEventListener('dragend', dragOut);
    document.addEventListener('drop', dragOut);

    return () => {
      document.removeEventListener('dragenter', dragIn);
      document.removeEventListener('dragover', dragIn);
      document.removeEventListener('dragleave', dragOut);
      document.removeEventListener('dragend', dragOut);
      document.removeEventListener('drop', dragOut);
    };
  }, []);

  return (
    <DroppableContext.Provider value={droppableActive}>
      {children}
    </DroppableContext.Provider>
  );
};
