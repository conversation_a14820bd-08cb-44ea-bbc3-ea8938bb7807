import React from 'react';
import { StyleSheet } from 'react-native';
import type { StyleProp, ViewStyle } from 'react-native';
import { IS_WEB } from 'src/constants';

interface DropSupportingViewProps
  extends Pick<
    React.HTMLProps<HTMLDivElement>,
    'onDropCapture' | 'onDragOver' | 'onDragLeave' | 'draggable'
  > {
  style?: StyleProp<ViewStyle>;
  testID?: string;
}

export function DropSupportingView({
  style,
  testID,
  ...props
}: React.PropsWithChildren<DropSupportingViewProps>): React.ReactElement {
  return React.createElement('div', {
    ...props,
    style: StyleSheet.flatten(style),
    [IS_WEB ? 'data-testid' : 'testID']: testID,
  });
}
