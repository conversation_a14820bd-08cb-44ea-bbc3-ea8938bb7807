import { Typography } from '@cat-home-experts/react-native-components';
import { act, cleanup, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { Carousel } from './Carousel';
import { CarouselDots } from './CarouselDots';

jest.mock('src/hooks/useMediaQuery', () => ({
  useDesktopMediaQuery: () => false,
}));

type Item = {
  id: number;
  title: string;
  description: string;
};

const mockData: Item[] = [
  { id: 1, title: 'Item 1', description: 'Description 1' },
  { id: 2, title: 'Item 2', description: 'Description 2' },
  { id: 3, title: 'Item 3', description: 'Description 3' },
];

describe('Components | Carousel', () => {
  const CAROUSEL_TEST_ID = 'carousel-test-id';
  const renderItem = (item: Item) => <Typography>{item.title}</Typography>;

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders items correctly', async () => {
    const { getByText, getByTestId } = render(
      <Carousel
        data={mockData}
        renderItem={renderItem}
        testID={CAROUSEL_TEST_ID}
      />,
    );

    // force on layout to be called
    const carousel = getByTestId(CAROUSEL_TEST_ID);
    act(() => {
      carousel.props.onLayout({
        nativeEvent: {
          layout: {
            width: 100,
            height: 100,
          },
        },
      });
    });

    await waitFor(() => expect(getByText('Item 1')).toBeOnTheScreen());
    expect(getByText('Item 2')).toBeOnTheScreen();
    expect(getByText('Item 3')).toBeOnTheScreen();
  });

  it('renders pagination dots correctly', async () => {
    const { getByTestId } = render(
      <Carousel
        data={mockData}
        renderItem={renderItem}
        testID={CAROUSEL_TEST_ID}
      />,
    );

    // force on layout to be called
    const carousel = getByTestId(CAROUSEL_TEST_ID);
    act(() => {
      carousel.props.onLayout({
        nativeEvent: {
          layout: {
            width: 100,
            height: 100,
          },
        },
      });
    });

    await waitFor(() =>
      expect(getByTestId(`${CarouselDots.testIds.EL}-0`)).toBeOnTheScreen(),
    );
    expect(getByTestId(`${CarouselDots.testIds.EL}-1`)).toBeOnTheScreen();
    expect(getByTestId(`${CarouselDots.testIds.EL}-2`)).toBeOnTheScreen();
  });
});
