import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { chunk } from 'lodash';
import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  ReactElement,
} from 'react';
import {
  FlatList,
  type DimensionValue,
  type LayoutChangeEvent,
  type ViewStyle,
  type ViewToken,
  type ViewabilityConfig,
  View,
} from 'react-native';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { CarouselDots } from './CarouselDots';
import { ObjectWithId } from './types';

type Props<T> = {
  data: T[];
  renderItem: (item: T) => ReactElement;
  testID?: string;
  containerStyle?: ViewStyle;
  keyExtractor?: (item: T) => string;
  padding?: number;
  cardHeight?: DimensionValue;
  maxWidth?: number;
};

const handleExtractKey = <T extends ObjectWithId>(item: T) => {
  if (item && 'id' in item) {
    return String(item.id);
  }

  return String(item);
};

const viewabilityConfig: ViewabilityConfig = {
  itemVisiblePercentThreshold: 50,
};

/**
 * TODO: This should be moved to the react-native-components library.
 */
export const Carousel = <T extends ObjectWithId>({
  testID,
  data,
  renderItem,
  containerStyle,
  keyExtractor = handleExtractKey,
  maxWidth = 400,
  padding = 0,
  cardHeight = 450,
}: Props<T>): React.ReactElement => {
  const isDesktop = useDesktopMediaQuery();

  // State/Refs
  const flatListRef = useRef<FlatList<T>>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const [activeCardIndex, setActiveCardIndex] = useState(0);

  // Computed
  const itemWidth = useMemo(() => {
    const containerSize = containerWidth - padding * 2;
    return isDesktop ? Math.min(maxWidth, containerSize) : containerSize;
  }, [containerWidth, isDesktop, maxWidth, padding]);

  const baseItemStyle: ViewStyle = useMemo(
    () => ({
      height: cardHeight,
      width: itemWidth,
    }),
    [cardHeight, itemWidth],
  );

  const chunkedCards = useMemo(() => chunk(data, 1), [data]);

  // Callbacks
  const handleLayout = useCallback((event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    if (width) {
      setContainerWidth(width);
    }
  }, []);

  const getItemLayout = useCallback(
    (_data: ArrayLike<T> | null | undefined, index: number) => ({
      length: itemWidth,
      offset: itemWidth * index,
      index,
    }),
    [itemWidth],
  );

  const onViewableItemsChanged = useCallback(
    ({ viewableItems }: { viewableItems: ViewToken[] }) => {
      if (viewableItems.length > 0) {
        const firstViewableIndex = viewableItems[0].index;
        if (firstViewableIndex !== null) {
          setActiveCardIndex(firstViewableIndex);
        }
      }
    },
    [],
  );

  const handleDotPress = useCallback((index: number) => {
    flatListRef.current?.scrollToIndex({
      index: index,
      animated: true,
      viewPosition: 0,
    });
  }, []);

  const renderFlatListItem = useCallback(
    ({ item }: { item: T }) => (
      <View style={baseItemStyle}>{renderItem(item)}</View>
    ),
    [baseItemStyle, renderItem],
  );

  // ref
  const viewabilityConfigCallbackPairs = useRef([
    { viewabilityConfig, onViewableItemsChanged },
  ]);

  return (
    <View
      testID={testID}
      style={[styles.container, containerStyle]}
      onLayout={handleLayout}
    >
      {containerWidth > 0 ? (
        <FlatList
          ref={flatListRef}
          data={data}
          renderItem={renderFlatListItem}
          keyExtractor={keyExtractor}
          initialScrollIndex={0}
          horizontal
          pagingEnabled={!isDesktop}
          decelerationRate="fast"
          showsHorizontalScrollIndicator={false}
          getItemLayout={getItemLayout}
          viewabilityConfigCallbackPairs={
            viewabilityConfigCallbackPairs.current
          }
          style={styles.flatList}
          contentContainerStyle={[
            styles.scrollViewContent,
            isDesktop && styles.scrollViewContentDesktop,
          ]}
        />
      ) : null}

      {!isDesktop && (
        <CarouselDots
          data={chunkedCards}
          activeIndex={activeCardIndex}
          keyExtractor={keyExtractor}
          onDotPress={handleDotPress}
        />
      )}
    </View>
  );
};

const styles = createMortarStyles(({ spacing }) => ({
  container: {
    width: '100%',
    paddingVertical: spacing(2),
    alignItems: 'center',
    overflow: 'hidden',
  },
  flatList: {
    width: '100%',
  },
  scrollViewContent: {
    alignItems: 'center',
  },
  scrollViewContentDesktop: {
    paddingHorizontal: spacing(6),
    gap: spacing(2),
  },
}));
