import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import React, { ReactElement } from 'react';
import { TouchableOpacity, View, ViewStyle } from 'react-native';
import { ObjectWithId } from './types';

type Props<T> = {
  data: T[][];
  activeIndex: number;
  keyExtractor: (item: T) => string;
  style?: ViewStyle;
  onDotPress: (index: number) => void;
};

const TEST_IDS = createTestIds('carousel-dots', {
  EL: 'element',
});

export const CarouselDots = <T extends ObjectWithId>({
  data,
  activeIndex,
  keyExtractor,
  style,
  onDotPress,
}: Props<T>): ReactElement | null => {
  if (data.length <= 1) {
    return null;
  }

  return (
    <View testID={TEST_IDS.ROOT} style={[styles.pagination, style]}>
      {data.map((subArray, index) => (
        <View key={keyExtractor(subArray[0])}>
          <TouchableOpacity
            testID={`${TEST_IDS.EL}-${index}`}
            onPress={() => onDotPress(index)}
          >
            <View
              role="button"
              key={keyExtractor(subArray[0])}
              style={[
                styles.paginationDot,
                index === activeIndex && styles.paginationDotActive,
              ]}
            />
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );
};

CarouselDots.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing(1),
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 8,
    backgroundColor: palette.mortarV3.tokenNeutral400,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: palette.mortarV3.tokenDefault700,
  },
}));
