import { cleanup, fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { CarouselDots } from './CarouselDots';
import { ObjectWithId } from './types';

const onDotPressMock = jest.fn();
const keyExtractor = <T extends ObjectWithId>(item: T) => String(item.id);

const mockData = [
  [
    { id: 1, title: 'Item 1', description: 'Description 1' },
    { id: 2, title: 'Item 2', description: 'Description 2' },
    { id: 3, title: 'Item 3', description: 'Description 3' },
  ],
  [
    { id: 4, title: 'Item 4', description: 'Description 4' },
    { id: 5, title: 'Item 5', description: 'Description 5' },
    { id: 6, title: 'Item 6', description: 'Description 6' },
  ],
];

describe('CarouselDots', () => {
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    const { getByTestId } = render(
      <CarouselDots
        data={mockData}
        activeIndex={0}
        keyExtractor={keyExtractor}
        onDotPress={onDotPressMock}
      />,
    );
    expect(getByTestId(CarouselDots.testIds.ROOT)).toBeOnTheScreen();
  });
});

it('calls onDotPress when a dot is pressed', () => {
  const { getByTestId } = render(
    <CarouselDots
      data={mockData}
      activeIndex={0}
      keyExtractor={keyExtractor}
      onDotPress={onDotPressMock}
    />,
  );
  const index = 0;
  fireEvent.press(getByTestId(`${CarouselDots.testIds.EL}-${index}`));
  expect(onDotPressMock).toHaveBeenCalledWith(index);
});
