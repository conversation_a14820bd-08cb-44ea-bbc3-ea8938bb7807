import React from 'react';
import {
  render,
  fireEvent,
  waitFor,
  cleanup,
} from '@testing-library/react-native';
import { palette as staticPalette } from '@cat-home-experts/react-native-utilities';
import { FeedbackBottomSheet } from './FeedbackBottomSheet';

const SCREEN_WIDTH = 375;

jest.mock('@gorhom/bottom-sheet', () => {
  const { TextInput } = jest.requireActual('react-native');
  return {
    ...jest.requireActual('@gorhom/bottom-sheet/mock'),
    BottomSheetTextInput: TextInput,
    SCREEN_WIDTH,
  };
});

describe('components | FeedbackBottomSheet', () => {
  const onDismissMock = jest.fn();
  const onSubmitMock = jest.fn();
  const setRatingMock = jest.fn();
  const setFeedbackMock = jest.fn();

  const defaultProps = {
    visible: true,
    onDismiss: onDismissMock,
    onSubmit: onSubmitMock,
    rating: undefined,
    setRating: setRatingMock,
    feedback: '',
    setFeedback: setFeedbackMock,
  };

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByTestId } = render(<FeedbackBottomSheet {...defaultProps} />);

    expect(getByTestId(FeedbackBottomSheet.testIds.ROOT)).toBeTruthy();
    expect(
      getByTestId(FeedbackBottomSheet.testIds.PAGINATION_BAR_1),
    ).toBeTruthy();
    expect(
      getByTestId(FeedbackBottomSheet.testIds.PAGINATION_BAR_2),
    ).toBeTruthy();
    expect(getByTestId(FeedbackBottomSheet.testIds.CLOSE_BUTTON)).toBeTruthy();
    expect(getByTestId(FeedbackBottomSheet.testIds.SCROLLVIEW)).toBeTruthy();
    expect(getByTestId(FeedbackBottomSheet.testIds.PAGE_1)).toBeTruthy();
    expect(getByTestId(FeedbackBottomSheet.testIds.PAGE_2)).toBeTruthy();
    expect(getByTestId(FeedbackBottomSheet.testIds.TEXT_INPUT)).toBeTruthy();
    expect(
      getByTestId(FeedbackBottomSheet.testIds.CHARACTER_COUNT),
    ).toBeTruthy();
    expect(getByTestId(FeedbackBottomSheet.testIds.SUBMIT_BUTTON)).toBeTruthy();
  });

  it('calls onDismiss when close button is pressed', () => {
    const { getByTestId } = render(<FeedbackBottomSheet {...defaultProps} />);

    fireEvent.press(getByTestId(FeedbackBottomSheet.testIds.CLOSE_BUTTON));

    expect(onDismissMock).toHaveBeenCalled();
  });

  it('calls onSubmit when submit button is pressed', () => {
    const { getByTestId } = render(<FeedbackBottomSheet {...defaultProps} />);

    fireEvent.press(getByTestId(FeedbackBottomSheet.testIds.SUBMIT_BUTTON));

    expect(onSubmitMock).toHaveBeenCalled();
  });

  it('updates feedback when text input changes', () => {
    const { getByTestId } = render(<FeedbackBottomSheet {...defaultProps} />);

    fireEvent.changeText(
      getByTestId(FeedbackBottomSheet.testIds.TEXT_INPUT),
      'New feedback',
    );

    expect(setFeedbackMock).toHaveBeenCalledWith('New feedback');
  });

  it('scrolls to next page when rating is set', async () => {
    const { getByTestId } = render(<FeedbackBottomSheet {...defaultProps} />);

    const emoticonRating = getByTestId(
      FeedbackBottomSheet.testIds.EMOTICON_RATING,
    );
    const paginationBar2 = getByTestId(
      FeedbackBottomSheet.testIds.PAGINATION_BAR_2,
    );

    expect(paginationBar2).toHaveStyle({
      backgroundColor: staticPalette.mortar.tokenColorLightGrey,
    });

    fireEvent(emoticonRating, 'onChange', 2);

    await waitFor(() => {
      expect(setRatingMock).toHaveBeenCalledWith(2);
      expect(paginationBar2).toHaveStyle({
        backgroundColor: staticPalette.mortar.tokenColorBlueGrey,
      });
    });
  });

  it('updates character count when feedback text changes', () => {
    const Wrapper = () => {
      const [feedback, setFeedback] = React.useState('');

      return (
        <FeedbackBottomSheet
          {...defaultProps}
          feedback={feedback}
          setFeedback={jest.fn(setFeedback)}
        />
      );
    };

    const { getByTestId, rerender } = render(<Wrapper />);

    const feedbackInput = getByTestId(FeedbackBottomSheet.testIds.TEXT_INPUT);
    const characterCount = getByTestId(
      FeedbackBottomSheet.testIds.CHARACTER_COUNT,
    );

    expect(characterCount).toHaveTextContent('0/500');

    fireEvent.changeText(feedbackInput, 'New feedback');

    rerender(<Wrapper />);

    expect(characterCount).toHaveTextContent('12/500');
  });
});
