import React, { ReactElement, useCallback, useRef, useState } from 'react';
import { Platform, View } from 'react-native';
import {
  TextInput,
  TouchableOpacity,
  ScrollView,
} from 'react-native-gesture-handler';
import {
  BottomSheetTextInput,
  SCREEN_WIDTH,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import {
  createMortarStyles,
  createTestIds,
  palette as staticPalette,
  spacing as staticSpacing,
} from '@cat-home-experts/react-native-utilities';
import { Button, Typography } from '@cat-home-experts/react-native-components';
import { Cross } from '@cat-home-experts/mortar-iconography-native';
import { SheetOrModal } from 'src/components/SheetOrModal';
import {
  EmoticonRating,
  Rating,
} from 'src/components/EmoticonRating/EmoticonRating';
import { IS_WEB } from 'src/constants';
import {
  FEEDBACK_PLACEHOLDER,
  FEEDBACK_QUESTION_DETAILS,
  FEEDBACK_QUESTION_RATING,
  FEEDBACK_SUBMIT,
} from './constants';

type Props = {
  visible: boolean;
  onDismiss: () => void;
  onSubmit: () => void;
  rating: Rating | undefined;
  setRating: (rating: Rating | undefined) => void;
  feedback: string;
  setFeedback: (feedback: string) => void;
};

const MAX_LENGTH = 500;
const WEB_WIDTH = 344; // same as default width for modal on web, but needed for page width
const PAGE_WIDTH = IS_WEB ? WEB_WIDTH : SCREEN_WIDTH;

const TEST_IDS = createTestIds('feedback-bottom-sheet', {
  PAGINATION_BAR_1: 'pagination-bar-1',
  PAGINATION_BAR_2: 'pagination-bar-2',
  CLOSE_BUTTON: 'close-button',
  PAGE_1: 'page-1',
  EMOTICON_RATING: 'emoticon-rating',
  PAGE_2: 'page-2',
  SCROLLVIEW: 'scrollview',
  TEXT_INPUT: 'text-input',
  CHARACTER_COUNT: 'character-count',
  SUBMIT_BUTTON: 'submit-button',
});

export const FeedbackBottomSheet = ({
  onDismiss,
  onSubmit,
  visible,
  rating,
  setRating,
  feedback,
  setFeedback,
}: Props): ReactElement => {
  const [scrollIndex, setScrollIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const textInputRef = useRef<TextInput>(null);

  const handleSetRating = useCallback(
    (newRating: Rating | undefined) => {
      scrollViewRef.current?.scrollTo({ x: SCREEN_WIDTH, animated: true });
      setScrollIndex(1);
      setRating(newRating);
      textInputRef.current?.focus();
    },
    [scrollViewRef, setScrollIndex, setRating],
  );

  const Container = IS_WEB ? ScrollView : BottomSheetScrollView;
  const Input = IS_WEB ? TextInput : BottomSheetTextInput;

  return (
    <SheetOrModal
      visible={visible}
      onDismiss={onDismiss}
      sheetProps={{
        keyboardBehavior: Platform.OS === 'android' ? 'extend' : 'interactive',
      }}
      style={styles.sheetOrModal}
    >
      <View style={styles.container} testID={TEST_IDS.ROOT}>
        <View style={styles.paginationCloseWrapper}>
          <View style={styles.paginationWrapper}>
            <View
              style={[styles.paginationBar, styles.paginationBarActive]}
              testID={TEST_IDS.PAGINATION_BAR_1}
            />
            <View
              style={[
                styles.paginationBar,
                scrollIndex > 0 && styles.paginationBarActive,
              ]}
              testID={TEST_IDS.PAGINATION_BAR_2}
            />
          </View>
          <TouchableOpacity
            onPress={onDismiss}
            style={styles.close}
            testID={TEST_IDS.CLOSE_BUTTON}
            hitSlop={staticSpacing(1.5)}
          >
            <Cross size={8} color={staticPalette.mortar.tokenColorDarkGrey} />
          </TouchableOpacity>
        </View>
        <Container
          contentContainerStyle={styles.scrollView}
          ref={scrollViewRef}
          pagingEnabled
          horizontal
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          disableScrollViewPanResponder
          scrollEnabled={false}
          keyboardDismissMode="none"
          keyboardShouldPersistTaps="handled"
          testID={TEST_IDS.SCROLLVIEW}
        >
          <View style={styles.page} testID={TEST_IDS.PAGE_1}>
            <View style={styles.titleWrapper}>
              <Typography use="title">{FEEDBACK_QUESTION_RATING}</Typography>
            </View>
            <EmoticonRating
              onChange={handleSetRating}
              value={rating}
              testId={TEST_IDS.EMOTICON_RATING}
            />
          </View>
          <View style={styles.page} testID={TEST_IDS.PAGE_2}>
            <View style={styles.secondRating}>
              <EmoticonRating onChange={handleSetRating} value={rating} />
            </View>
            <View style={styles.titleWrapper}>
              <Typography use="title">{FEEDBACK_QUESTION_DETAILS}</Typography>
            </View>
            <Input
              // @ts-expect-error TextInput from Gesture Handler is typed incorrectly
              ref={textInputRef}
              style={styles.textInput}
              placeholder={FEEDBACK_PLACEHOLDER}
              placeholderTextColor={staticPalette.mortar.tokenColorBlueGrey}
              multiline
              keyboardType="default"
              maxLength={MAX_LENGTH}
              defaultValue={feedback}
              onChangeText={setFeedback}
              testID={TEST_IDS.TEXT_INPUT}
            />
            <Typography
              useVariant="caption"
              style={styles.characterCount}
              testID={TEST_IDS.CHARACTER_COUNT}
            >
              {`${feedback.length}/${MAX_LENGTH}`}
            </Typography>
            <Button
              label={FEEDBACK_SUBMIT}
              onPress={onSubmit}
              variant="primary"
              block
              testID={TEST_IDS.SUBMIT_BUTTON}
            />
          </View>
        </Container>
      </View>
    </SheetOrModal>
  );
};

FeedbackBottomSheet.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette, typographyV2 }) => ({
  sheetOrModal: {
    width: PAGE_WIDTH,
    paddingHorizontal: 0,
  },
  container: {
    flex: 1,
    width: '100%',
  },
  paginationCloseWrapper: {
    paddingHorizontal: spacing(3),
    gap: spacing(2),
    flex: 1,
    flexDirection: 'row',
    marginBottom: IS_WEB ? spacing(3) : spacing(1),
  },
  paginationWrapper: {
    height: spacing(4),
    width: '100%',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing(1),
  },
  paginationBar: {
    flex: 1,
    height: spacing(0.25),
    backgroundColor: palette.mortar.tokenColorLightGrey,
    borderRadius: spacing(0.5),
  },
  paginationBarActive: {
    backgroundColor: palette.mortar.tokenColorBlueGrey,
  },
  close: {
    height: spacing(4),
    width: spacing(4),
    borderWidth: 1,
    borderColor: palette.mortar.tokenColorLightGrey,
    borderRadius: spacing(3),
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {},
  page: {
    width: PAGE_WIDTH,
    paddingHorizontal: spacing(3),
    paddingTop: spacing(1),
    paddingBottom: spacing(2),
  },
  titleWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing(2),
    marginBottom: spacing(3),
  },
  secondRating: {
    marginBottom: spacing(3),
  },
  textInput: {
    height: spacing(16),
    // https://github.com/facebook/react-native/issues/33562#issuecomment-1889933728
    paddingHorizontal: spacing(2),
    paddingTop: spacing(2),
    paddingBottom: spacing(2),
    marginBottom: spacing(0.5),
    fontFamily: typographyV2.tokenFontFamily,
    color: palette.mortar.tokenColorBlueGrey,
    fontSize: 16,
    lineHeight: 24,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderRadius: spacing(0.5),
    borderColor: palette.mortar.tokenColorLightGrey,
  },
  characterCount: {
    marginBottom: spacing(3),
    textAlign: 'right',
    color: palette.mortar.tokenColorBlueGrey,
  },
}));
