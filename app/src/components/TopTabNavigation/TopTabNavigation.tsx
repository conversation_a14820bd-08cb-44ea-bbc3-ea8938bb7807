import React, { ReactElement } from 'react';

import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';

const TopTabs = createMaterialTopTabNavigator();
const TopTabsScreen = TopTabs.Screen;

type Props = React.ComponentProps<typeof TopTabs.Navigator> & {
  children: React.ReactElement<typeof TopTabsScreen>[];
};

function TopTabNavigation({ children, ...rest }: Props): ReactElement {
  return <TopTabs.Navigator {...rest}>{children}</TopTabs.Navigator>;
}

export { TopTabNavigation, TopTabsScreen };
