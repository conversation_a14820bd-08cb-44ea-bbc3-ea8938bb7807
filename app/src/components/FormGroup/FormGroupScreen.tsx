import React from 'react';
import { ScrollView, View } from 'react-native';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
  spacing as staticSpacing,
} from '@cat-home-experts/react-native-utilities';
import { Button, ButtonProps } from '@cat-home-experts/react-native-components';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface FormGroupScreenProps {
  buttonProps?: ButtonProps;
  children: React.ReactNode;
  testID?: string;
}

const TEST_IDS = createTestIds('form-group-screen', {});

export const FormGroupScreen: React.NativeFC<
  FormGroupScreenProps,
  typeof TEST_IDS
> = ({ buttonProps, children, testID = TEST_IDS.ROOT }) => {
  const isDesktop = useDesktopMediaQuery();
  const { bottom } = useSafeAreaInsets();

  return (
    <>
      <ScrollView
        contentContainerStyle={[styles.root, isDesktop && styles.desktopRoot]}
        testID={testID}
      >
        <View style={styles.content}>{children}</View>
      </ScrollView>
      {isTruthy(buttonProps) && (
        <View
          style={[
            styles.buttonContainer,
            { paddingBottom: bottom || staticSpacing(1) },
          ]}
        >
          <View style={styles.content}>
            <Button variant="secondary" block {...buttonProps} />
          </View>
        </View>
      )}
    </>
  );
};

FormGroupScreen.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  root: {
    backgroundColor: palette.mortarV3.tokenDefault100,
  },
  desktopRoot: {
    paddingTop: spacing(2),
    paddingBottom: spacing(2),
  },
  buttonContainer: {
    width: '100%',
    backgroundColor: palette.mortarV3.tokenNeutral0,
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(1),
    borderTopWidth: 2,
    borderTopColor: palette.mortarV3.tokenNeutral200,
  },
  content: {
    width: '100%',
    maxWidth: 800,
    alignSelf: 'center',
    gap: spacing(2),
  },
}));
