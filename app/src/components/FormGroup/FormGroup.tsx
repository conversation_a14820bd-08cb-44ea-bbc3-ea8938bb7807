import React from 'react';
import { View } from 'react-native';
import { NativeMortarIcon } from '@cat-home-experts/mortar-types';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';
import { Typography } from '@cat-home-experts/react-native-components';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';

interface FormGroupProps {
  header?: {
    icon: NativeMortarIcon;
    title: string;
  };
  children: React.ReactNode;
  testID?: string;
}

const TEST_IDS = createTestIds('form-group', {});

export const FormGroup: React.NativeFC<FormGroupProps, typeof TEST_IDS> = ({
  header,
  children,
  testID = TEST_IDS.ROOT,
}) => {
  const isDesktop = useDesktopMediaQuery();

  return (
    <View testID={testID}>
      {isTruthy(header) && (
        <View style={[styles.header, isDesktop && styles.headerDesktop]}>
          <header.icon
            color={staticPalette.mortarV3.tokenNeutral600}
            size={20}
          />
          <Typography useVariant="bodySMSemiBold">{header.title}</Typography>
        </View>
      )}
      <View style={[styles.card, isDesktop && styles.cardDesktop]}>
        {children}
      </View>
    </View>
  );
};

FormGroup.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing(0.5),
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(1),
    borderBottomWidth: 1,
    borderBottomColor: palette.mortarV3.tokenNeutral200,
  },
  headerDesktop: {
    borderBottomWidth: 0,
  },
  card: {
    padding: spacing(3),
    backgroundColor: palette.mortarV3.tokenNeutral0,
    borderBottomWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
    gap: spacing(2),
  },
  cardDesktop: {
    borderWidth: 1,
    borderRadius: spacing(1),
  },
}));
