import { StyleProp, ViewStyle } from 'react-native';
import React, { ReactElement } from 'react';
import {
  Icon,
  NewTypographyVariants,
  Typography,
} from '@cat-home-experts/react-native-components';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { IconsId } from '@cat-home-experts/iconography';
import {
  createMortarStyles,
  palette,
  spacing,
} from '@cat-home-experts/react-native-utilities';

type LinkButtonProps = {
  onPress?: () => void;
  label: string;
  textVariant?: NewTypographyVariants;
  trailingIcon?: IconsId;
  color?: string;
  margin?: number;
  style?: StyleProp<ViewStyle>;
  /** `inlineBlock`: Overrides container styles. */
  inlineBlock?: boolean;
};

/**
 * TODO: This should be moved to the react-native-components library.
 */
export const LinkButton = ({
  onPress,
  label,
  textVariant = 'bodySemiBold',
  trailingIcon,
  color = palette.mortar.tokenColorSystemLinkBlue,
  margin = spacing(1),
  style,
  inlineBlock = false,
}: LinkButtonProps): ReactElement => {
  const typographyStyles = {
    color,
  };

  return (
    <TouchableOpacity
      style={[!inlineBlock && styles.container, { margin }, style]}
      onPress={onPress}
    >
      <Typography style={typographyStyles} useVariant={textVariant}>
        {label}
      </Typography>
      {trailingIcon && (
        <Icon
          style={styles.iconStyles}
          color={typographyStyles.color}
          size={16}
          name={trailingIcon}
        />
      )}
    </TouchableOpacity>
  );
};

const styles = createMortarStyles(() => ({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing(1),
    width: '100%',
  },
  iconStyles: {
    position: 'relative',
    top: 2, // Minor cosmetic correction.
  },
}));
