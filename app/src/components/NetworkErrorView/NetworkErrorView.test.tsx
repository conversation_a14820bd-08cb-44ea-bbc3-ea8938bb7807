import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { isAxiosOfflineError } from 'src/utilities/errors/isError';
import { NetworkErrorView } from './NetworkErrorView';
import { OfflineErrorView } from './components/OfflineErrorView';
import { UnknownErrorView } from './components/UnknownErrorView';

jest.mock('src/utilities/errors/isError', () => ({
  isAxiosOfflineError: jest.fn(),
}));

describe('NetworkErrorView', () => {
  const mockOfflineError = new Error('Network Error');
  const mockGenericError = new Error('Something went wrong');

  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  it('renders UnknownErrorView when no error', () => {
    (isAxiosOfflineError as jest.Mock).mockReturnValue(false);
    const { getByTestId } = render(
      <NetworkErrorView
        dataName="TestData"
        dataRefresh={jest.fn()}
        error={null}
      />,
    );
    expect(getByTestId(UnknownErrorView.testIds!.ROOT)).toBeTruthy();
  });

  it('renders OfflineErrorView when offline', () => {
    (isAxiosOfflineError as jest.Mock).mockReturnValue(true);
    const { getByTestId } = render(
      <NetworkErrorView
        dataName="TestData"
        dataRefresh={jest.fn()}
        error={mockOfflineError}
      />,
    );
    expect(getByTestId(OfflineErrorView.testIds!.ROOT)).toBeTruthy();
  });

  it('renders UnknownErrorView when there is a non-offline error', () => {
    (isAxiosOfflineError as jest.Mock).mockReturnValue(false);
    const { getByTestId } = render(
      <NetworkErrorView
        dataName="TestData"
        dataRefresh={jest.fn()}
        error={mockGenericError}
      />,
    );
    expect(getByTestId(UnknownErrorView.testIds!.ROOT)).toBeTruthy();
  });

  it('calls dataRefresh when refresh is triggered from OfflineErrorView', () => {
    (isAxiosOfflineError as jest.Mock).mockReturnValue(true);
    const dataRefresh = jest.fn();
    const { getByText } = render(
      <NetworkErrorView
        dataName="TestData"
        dataRefresh={dataRefresh}
        error={mockOfflineError}
      />,
    );
    fireEvent.press(getByText(/refresh/i));
    expect(dataRefresh).toHaveBeenCalled();
  });

  it('calls dataRefresh when refresh is triggered from UnknownErrorView', () => {
    (isAxiosOfflineError as jest.Mock).mockReturnValue(false);
    const dataRefresh = jest.fn();
    const { getByText } = render(
      <NetworkErrorView
        dataName="TestData"
        dataRefresh={dataRefresh}
        error={mockGenericError}
      />,
    );
    fireEvent.press(getByText(/refresh/i));
    expect(dataRefresh).toHaveBeenCalled();
  });

  it('does not throw when refresh is pressed and dataRefresh is not provided (OfflineErrorView)', () => {
    (isAxiosOfflineError as jest.Mock).mockReturnValue(true);
    const { getByText } = render(
      <NetworkErrorView
        dataName="TestData"
        dataRefresh={() => undefined} // Simulate undefined prop
        error={mockOfflineError}
      />,
    );
    expect(() => {
      fireEvent.press(getByText(/refresh/i));
    }).not.toThrow();
  });

  it('does not throw when refresh is pressed and dataRefresh is not provided (UnknownErrorView)', () => {
    (isAxiosOfflineError as jest.Mock).mockReturnValue(false);
    const { getByText } = render(
      <NetworkErrorView
        dataName="TestData"
        dataRefresh={() => undefined} // Simulate undefined prop
        error={mockGenericError}
      />,
    );
    expect(() => {
      fireEvent.press(getByText(/refresh/i));
    }).not.toThrow();
  });
});
