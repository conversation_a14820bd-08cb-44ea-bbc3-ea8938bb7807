import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';

import { createTestIds } from '@cat-home-experts/react-native-utilities';
import ServerErrorImage from '../images/server-error.svg';
import { DATA_ERROR_COPY } from '../../constants';
import { InformationContentView } from '../InformationContentView';

type Props = {
  onRefresh: () => void;
  dataName?: string;
  headingText?: string;
  bodyText?: string;
  buttonText?: string;
  style?: StyleProp<ViewStyle>;
  testId?: string;
};

const TEST_IDS = createTestIds('unknown-error-view', {});

export const UnknownErrorView: React.FC<Props> = ({
  onRefresh,
  headingText = DATA_ERROR_COPY.DATA_ERROR_TITLE('data'),
  bodyText = DATA_ERROR_COPY.REASON,
  buttonText = DATA_ERROR_COPY.REFRESH,
  style,
  testId,
}: Props) => {
  return (
    <InformationContentView
      icon={<ServerErrorImage />}
      headingText={headingText}
      bodyText={bodyText}
      buttonText={buttonText}
      onRefresh={onRefresh}
      style={style}
      testId={testId || TEST_IDS.ROOT}
    />
  );
};

UnknownErrorView.testIds = TEST_IDS;
