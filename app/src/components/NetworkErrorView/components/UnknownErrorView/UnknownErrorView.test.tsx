import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { UnknownErrorView } from './UnknownErrorView';
import { DATA_ERROR_COPY } from '../../constants';

describe('UnknownErrorView', () => {
  afterEach(() => {
    cleanup();
  });

  it('renders with default props', () => {
    const onRefresh = jest.fn();
    const { getByText } = render(<UnknownErrorView onRefresh={onRefresh} />);
    expect(getByText(DATA_ERROR_COPY.DATA_ERROR_TITLE('data'))).toBeTruthy();
    expect(getByText(DATA_ERROR_COPY.REASON)).toBeTruthy();
    expect(getByText(DATA_ERROR_COPY.REFRESH)).toBeTruthy();
  });

  it('renders with custom texts', () => {
    const onRefresh = jest.fn();
    const heading = 'Custom Error Heading';
    const body = 'Custom error body';
    const button = 'Try Again';
    const { getByText } = render(
      <UnknownErrorView
        onRefresh={onRefresh}
        headingText={heading}
        bodyText={body}
        buttonText={button}
      />,
    );
    expect(getByText(heading)).toBeTruthy();
    expect(getByText(body)).toBeTruthy();
    expect(getByText(button)).toBeTruthy();
  });

  it('calls onRefresh when button is pressed', () => {
    const onRefresh = jest.fn();
    const { getByText } = render(<UnknownErrorView onRefresh={onRefresh} />);
    fireEvent.press(getByText(DATA_ERROR_COPY.REFRESH));
    expect(onRefresh).toHaveBeenCalled();
  });
});
