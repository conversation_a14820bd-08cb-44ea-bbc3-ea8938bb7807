import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { InformationContentView } from './InformationContentView';

describe('InformationContentView', () => {
  afterEach(cleanup);

  it('renders with default props', () => {
    const heading = 'Test Heading';
    const body = 'Test Body';
    const button = 'Test Button';
    const onRefresh = jest.fn();
    const { getByText } = render(
      <InformationContentView
        headingText={heading}
        bodyText={body}
        buttonText={button}
        onRefresh={onRefresh}
        icon={<></>}
      />,
    );
    expect(getByText(heading)).toBeOnTheScreen();
    expect(getByText(body)).toBeOnTheScreen();
    expect(getByText(button)).toBeOnTheScreen();
  });

  it('renders with custom texts', () => {
    const onRefresh = jest.fn();
    const heading = 'Custom Heading';
    const body = 'Custom Body';
    const button = 'Custom Button';
    const { getByText } = render(
      <InformationContentView
        onRefresh={onRefresh}
        headingText={heading}
        bodyText={body}
        buttonText={button}
        icon={<></>}
      />,
    );
    expect(getByText(heading)).toBeTruthy();
    expect(getByText(body)).toBeTruthy();
    expect(getByText(button)).toBeTruthy();
  });

  it('calls onRefresh when button is pressed', () => {
    const onRefresh = jest.fn();
    const buttonText = 'Refresh';
    const { getByText } = render(
      <InformationContentView
        headingText=""
        bodyText=""
        buttonText={buttonText}
        onRefresh={onRefresh}
        icon={<></>}
      />,
    );
    fireEvent.press(getByText(buttonText));
    expect(onRefresh).toHaveBeenCalled();
  });
});
