import React from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';

import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { Button, Typography } from '@cat-home-experts/react-native-components';
import NoNetworkIcon from '../images/no-connection-wifi.svg';

type Props = {
  onRefresh: () => void;
  headingText: string;
  bodyText: string;
  buttonText: string;
  icon: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  testId?: string;
};

const TEST_IDS = createTestIds('information-content-view', {});

export const InformationContentView: React.FC<Props> = ({
  onRefresh,
  headingText,
  bodyText,
  buttonText,
  icon = <NoNetworkIcon width={64} height={64} />,
  style,
  testId,
}) => {
  return (
    <View style={[styles.container, style]} testID={testId || TEST_IDS.ROOT}>
      <View style={styles.content}>
        {icon}
        <Typography useVariant="headingSMSemiBold" style={styles.textHeading}>
          {headingText}
        </Typography>
        <Typography useVariant="bodyRegular" style={styles.textBody}>
          {bodyText}
        </Typography>
        <View style={styles.buttonsContainer}>
          <Button
            label={buttonText}
            onPress={onRefresh}
            size="small"
            block
            variant="secondary"
          />
        </View>
      </View>
    </View>
  );
};

InformationContentView.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    paddingHorizontal: spacing(2),
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    maxWidth: spacing(75),
    alignSelf: 'center',
    paddingHorizontal: spacing(2),
  },
  textHeading: {
    paddingTop: spacing(2),
  },
  textBody: {
    paddingTop: spacing(2),
    paddingBottom: spacing(4),
    textAlign: 'center',
  },
  buttonsContainer: {
    width: '100%',
  },
}));
