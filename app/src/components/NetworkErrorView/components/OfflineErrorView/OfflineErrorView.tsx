import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';

import { createTestIds } from '@cat-home-experts/react-native-utilities';
import NoNetworkIcon from '../images/no-connection-wifi.svg';
import { OFFLINE_ERROR_COPY } from '../../constants';
import { InformationContentView } from '../InformationContentView';

type Props = {
  onRefresh: () => void;
  testId?: string;
  headingText?: string;
  bodyText?: string;
  buttonText?: string;
  style?: StyleProp<ViewStyle>;
};

const TEST_IDS = createTestIds('offline-error-view', {});

export const OfflineErrorView: React.FC<Props> = ({
  onRefresh,
  headingText = OFFLINE_ERROR_COPY.HEADING,
  bodyText = OFFLINE_ERROR_COPY.BODY,
  buttonText = OFFLINE_ERROR_COPY.PRIMARY_BUTTON,
  style,
  testId,
}) => {
  return (
    <InformationContentView
      icon={<NoNetworkIcon />}
      headingText={headingText}
      bodyText={bodyText}
      buttonText={buttonText}
      onRefresh={onRefresh}
      style={style}
      testId={testId || TEST_IDS.ROOT}
    />
  );
};

OfflineErrorView.testIds = TEST_IDS;
