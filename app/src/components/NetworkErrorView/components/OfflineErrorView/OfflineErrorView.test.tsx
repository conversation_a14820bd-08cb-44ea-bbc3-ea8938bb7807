import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { OfflineErrorView } from './OfflineErrorView';
import { OFFLINE_ERROR_COPY } from '../../constants';

describe('OfflineErrorView', () => {
  afterEach(() => {
    cleanup();
  });

  it('renders with default props', () => {
    const onRefresh = jest.fn();
    const { getByText } = render(<OfflineErrorView onRefresh={onRefresh} />);
    expect(getByText(OFFLINE_ERROR_COPY.HEADING)).toBeOnTheScreen();
    expect(getByText(OFFLINE_ERROR_COPY.BODY)).toBeOnTheScreen();
    expect(getByText(OFFLINE_ERROR_COPY.PRIMARY_BUTTON)).toBeOnTheScreen();
  });

  it('renders with custom texts', () => {
    const onRefresh = jest.fn();
    const heading = 'Custom Heading';
    const body = 'Custom Body';
    const button = 'Custom Button';
    const { getByText } = render(
      <OfflineErrorView
        onRefresh={onRefresh}
        headingText={heading}
        bodyText={body}
        buttonText={button}
      />,
    );
    expect(getByText(heading)).toBeOnTheScreen();
    expect(getByText(body)).toBeOnTheScreen();
    expect(getByText(button)).toBeOnTheScreen();
  });

  it('calls onRefresh when button is pressed', () => {
    const onRefresh = jest.fn();
    const { getByText } = render(<OfflineErrorView onRefresh={onRefresh} />);
    fireEvent.press(getByText(OFFLINE_ERROR_COPY.PRIMARY_BUTTON));
    expect(onRefresh).toHaveBeenCalled();
  });

  it('passes testId to root view', () => {
    const onRefresh = jest.fn();
    const { getByTestId } = render(
      <OfflineErrorView onRefresh={onRefresh} testId="custom-test-id" />,
    );
    expect(getByTestId('custom-test-id')).toBeTruthy();
  });
});
