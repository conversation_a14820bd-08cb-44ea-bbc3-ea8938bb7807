import React, { useMemo } from 'react';

import { StyleProp, StyleSheet, ViewStyle } from 'react-native';
import { isAxiosOfflineError } from 'src/utilities/errors/isError';
import { useNetwork } from 'src/hooks/useNetwork';
import { UnknownErrorView } from './components/UnknownErrorView';
import { OfflineErrorView } from './components/OfflineErrorView';
import { DATA_ERROR_COPY } from './constants';

type Props = {
  /**
   * Name of the data
   */
  dataName: string;
  /**
   * Function to call when the data needs to be refreshed
   */
  dataRefresh: () => void;
  /**
   * Test ID for the component
   */
  testId?: string;
  /**
   * Whether to expand the view to fill the available space or render inline
   */
  expanded?: boolean;
  /**
   * If provided, triggers the unknown error view
   */
  error: Error | null;
  /**
   * Custom heading text for the unknown error view
   */
  customHeadingText?: string;
  /**
   * Custom body text for the unknown error view
   */
  customBodyText?: string;
  /**
   * Custom button text for the unknown error view
   */
  customButtonText?: string;
  /**
   * Custom style for the unknown error view
   */
  style?: StyleProp<ViewStyle>;
  /**
   * Test ID for the component
   */
  testID?: string;
};

export const NetworkErrorView: React.FC<Props> = ({
  dataName,
  expanded = true,
  dataRefresh,
  error,
  customHeadingText,
  customBodyText,
  customButtonText,
  style,
  testId,
}: Props) => {
  const { isOnline } = useNetwork();
  const isOfflineError = useMemo(() => isAxiosOfflineError(error), [error]);

  // Network outage fallback
  if (isOnline === false || isOfflineError) {
    return (
      <OfflineErrorView
        testId={testId}
        onRefresh={dataRefresh}
        style={[expanded && styles.expand, style]}
      />
    );
  }

  return (
    <UnknownErrorView
      testId={testId}
      headingText={
        customHeadingText ?? DATA_ERROR_COPY.DATA_ERROR_TITLE(dataName)
      }
      bodyText={customBodyText ?? DATA_ERROR_COPY.REASON}
      buttonText={customButtonText ?? DATA_ERROR_COPY.REFRESH}
      onRefresh={dataRefresh}
      style={[expanded && styles.expand, style]}
    />
  );
};

const styles = StyleSheet.create({
  expand: {
    flex: 1,
  },
});
