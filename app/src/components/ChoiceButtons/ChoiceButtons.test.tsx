import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { ChoiceButtons, BUTTON_LABELS } from './ChoiceButtons';

describe('Components | ChoiceButtons', () => {
  const mockOnPressYes = jest.fn();
  const mockOnPressNo = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  it('renders correctly with both buttons', () => {
    const { getByText } = render(
      <ChoiceButtons
        onPressYesButton={mockOnPressYes}
        onPressNoButton={mockOnPressNo}
      />,
    );

    expect(getByText(BUTTON_LABELS.YES)).toBeTruthy();
    expect(getByText(BUTTON_LABELS.NO)).toBeTruthy();
  });

  it('calls onPressYesButton when the yes button is pressed', () => {
    const { getByTestId } = render(
      <ChoiceButtons
        onPressYesButton={mockOnPressYes}
        onPressNoButton={mockOnPressNo}
      />,
    );

    fireEvent.press(getByTestId(ChoiceButtons.testIds.YES_BUTTON));
    expect(mockOnPressYes).toHaveBeenCalled();
  });

  it('calls onPressNoButton when the no button is pressed', () => {
    const { getByTestId } = render(
      <ChoiceButtons
        onPressYesButton={mockOnPressYes}
        onPressNoButton={mockOnPressNo}
      />,
    );

    fireEvent.press(getByTestId(ChoiceButtons.testIds.NO_BUTTON));
    expect(mockOnPressNo).toHaveBeenCalled();
  });
});
