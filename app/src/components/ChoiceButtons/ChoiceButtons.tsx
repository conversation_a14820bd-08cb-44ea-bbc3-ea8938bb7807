import { Button } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import React, { ReactElement } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';

export const BUTTON_LABELS = {
  YES: 'Yes',
  NO: 'No',
};

const TEST_IDS = createTestIds('choice-buttons', {
  YES_BUTTON: 'yes-button',
  NO_BUTTON: 'no-button',
});

type ChoiceButtonsProps = {
  value?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  onPressYesButton: () => void;
  onPressNoButton: () => void;
};

export const ChoiceButtons = ({
  value,
  containerStyle,
  onPressYesButton,
  onPressNoButton,
}: ChoiceButtonsProps): ReactElement => {
  return (
    <View style={[styles.buttonsContainer, containerStyle]}>
      <View style={styles.acceptButton}>
        <Button
          testID={TEST_IDS.YES_BUTTON}
          onPress={onPressYesButton}
          label={BUTTON_LABELS.YES}
          variant={value === true ? 'outline' : 'muted'}
          block
          style={styles.button}
        />
      </View>
      <View style={styles.declineButton}>
        <Button
          testID={TEST_IDS.NO_BUTTON}
          onPress={onPressNoButton}
          label={BUTTON_LABELS.NO}
          variant={value === false ? 'outline' : 'muted'}
          block
          style={styles.button}
        />
      </View>
    </View>
  );
};

ChoiceButtons.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing }) => ({
  buttonsContainer: {
    flexDirection: 'row',
    marginBottom: spacing(2),
  },
  button: { height: spacing(5.75) },
  acceptButton: {
    flex: 1,
    paddingRight: spacing(0.5),
  },
  declineButton: { flex: 1, paddingLeft: spacing(0.5) },
}));
