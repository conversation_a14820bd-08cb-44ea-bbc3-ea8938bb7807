import React from 'react';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { ScrollView } from 'react-native';
import { Button } from '@cat-home-experts/react-native-components';
import {
  REQUEST_MORE_INFO_MESSAGE,
  REQUEST_PHOTOS_MESSAGE,
  TEST_IDS,
} from './constants';

interface QuickActionsChatInputProps {
  onRequestMoreInfo: (message: string) => void;
  onRequestPhotos: (message: string) => void;
  onRequestAddress: () => void;
  onBookAppointment: () => void;
  onCreateQuote: () => void;
  onCreatePaymentRequest?: () => void;
}

export const QuickActionsChatInput = ({
  onRequestMoreInfo,
  onRequestPhotos,
  onRequestAddress,
  onBookAppointment,
  onCreateQuote,
  onCreatePaymentRequest,
}: QuickActionsChatInputProps): React.ReactElement => {
  return (
    <ScrollView
      horizontal
      contentContainerStyle={styles.inputModeContainer}
      showsHorizontalScrollIndicator={false}
    >
      <Button
        label="Book appointment"
        onPress={onBookAppointment}
        variant="tertiary"
        iconStart="calendar-blank"
        style={styles.inputModeButton}
        testID={TEST_IDS.BOOK_APPOINTMENT}
        size="small"
      />
      <Button
        label="Create quote"
        onPress={onCreateQuote}
        variant="tertiary"
        iconStart="receipt"
        style={styles.inputModeButton}
        testID={TEST_IDS.CREATE_QUOTE}
        size="small"
      />
      <Button
        label="Request more information"
        onPress={() => onRequestMoreInfo(REQUEST_MORE_INFO_MESSAGE)}
        variant="tertiary"
        iconStart="chat-left-text"
        style={styles.inputModeButton}
        testID={TEST_IDS.REQUEST_MORE_INFO}
        size="small"
      />
      <Button
        label="Request photos"
        onPress={() => onRequestPhotos(REQUEST_PHOTOS_MESSAGE)}
        variant="tertiary"
        iconStart="chat-left-text"
        style={styles.inputModeButton}
        testID={TEST_IDS.REQUEST_PHOTOS}
        size="small"
      />
      <Button
        label="Request address"
        onPress={onRequestAddress}
        variant="tertiary"
        iconStart="house"
        style={styles.inputModeButton}
        testID={TEST_IDS.ADDRESS_REQUESTED}
        size="small"
      />
      {onCreatePaymentRequest && (
        <Button
          label="Payment request"
          onPress={onCreatePaymentRequest}
          variant="tertiary"
          iconStart="credit-card"
          size="small"
          style={styles.inputModeButton}
          testID={TEST_IDS.CREATE_PAYMENT_REQUEST}
        />
      )}
    </ScrollView>
  );
};

const styles = createMortarStyles(({ spacing }) => ({
  // Input Mode
  inputModeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing(1),
    gap: spacing(1),
  },

  inputModeButton: {
    borderRadius: spacing(4),
    alignItems: 'center',
  },
}));
