import React from 'react';
import { cleanup, fireEvent, render } from '@testing-library/react-native';
import { QuickActionsMessage } from './QuickActionsMessage';
import {
  REQUEST_MORE_INFO_MESSAGE,
  REQUEST_PHOTOS_MESSAGE,
  TEST_IDS,
} from './constants';

describe('components | QuickActions | QuickActionsMessage', () => {
  const mockOnRequestMoreInfo = jest.fn();
  const mockOnRequestPhotos = jest.fn();
  const mockOnRequestAddress = jest.fn();
  const mockOnBookAppointment = jest.fn();
  const mockOnCreateQuote = jest.fn();
  const mockOnCreatePaymentRequest = jest.fn();

  const defaultProps = {
    onRequestMoreInfo: mockOnRequestMoreInfo,
    onRequestPhotos: mockOnRequestPhotos,
    onRequestAddress: mockOnRequestAddress,
    onBookAppointment: mockOnBookAppointment,
    onCreateQuote: mockOnCreateQuote,
  };

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders all default quick action buttons and text elements', () => {
    const { getByTestId, getByText, queryByTestId } = render(
      <QuickActionsMessage {...defaultProps} />,
    );

    expect(getByTestId(TEST_IDS.REQUEST_MORE_INFO)).toBeDefined();
    expect(getByTestId(TEST_IDS.REQUEST_PHOTOS)).toBeDefined();
    expect(getByTestId(TEST_IDS.ADDRESS_REQUESTED)).toBeDefined();
    expect(getByTestId(TEST_IDS.BOOK_APPOINTMENT)).toBeDefined();
    expect(getByTestId(TEST_IDS.CREATE_QUOTE)).toBeDefined();

    expect(
      getByText('Quick responses to get the conversation started'),
    ).toBeDefined();
    expect(getByText('Quick actions')).toBeDefined();

    expect(queryByTestId(TEST_IDS.CREATE_PAYMENT_REQUEST)).toBeNull();
  });

  it('can render conditional payment request quick action button', () => {
    const propsWithPaymentRequest = {
      ...defaultProps,
      onCreatePaymentRequest: mockOnCreatePaymentRequest,
    };

    const { getByTestId } = render(
      <QuickActionsMessage {...propsWithPaymentRequest} />,
    );

    expect(getByTestId(TEST_IDS.CREATE_PAYMENT_REQUEST)).toBeDefined();
  });

  it('calls onRequestMoreInfo with correct message when request more info button is pressed', () => {
    const { getByTestId } = render(<QuickActionsMessage {...defaultProps} />);

    fireEvent.press(getByTestId(TEST_IDS.REQUEST_MORE_INFO));

    expect(mockOnRequestMoreInfo).toHaveBeenCalledWith(
      REQUEST_MORE_INFO_MESSAGE,
    );
  });

  it('calls onRequestPhotos with correct message when request photos button is pressed', () => {
    const { getByTestId } = render(<QuickActionsMessage {...defaultProps} />);

    fireEvent.press(getByTestId(TEST_IDS.REQUEST_PHOTOS));

    expect(mockOnRequestPhotos).toHaveBeenCalledWith(REQUEST_PHOTOS_MESSAGE);
  });

  it('calls onRequestAddress when request address button is pressed', () => {
    const { getByTestId } = render(<QuickActionsMessage {...defaultProps} />);

    fireEvent.press(getByTestId(TEST_IDS.ADDRESS_REQUESTED));

    expect(mockOnRequestAddress).toHaveBeenCalledTimes(1);
  });

  it('calls onBookAppointment when book appointment button is pressed', () => {
    const { getByTestId } = render(<QuickActionsMessage {...defaultProps} />);

    fireEvent.press(getByTestId(TEST_IDS.BOOK_APPOINTMENT));

    expect(mockOnBookAppointment).toHaveBeenCalledTimes(1);
  });

  it('calls onCreateQuote when create quote button is pressed', () => {
    const { getByTestId } = render(<QuickActionsMessage {...defaultProps} />);

    fireEvent.press(getByTestId(TEST_IDS.CREATE_QUOTE));

    expect(mockOnCreateQuote).toHaveBeenCalledTimes(1);
  });

  it('calls onCreatePaymentRequest when payment request button is pressed', () => {
    const propsWithPaymentRequest = {
      ...defaultProps,
      onCreatePaymentRequest: mockOnCreatePaymentRequest,
    };

    const { getByTestId } = render(
      <QuickActionsMessage {...propsWithPaymentRequest} />,
    );

    fireEvent.press(getByTestId(TEST_IDS.CREATE_PAYMENT_REQUEST));

    expect(mockOnCreatePaymentRequest).toHaveBeenCalledTimes(1);
  });
});
