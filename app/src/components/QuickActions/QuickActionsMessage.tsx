import React from 'react';
import { StyleSheet, View } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { Button, Typography } from '@cat-home-experts/react-native-components';
import {
  REQUEST_MORE_INFO_MESSAGE,
  REQUEST_PHOTOS_MESSAGE,
  TEST_IDS,
} from './constants';

interface QuickActionsMessageProps {
  onRequestMoreInfo: (message: string) => void;
  onRequestPhotos: (message: string) => void;
  onRequestAddress: () => void;
  onBookAppointment: () => void;
  onCreateQuote: () => void;
  onCreatePaymentRequest?: () => void;
}

export const QuickActionsMessage = ({
  onRequestMoreInfo,
  onRequestPhotos,
  onRequestAddress,
  onBookAppointment,
  onCreateQuote,
  onCreatePaymentRequest,
}: QuickActionsMessageProps): React.ReactElement => {
  return (
    <View style={styles.messageModeContainer}>
      <View style={styles.header}>
        <View style={styles.headerStripe} />
      </View>
      <Typography use="bodySmall" style={styles.subtitle}>
        {'Quick responses to get the conversation started'}
      </Typography>
      <View style={styles.buttonGroup}>
        <Button
          label="Request more information"
          onPress={() => onRequestMoreInfo(REQUEST_MORE_INFO_MESSAGE)}
          variant="tertiary"
          iconStart="chat-left-text"
          size="small"
          block
          style={styles.button}
          testID={TEST_IDS.REQUEST_MORE_INFO}
        />
        <Button
          label="Request photos"
          onPress={() => onRequestPhotos(REQUEST_PHOTOS_MESSAGE)}
          variant="tertiary"
          iconStart="chat-left-text"
          size="small"
          block
          style={styles.button}
          testID={TEST_IDS.REQUEST_PHOTOS}
        />
      </View>
      <Typography use="bodySmall" style={styles.subtitle}>
        {'Quick actions'}
      </Typography>
      <View style={styles.buttonGroup}>
        <Button
          label="Request address"
          onPress={onRequestAddress}
          variant="tertiary"
          iconStart="house"
          style={styles.button}
          block
          size="small"
          testID={TEST_IDS.ADDRESS_REQUESTED}
        />
        <Button
          label="Book appointment"
          onPress={onBookAppointment}
          variant="tertiary"
          iconStart="calendar-blank"
          style={styles.button}
          block
          size="small"
          testID={TEST_IDS.BOOK_APPOINTMENT}
        />
        <Button
          label="Create quote"
          onPress={onCreateQuote}
          variant="tertiary"
          iconStart="receipt"
          size="small"
          block
          style={styles.button}
          testID={TEST_IDS.CREATE_QUOTE}
        />
        {onCreatePaymentRequest && (
          <Button
            label="Payment request"
            onPress={onCreatePaymentRequest}
            variant="tertiary"
            iconStart="credit-card"
            size="small"
            block
            style={styles.button}
            testID={TEST_IDS.CREATE_PAYMENT_REQUEST}
          />
        )}
      </View>
    </View>
  );
};

const styles = createMortarStyles(({ spacing, palette }) => ({
  // Message Mode
  messageModeContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing(1),
    paddingHorizontal: spacing(3),
    paddingBottom: spacing(1),
  },

  caption: {
    color: palette.mortarV3.tokenNeutral600,
  },

  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing(1),
  },
  headerStripe: {
    flex: 1,
    height: StyleSheet.hairlineWidth,
    backgroundColor: palette.mortar.tokenColorLightGrey,
  },

  subtitle: {
    marginBottom: spacing(1),
    textAlign: 'center',
  },
  buttonGroup: {
    gap: spacing(1),
    marginBottom: spacing(3),
    width: '100%',
    paddingHorizontal: spacing(7),
  },
  button: {
    borderRadius: spacing(4),
  },
}));
