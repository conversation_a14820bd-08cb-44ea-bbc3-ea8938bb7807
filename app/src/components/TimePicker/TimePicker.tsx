import React, { ReactElement, useMemo, useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { format, set } from 'date-fns';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { Typography } from '@cat-home-experts/react-native-components';
import { TimePickerProps } from './types';

const TEST_IDS = createTestIds('TimePicker', {
  LABEL: 'label',
  BUTTON: 'button',
  ADD_LABEL: 'add-label',
  TIME_DISPLAY: 'time-display',
  DATE_PICKER: 'date-picker',
});

export const TimePicker = ({
  label,
  value,
  onChange,
  minimumDate,
  minuteInterval = 5,
  title = 'Select time',
  initialDate = set(new Date(), { seconds: 0, milliseconds: 0 }),
}: TimePickerProps): ReactElement => {
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleConfirm = (date: Date) => {
    setOpen(false);
    onChange(date);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const formattedTime = useMemo(() => {
    if (!value) {
      return '';
    }

    return format(value, 'HH:mm');
  }, [value]);

  return (
    <>
      <View style={styles.root} testID={TEST_IDS.ROOT}>
        <Typography testID={TEST_IDS.LABEL}>{label}</Typography>
        <TouchableOpacity
          testID={TEST_IDS.BUTTON}
          style={styles.button}
          onPress={handleOpen}
        >
          {isTruthy(value) ? (
            <View style={styles.timeDisplay} testID={TEST_IDS.TIME_DISPLAY}>
              <Typography useVariant="bodySmall">{formattedTime}</Typography>
            </View>
          ) : (
            <Typography
              useVariant="textLinkRegular"
              style={styles.addButton}
              testID={TEST_IDS.ADD_LABEL}
            >
              {'Add'}
            </Typography>
          )}
        </TouchableOpacity>
      </View>
      <DatePicker
        modal
        open={open}
        date={value || minimumDate || initialDate}
        mode="time"
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        minuteInterval={minuteInterval}
        minimumDate={minimumDate}
        title={title}
        testID={TEST_IDS.DATE_PICKER}
      />
    </>
  );
};

TimePicker.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => {
  const ITEM_HEIGHT = spacing(4);
  return {
    root: {
      paddingVertical: spacing(1),
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    button: {
      height: ITEM_HEIGHT,
      alignItems: 'center',
      justifyContent: 'center',
    },
    timeDisplay: {
      height: ITEM_HEIGHT,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: palette.mortarV3.tokenNeutral100,
      borderRadius: spacing(1.25),
      paddingHorizontal: spacing(1.5),
    },
    addButton: {
      color: palette.mortarV3.tokenDefault500,
    },
  };
});
