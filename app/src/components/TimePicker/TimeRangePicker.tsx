import React, { ReactElement, useMemo } from 'react';
import { View } from 'react-native';
import { addMinutes, isAfter } from 'date-fns';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { TimePicker } from './TimePicker';

type TimeRangePickerProps = {
  startValue?: Date;
  onStartChange: (date?: Date) => void;
  endValue?: Date;
  onEndChange: (date?: Date) => void;
  minimumRangeMins?: number;
  initialDate?: Date;
  testId?: string;
};

const TEST_IDS = createTestIds('TimeRangePicker', {});

export const TimeRangePicker = ({
  startValue,
  onStartChange,
  endValue,
  onEndChange,
  minimumRangeMins = 15,
  initialDate,
  testId = TEST_IDS.ROOT,
}: TimeRangePickerProps): ReactElement => {
  const endMinimumDate = useMemo(() => {
    if (isTruthy(startValue) && !isTruthy(endValue)) {
      return addMinutes(startValue, minimumRangeMins);
    }

    return undefined;
  }, [startValue, endValue, minimumRangeMins]);

  const handleStartTimeChange = (date: Date) => {
    onStartChange(date);

    if (isTruthy(endValue) && isAfter(date, endValue)) {
      onEndChange(undefined);
    }
  };

  return (
    <View style={styles.root} testID={testId}>
      <TimePicker
        label="Start time"
        value={startValue}
        onChange={handleStartTimeChange}
        initialDate={initialDate}
      />
      <TimePicker
        label="End time"
        value={endValue}
        onChange={onEndChange}
        minimumDate={endMinimumDate}
        initialDate={endMinimumDate ?? initialDate}
      />
    </View>
  );
};

TimeRangePicker.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing }) => ({
  root: {
    flexDirection: 'column',
    gap: spacing(0.5),
  },
}));
