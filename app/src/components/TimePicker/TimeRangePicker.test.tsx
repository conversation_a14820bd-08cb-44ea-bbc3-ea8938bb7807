import React from 'react';
import { TouchableOpacity } from 'react-native';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { DatePickerProps } from 'react-native-date-picker';
import { TimePicker } from './TimePicker';
import { TimeRangePicker } from './TimeRangePicker';

const MOCK_NEW_DATE = new Date('2024-03-27T16:00:00');

const MockDatePicker = (props: DatePickerProps) => {
  const onPress = () => props.onConfirm?.(MOCK_NEW_DATE);
  if (props.open) {
    return <TouchableOpacity testID={props.testID} onPress={onPress} />;
  }

  return null;
};

describe('Components | TimePicker | TimeRangePicker', () => {
  beforeEach(() => {
    jest.mock('react-native-date-picker', () => (props: DatePickerProps) => (
      <MockDatePicker {...props} />
    ));
  });

  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('should render the default state', () => {
    const { getByTestId, queryAllByTestId } = render(
      <TimeRangePicker onEndChange={jest.fn} onStartChange={jest.fn} />,
    );

    const timeRangePicker = getByTestId(TimeRangePicker.testIds.ROOT);
    const label = queryAllByTestId(TimePicker.testIds.LABEL);
    const addLabel = queryAllByTestId(TimePicker.testIds.ADD_LABEL);
    const timeDisplay = queryAllByTestId(TimePicker.testIds.TIME_DISPLAY);
    const datePicker = queryAllByTestId(TimePicker.testIds.DATE_PICKER);

    expect(timeRangePicker).toBeDefined();
    expect(label).toHaveLength(2);
    expect(label[0]).toHaveTextContent('Start time');
    expect(label[1]).toHaveTextContent('End time');
    expect(addLabel).toHaveLength(2);
    expect(addLabel[0]).toHaveTextContent('Add');
    expect(addLabel[1]).toHaveTextContent('Add');
    expect(timeDisplay).toHaveLength(0);
    expect(datePicker).toHaveLength(0);
  });

  it('should call onStartChange with new date when start changed', () => {
    const onStartChange = jest.fn();
    const onEndChange = jest.fn();
    const { queryAllByTestId, getByTestId } = render(
      <TimeRangePicker
        onStartChange={onStartChange}
        onEndChange={onEndChange}
      />,
    );

    const button = queryAllByTestId(TimePicker.testIds.BUTTON);

    fireEvent.press(button[0]);

    const datePicker = getByTestId(TimePicker.testIds.DATE_PICKER);
    fireEvent.press(datePicker);

    expect(onStartChange).toHaveBeenCalledWith(MOCK_NEW_DATE);
    expect(onEndChange).not.toHaveBeenCalled();
  });

  it('should call onEndChange with new date when end changed', () => {
    const onStartChange = jest.fn();
    const onEndChange = jest.fn();
    const { queryAllByTestId, getByTestId } = render(
      <TimeRangePicker
        onStartChange={onStartChange}
        onEndChange={onEndChange}
      />,
    );

    const button = queryAllByTestId(TimePicker.testIds.BUTTON);

    fireEvent.press(button[1]);

    const datePicker = getByTestId(TimePicker.testIds.DATE_PICKER);
    fireEvent.press(datePicker);

    expect(onStartChange).not.toHaveBeenCalled();
    expect(onEndChange).toHaveBeenCalledWith(MOCK_NEW_DATE);
  });

  it('should call onEndChanged with undefined if start date is after end date', () => {
    const onStartChange = jest.fn();
    const onEndChange = jest.fn();
    const { queryAllByTestId, getByTestId } = render(
      <TimeRangePicker
        onStartChange={onStartChange}
        onEndChange={onEndChange}
        endValue={new Date('2024-03-27T12:00:00')}
      />,
    );

    const button = queryAllByTestId(TimePicker.testIds.BUTTON);

    fireEvent.press(button[0]);

    const datePicker = getByTestId(TimePicker.testIds.DATE_PICKER);
    fireEvent.press(datePicker);

    expect(onStartChange).toHaveBeenCalledWith(MOCK_NEW_DATE);
    expect(onEndChange).toHaveBeenCalledWith(undefined);
  });
});
