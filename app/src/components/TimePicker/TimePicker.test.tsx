import React from 'react';
import { TouchableOpacity } from 'react-native';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { DatePickerProps } from 'react-native-date-picker';
import { TimePicker } from './TimePicker';

const MOCK_NEW_DATE = new Date('2024-03-27T16:00:00');

const MockDatePicker = (props: DatePickerProps) => {
  const onPress = () => props.onConfirm?.(MOCK_NEW_DATE);
  if (props.open) {
    return <TouchableOpacity testID={props.testID} onPress={onPress} />;
  }

  return null;
};

describe('Components | TimePicker', () => {
  beforeEach(() => {
    jest.mock('react-native-date-picker', () => (props: DatePickerProps) => (
      <MockDatePicker {...props} />
    ));
  });

  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('should render the label and add label by when no date is undefined', () => {
    const { getByTestId, queryByTestId } = render(
      <TimePicker label="Pick a time" onChange={jest.fn} />,
    );
    const label = getByTestId(TimePicker.testIds.LABEL);
    const addLabel = getByTestId(TimePicker.testIds.ADD_LABEL);
    const timeDisplay = queryByTestId(TimePicker.testIds.TIME_DISPLAY);
    const datePicker = queryByTestId(TimePicker.testIds.DATE_PICKER);

    expect(label).toHaveTextContent('Pick a time');
    expect(addLabel).toHaveTextContent('Add');
    expect(timeDisplay).toBeNull();
    expect(datePicker).toBeNull();
  });

  it('should render the label and time by when date is defined', () => {
    const date = new Date('2024-01-01T12:00:00');
    const { getByTestId, queryByTestId } = render(
      <TimePicker label="Pick a time" onChange={jest.fn} value={date} />,
    );
    const label = getByTestId(TimePicker.testIds.LABEL);
    const timeDisplay = getByTestId(TimePicker.testIds.TIME_DISPLAY);
    const addLabel = queryByTestId(TimePicker.testIds.ADD_LABEL);

    expect(label).toHaveTextContent('Pick a time');
    expect(timeDisplay).toHaveTextContent('12:00');
    expect(addLabel).toBeNull();
  });

  it('should open the date picker when add button is clicked', () => {
    const { getByTestId } = render(
      <TimePicker label="Pick a time" onChange={jest.fn} />,
    );

    const button = getByTestId(TimePicker.testIds.BUTTON);

    fireEvent.press(button);

    const datePicker = getByTestId(TimePicker.testIds.DATE_PICKER);

    expect(datePicker).toBeDefined();
  });

  it('should call onChange with new date', () => {
    const onChange = jest.fn();
    const { getByTestId } = render(
      <TimePicker label="Pick a time" onChange={onChange} />,
    );

    const button = getByTestId(TimePicker.testIds.BUTTON);

    fireEvent.press(button);

    const datePicker = getByTestId(TimePicker.testIds.DATE_PICKER);
    fireEvent.press(datePicker);

    expect(onChange).toHaveBeenCalledWith(MOCK_NEW_DATE);
  });
});
