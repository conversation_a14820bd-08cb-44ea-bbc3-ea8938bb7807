import React, { ReactElement, useCallback, useMemo } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { format, set } from 'date-fns';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { Typography } from '@cat-home-experts/react-native-components';
import { TimePickerProps } from './types';

const TEST_IDS = createTestIds('TimePicker', {
  LABEL: 'label',
  BUTTON: 'button',
  ADD_LABEL: 'add-label',
  TIME_DISPLAY: 'time-display',
  DATE_PICKER: 'date-picker',
});

export const TimePicker = ({
  label,
  value,
  onChange,
  minimumDate,
  initialDate = set(new Date(), { seconds: 0, milliseconds: 0 }),
}: TimePickerProps): ReactElement => {
  const handleOpen = () => {
    onChange(initialDate);
  };

  const handleOnChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const { value: time } = event.target;
      const [hours, minutes] = time.split(':');
      const newDate = set(initialDate, {
        hours: Number(hours),
        minutes: Number(minutes),
      });

      onChange(newDate);
    },
    [initialDate, onChange],
  );

  const formattedTime = useMemo(() => {
    if (!value) {
      return '';
    }

    return format(value, 'HH:mm');
  }, [value]);

  const formattedMinimumDate = useMemo(() => {
    if (!minimumDate) {
      return '';
    }

    return format(minimumDate, 'HH:mm');
  }, [minimumDate]);

  return (
    <>
      <View style={styles.root} testID={TEST_IDS.ROOT}>
        <Typography testID={TEST_IDS.LABEL}>{label}</Typography>

        {isTruthy(value) ? (
          <View style={styles.timeDisplay} testID={TEST_IDS.TIME_DISPLAY}>
            <input
              type="time"
              value={formattedTime}
              onChange={handleOnChange}
              min={formattedMinimumDate}
              style={styles.timeInput}
            />
          </View>
        ) : (
          <TouchableOpacity
            testID={TEST_IDS.BUTTON}
            style={styles.button}
            onPress={handleOpen}
          >
            <Typography
              useVariant="textLinkRegular"
              style={styles.addButton}
              testID={TEST_IDS.ADD_LABEL}
            >
              {'Add'}
            </Typography>
          </TouchableOpacity>
        )}
      </View>
    </>
  );
};

TimePicker.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing, typographyV2 }) => {
  const ITEM_HEIGHT = spacing(4);
  return {
    root: {
      paddingVertical: spacing(1),
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    button: {
      height: ITEM_HEIGHT,
      alignItems: 'center',
      justifyContent: 'center',
    },
    timeDisplay: {
      height: ITEM_HEIGHT,
    },
    timeInput: {
      fontFamily: typographyV2.tokenFontFamily,
      fontWeight: '400',
      fontSize: spacing(2),
      height: '100%',
      paddingHorizontal: spacing(1.5),
      paddingVertical: spacing(1.5),
      color: palette.mortar.tokenColorDarkGrey,
      borderRadius: spacing(0.5),
      backgroundColor: palette.mortar.tokenColorPrimaryWhite,
      borderColor: palette.mortar.tokenColorLightGrey,
      borderWidth: 1,
    },
    addButton: {
      color: palette.mortarV3.tokenDefault500,
    },
  };
});
