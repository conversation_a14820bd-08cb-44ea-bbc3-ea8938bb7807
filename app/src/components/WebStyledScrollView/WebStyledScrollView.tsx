import React from 'react';
import { ScrollView, ScrollViewProps } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { IS_WEB } from 'src/constants';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';

export const WebStyledScrollView: React.FC<ScrollViewProps> = (props) => {
  const isDesktop = useDesktopMediaQuery();
  return (
    <ScrollView
      {...props}
      contentContainerStyle={[
        styles.container,
        IS_WEB && isDesktop && styles.containerWeb,
        props.contentContainerStyle,
      ]}
    />
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    backgroundColor: palette.mortar.tokenColorLightBlue,
    maxWidth: spacing(100),
    width: '100%',
    marginHorizontal: 'auto',
  },
  containerWeb: {
    maxWidth: spacing(102),
    paddingHorizontal: spacing(1),
    paddingVertical: spacing(3),
  },
}));
