import React, { ReactElement, ReactNode, useState } from 'react';
import { StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';
import {
  Icon,
  NewTypographyVariants,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { tokenColorBlack } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';

const TEST_IDS = createTestIds('expanding-dropdown', {
  TOUCHABLE_EXPAND: 'touchable-expand',
  TITLE: 'title',
  ADDITIONAL_TITLE_TEXT: 'additional-title-text',
  CONTENT: 'content',
  COUNTER: 'counter',
  CTA: 'cta',
});

type ExpandingDropdownProps = {
  collapsedText: string;
  additionalTitleText?: string;
  children: ReactNode;
  expandedText?: string;
  revertColorsInExpandedState?: boolean;
  titleColor?: string;
  chevronColor?: string;
  counter?: number;
  isInitiallyExpanded?: boolean;
  alignChevronRight?: boolean;
  titleTypographyVariant?: NewTypographyVariants;
  containerWrapperStyle?: StyleProp<ViewStyle>;
  containerWrapperStyleExpanded?: StyleProp<ViewStyle>;
  containerStyleOverwrite?: StyleProp<ViewStyle>;
  onPressStyleOverwrite?: StyleProp<ViewStyle>;
  onPressTextStyleOverwrite?: StyleProp<ViewStyle>;
  containerStyleExpandedOverwrite?: StyleProp<ViewStyle>;
  ctaOnPress?: () => void;
  ctaName?: string;
  testID?: string;
};

/**
 * A component that displays a collapsible content area with a title and an optional counter.
 * The content can be expanded or collapsed with a chevron icon.
 *
 * @param {ExpandingDropdownProps} props - The props for the component.
 * @returns {ReactElement} The ExpandingDropdown component.
 */
export const ExpandingDropdown = ({
  collapsedText,
  expandedText,
  additionalTitleText,
  revertColorsInExpandedState,
  titleColor,
  isInitiallyExpanded = false,
  chevronColor,
  counter,
  alignChevronRight,
  titleTypographyVariant = 'textLinkSmallBold',
  children,
  containerWrapperStyle,
  containerWrapperStyleExpanded,
  containerStyleOverwrite,
  containerStyleExpandedOverwrite,
  onPressStyleOverwrite,
  onPressTextStyleOverwrite,
  ctaOnPress,
  ctaName,
  testID,
}: ExpandingDropdownProps): ReactElement => {
  // State section
  const [showContent, setShowContent] = useState(isInitiallyExpanded);

  // Handler section
  const handleToggle = () => {
    setShowContent((value) => !value);
  };

  // Style section
  const containerWrapperStyles = [
    containerWrapperStyle,
    showContent &&
      containerWrapperStyleExpanded &&
      containerWrapperStyleExpanded,
  ];
  const centeredRowStyles = [styles.centredRow, containerStyleOverwrite];
  const onPressStyleOverwriteStyles = [
    styles.row,
    revertColorsInExpandedState && showContent
      ? styles.onPressRevertedAndExpanded
      : styles.onPressReverted,
    onPressStyleOverwrite,
    showContent && containerStyleExpandedOverwrite,
  ];
  const counterCircleStyles = [
    styles.counterCircle,
    revertColorsInExpandedState &&
      showContent &&
      styles.revertedCounterCircleColorInExpandedState,
  ];
  const titleStyles = [
    titleColor ? { color: titleColor } : styles.blackText,
    onPressTextStyleOverwrite,
  ];

  // Render section
  return (
    <View testID={testID || TEST_IDS.ROOT} style={containerWrapperStyles}>
      <View style={centeredRowStyles}>
        <TouchableOpacity
          style={onPressStyleOverwriteStyles}
          onPress={handleToggle}
          testID={TEST_IDS.TOUCHABLE_EXPAND}
        >
          {counter != null ? (
            <View style={counterCircleStyles}>
              <Typography
                testID={TEST_IDS.COUNTER}
                useVariant="labelSemiBold"
                style={styles.blackText}
              >
                {counter}
              </Typography>
            </View>
          ) : null}
          <View style={styles.titleContainer}>
            <Typography useVariant={titleTypographyVariant} style={titleStyles}>
              {showContent ? expandedText || collapsedText : collapsedText}
            </Typography>
            {additionalTitleText && (
              <View style={styles.additionalTextContainer}>
                <Typography
                  testID={TEST_IDS.ADDITIONAL_TITLE_TEXT}
                  useVariant="bodySMRegular"
                  style={styles.additionalText}
                >
                  {additionalTitleText}
                </Typography>
              </View>
            )}
          </View>

          {ctaOnPress && ctaName && (
            <Typography
              testID={TEST_IDS.CTA}
              style={styles.dropdownLink}
              useVariant="bodySMSemiBold"
              onPress={ctaOnPress}
            >
              {ctaName}
            </Typography>
          )}

          <Icon
            name={showContent ? 'chevron-up' : 'chevron-down'}
            size={18}
            color={chevronColor ? chevronColor : tokenColorBlack}
            style={alignChevronRight ? styles.chevronRight : styles.chevron}
          />
        </TouchableOpacity>
      </View>
      {showContent && <View testID={TEST_IDS.CONTENT}>{children}</View>}
    </View>
  );
};

ExpandingDropdown.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  centredRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    paddingTop: spacing(2),
  },
  row: {
    flexDirection: 'row',
  },
  chevron: {
    marginLeft: spacing(1),
    marginTop: spacing(0.25),
  },
  chevronRight: {
    marginTop: spacing(0.5),
    marginLeft: 'auto',
  },
  blackText: {
    color: palette.mortar.tokenColorBlack,
  },
  counterCircle: {
    width: spacing(3.5),
    height: spacing(3.5),
    borderRadius: spacing(1.75),
    backgroundColor: palette.mortar.tokenColorBlueEight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing(1),
  },
  revertedCounterCircleColorInExpandedState: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  onPressReverted: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderRadius: spacing(0.5),
  },
  onPressRevertedAndExpanded: {
    borderTopStartRadius: spacing(0.5),
    borderTopEndRadius: spacing(0.5),
    backgroundColor: palette.mortar.tokenColorBlueEight,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  additionalTextContainer: {
    marginLeft: spacing(1.5),
  },
  additionalText: {
    color: palette.mortar.tokenColorBlueGrey,
  },
  dropdownLink: {
    color: palette.mortar.tokenColorSystemLinkBlue,
    alignSelf: 'center',
    position: 'absolute',
    right: spacing(5.5),
  },
}));
