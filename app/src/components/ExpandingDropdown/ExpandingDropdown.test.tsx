import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { Typography } from '@cat-home-experts/react-native-components';
import { ExpandingDropdown } from 'src/components/ExpandingDropdown/ExpandingDropdown';
import { getFlattenedStyles } from '@cat-home-experts/react-native-utilities';

describe('Components | ExpandingDropdown', () => {
  const defaultProps = {
    collapsedText: 'Open me',
    expandedText: 'Close me',
  };

  afterEach(cleanup);

  it('renders with custom test Id', () => {
    const customId = 'test-id';

    const { getByTestId } = render(
      <ExpandingDropdown {...defaultProps} testID={customId}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const component = getByTestId(customId);

    expect(component).toBeDefined();
  });

  it('renders expanding dropdown in initial collapsed state', () => {
    const { getByTestId, queryByTestId } = render(
      <ExpandingDropdown {...defaultProps}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const expandingDropdown = getByTestId(ExpandingDropdown.testIds.ROOT);
    const childView = queryByTestId(ExpandingDropdown.testIds.CONTENT);

    expect(expandingDropdown).toBeDefined();
    expect(childView).toBeNull();
  });

  it('can expand child view', () => {
    const childViewTestId = 'expanding-dropdown-child-view';
    const { getByTestId, queryByTestId } = render(
      <ExpandingDropdown {...defaultProps}>
        <Typography testID={childViewTestId}>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const expandTouch = getByTestId(ExpandingDropdown.testIds.TOUCHABLE_EXPAND);

    //initial state should be collapsed
    const childViewCollapsed = queryByTestId(ExpandingDropdown.testIds.CONTENT);
    expect(childViewCollapsed).toBeNull();

    fireEvent.press(expandTouch);

    // after press event, should be expanded
    const childViewAfter = getByTestId(ExpandingDropdown.testIds.CONTENT);
    const childViewContent = getByTestId(childViewTestId);
    expect(childViewAfter).toBeDefined();
    expect(childViewContent).toBeDefined();
  });

  it('shows correct title text', () => {
    const { getByTestId, getByText } = render(
      <ExpandingDropdown {...defaultProps}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const expandTouch = getByTestId(ExpandingDropdown.testIds.TOUCHABLE_EXPAND);

    //initial state should be collapsed text
    const titleCollapsed = getByText(defaultProps.collapsedText);
    expect(titleCollapsed).toBeDefined();

    fireEvent.press(expandTouch);

    // after press event, should be expanded text
    const titleExpanded = getByText(defaultProps.expandedText);
    expect(titleExpanded).toBeDefined();
  });

  it('hides content when re-collapsing the dropdown', () => {
    const { getByTestId, queryByTestId, getByText } = render(
      <ExpandingDropdown {...defaultProps}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const expandTouch = getByTestId(ExpandingDropdown.testIds.TOUCHABLE_EXPAND);

    // initial state should be collapsed
    const childViewCollapsed = queryByTestId(ExpandingDropdown.testIds.CONTENT);
    const titleCollapsed = getByText(defaultProps.collapsedText);
    expect(titleCollapsed).toBeDefined();
    expect(childViewCollapsed).toBeNull();

    fireEvent.press(expandTouch);

    // after first press event, should be expanded
    const titleExpanded = getByText(defaultProps.expandedText);
    const childViewExpanded = getByTestId(ExpandingDropdown.testIds.CONTENT);
    expect(titleExpanded).toBeDefined();
    expect(childViewExpanded).toBeDefined();

    fireEvent.press(expandTouch);

    // after second press event, should be collapsed
    const titleReCollapsed = getByText(defaultProps.collapsedText);
    const childViewReCollapsed = queryByTestId(
      ExpandingDropdown.testIds.CONTENT,
    );
    expect(titleReCollapsed).toBeDefined();
    expect(childViewReCollapsed).toBeNull();
  });

  it('can set a custom colour', () => {
    const customColor = 'red';
    const { getByText } = render(
      <ExpandingDropdown {...defaultProps} titleColor={customColor}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const titleText = getByText(defaultProps.collapsedText);
    const titleStyles = getFlattenedStyles(titleText);

    expect(titleStyles).toMatchObject({ color: customColor });
  });

  it('displays the counter when provided', () => {
    const counterValue = 5;
    const { getByTestId } = render(
      <ExpandingDropdown {...defaultProps} counter={counterValue}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const counterText = getByTestId(ExpandingDropdown.testIds.COUNTER);
    expect(counterText).toBeDefined();
  });

  it("doesn't displays the counter when it's not provided", () => {
    const { queryByTestId } = render(
      <ExpandingDropdown {...defaultProps}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const counterText = queryByTestId(ExpandingDropdown.testIds.COUNTER);
    expect(counterText).toBeNull();
  });

  it('renders additional title text when provided', () => {
    const additionalText = 'Additional text';
    const { getByTestId } = render(
      <ExpandingDropdown {...defaultProps} additionalTitleText={additionalText}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const additionalTitleText = getByTestId(
      ExpandingDropdown.testIds.ADDITIONAL_TITLE_TEXT,
    );
    expect(additionalTitleText).toHaveTextContent(additionalText);
  });

  it('does not render additional title text when not provided', () => {
    const { queryByTestId } = render(
      <ExpandingDropdown {...defaultProps}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const additionalTitleText = queryByTestId(
      ExpandingDropdown.testIds.ADDITIONAL_TITLE_TEXT,
    );
    expect(additionalTitleText).toBeNull();
  });

  it('renders the correct counter string', () => {
    const counterValue = 5;
    const { getByTestId } = render(
      <ExpandingDropdown {...defaultProps} counter={counterValue}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const counterText = getByTestId(ExpandingDropdown.testIds.COUNTER);
    expect(counterText).toHaveTextContent('5');
  });

  it('renders a button in the dropdown if ctaName and functionality passed in', () => {
    const mockCTA = jest.fn();
    const { getByTestId } = render(
      <ExpandingDropdown {...defaultProps} ctaName="Edit" ctaOnPress={mockCTA}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const cta = getByTestId(ExpandingDropdown.testIds.CTA);

    expect(cta).toBeDefined();
  });

  it('calls cta function if present and pressed', () => {
    const mockCTA = jest.fn();
    const { getByTestId } = render(
      <ExpandingDropdown {...defaultProps} ctaName="Edit" ctaOnPress={mockCTA}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const cta = getByTestId(ExpandingDropdown.testIds.CTA);

    fireEvent.press(cta);

    expect(mockCTA).toHaveBeenCalledTimes(1);
  });

  it("doesn't render a button if ctaName is missing", () => {
    const mockCTA = jest.fn();
    const { queryByTestId } = render(
      <ExpandingDropdown {...defaultProps} ctaOnPress={mockCTA}>
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const cta = queryByTestId(ExpandingDropdown.testIds.CTA);

    expect(cta).toBeNull();
  });

  it("doesn't render a button if cta functionality is missing", () => {
    const { queryByTestId } = render(
      <ExpandingDropdown {...defaultProps} ctaName="Edit">
        <Typography>{'Some text'}</Typography>
      </ExpandingDropdown>,
    );

    const cta = queryByTestId(ExpandingDropdown.testIds.CTA);

    expect(cta).toBeNull();
  });
});
