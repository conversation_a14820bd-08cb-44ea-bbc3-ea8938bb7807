import React, { SetStateAction } from 'react';
import { TouchableOpacity } from 'react-native';
import { Icon, Typography } from '@cat-home-experts/react-native-components';
import { IS_WEB } from 'src/constants';
import { palette as staticPalette } from '@cat-home-experts/react-native-utilities/dist/styles/styles';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';

interface DropdownTriggerProps<T> {
  selection?: T;
  displayGetter: (v: T) => string;
  placeholder: string;
  setPresented: React.Dispatch<SetStateAction<boolean>>;
}

const TEST_IDS = createTestIds('dropdown-trigger', {
  DROPDOWN_TRIGGER: 'dropdown-trigger',
});

export function DropdownTrigger<T>({
  selection,
  displayGetter = (v) => v as string,
  placeholder,
  setPresented,
}: DropdownTriggerProps<T>): ReturnType<React.FC> {
  return (
    <TouchableOpacity
      testID={TEST_IDS.DROPDOWN_TRIGGER}
      style={styles.triggerRoot}
      onPress={() => setPresented(true)}
    >
      <Typography
        useVariant="bodyRegular"
        style={[styles.triggerText, !selection && styles.triggerTextNoValue]}
        numberOfLines={1}
        ellipsizeMode="tail"
      >
        {selection ? displayGetter(selection) : placeholder}
      </Typography>
      {IS_WEB && (
        <Icon
          name="chevron-down"
          color={staticPalette.mortar.tokenColorBlueGrey}
          size={20}
        />
      )}
    </TouchableOpacity>
  );
}

DropdownTrigger.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  triggerRoot: {
    height: spacing(6.25),
    borderRadius: spacing(0.5),
    paddingHorizontal: spacing(1.5),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: palette.mortar.tokenColorLightGrey,
    gap: spacing(1),
  },
  triggerText: {
    flex: 1,
  },
  triggerTextNoValue: {
    color: palette.mortar.tokenColorBlueGrey,
  },
}));
