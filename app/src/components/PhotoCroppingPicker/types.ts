import type { ImagePicked } from 'src/screens/Photos/photos.types';

export interface PhotoCropperProps {
  ruleOfThirds?: boolean;
  guideAspectRatio: number;
  image: ImagePicked | null;
  disabled?: boolean;
  onDismiss?: () => void;
  onCrop?: (value: ImagePicked) => void;
}

export interface PhotoCroppingPickerProps extends PhotoCropperProps {
  visible?: boolean;
}

export interface GetPhotoCropperDatConfig {
  image: ImagePicked | null;
  guideRatio: number;
  screenWidth: number;
}

export interface PhotoCropperData {
  imageQuadrants: Record<'x' | 'y', number>;
  guideQuadrants: Record<'x' | 'y', number>;
  initialScale: number;
}
