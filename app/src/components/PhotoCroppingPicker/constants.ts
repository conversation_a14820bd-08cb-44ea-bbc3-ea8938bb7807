import { Easing, ReduceMotion } from 'react-native-reanimated';
import { spacing } from '@cat-home-experts/react-native-utilities';
import type { DimensionValue } from 'react-native';

export const PHOTO_CROPPING_PICKER_DOUBLE_TAP_TIMING_CONFIG = {
  duration: 750,
  easing: Easing.out(Easing.exp),
  reduceMotion: 'system' as ReduceMotion,
};

export const PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG = {
  duration: 750,
  easing: Easing.out(Easing.exp),
  reduceMotion: 'system' as ReduceMotion,
};

export const PHOTO_CROPPING_PICKER_PADDING = spacing(2);

export const PHOTO_CROPPING_PICKER_TEXT = {
  DEFAULT_TITLE: 'Crop Photo',
};

export const MAX_IMAGE_SIZE = 1600;

export const MINIMUM_PIXELS = 4;

export const EXTRA_SPACE_WEB = 136;

export const MAXIMUM_SIZE =
  `calc(90vh - ${EXTRA_SPACE_WEB}px)` as DimensionValue;
