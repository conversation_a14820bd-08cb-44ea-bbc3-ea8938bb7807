import { useEffect } from 'react';
import type { PhotoCroppingPickerProps } from '../types';

export const PhotoCroppingPicker: React.FC<PhotoCroppingPickerProps> = ({
  visible,
  onCrop,
}) => {
  useEffect(() => {
    if (!visible) {
      return;
    }

    onCrop?.({
      uri: '123.jpg',
      width: 600,
      height: 400,
      mimeType: 'image/jpeg',
    });
  }, [onCrop, visible]);

  return null;
};
