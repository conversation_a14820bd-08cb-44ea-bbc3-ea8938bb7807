import { PixelRatio } from 'react-native';
import {
  ImageManipulator,
  SaveFormat,
  type ImageResult,
} from 'expo-image-manipulator';
import {
  MAX_IMAGE_SIZE,
  MINIMUM_PIXELS,
} from 'src/components/PhotoCroppingPicker/constants';
import type { ImagePicked } from 'src/screens/Photos/photos.types';
import type { ImagePickerAssetExt } from 'src/utilities/photos/ImagePickerExt/ImagePickerExt.types';
import type { GetPhotoCropperDatConfig, PhotoCropperData } from './types';

/**
 * Utility for devising how far is "outside of bounds"
 * @param image
 * @param guideRatio
 * @param screenWidth
 */
export const getPhotoCropperData = ({
  image,
  guideRatio,
  screenWidth,
}: GetPhotoCropperDatConfig): PhotoCropperData => {
  if (!image) {
    return {
      imageQuadrants: { x: 0, y: 0 },
      guideQuadrants: { x: 0, y: 0 },
      initialScale: 1,
    };
  }

  const pixelRatio = PixelRatio.get();
  const screenWidthTruePixels = screenWidth * pixelRatio;
  const initialScale = screenWidthTruePixels / image.width;
  const xImageQuadrant = (image.width * initialScale) / 2;
  const yImageQuadrant = (image.height * initialScale) / 2;
  const xGuideQuadrant = screenWidthTruePixels / 2;
  const yGuideQuadrant = screenWidthTruePixels / guideRatio / 2;

  return {
    initialScale,
    imageQuadrants: { x: xImageQuadrant, y: yImageQuadrant },
    guideQuadrants: { x: xGuideQuadrant, y: yGuideQuadrant },
  };
};

/**
 * Checks the mimeType to see if an image is a JPEG
 * @param mimeType
 */
export const isJpeg = (mimeType?: string): boolean =>
  ['image/jpg', 'image/jpeg'].includes(mimeType ?? '');

/**
 * Performs the cropping (and size limiting) on an image
 * Implementation is based on this:
 *  https://github.com/expo/expo/blob/7bf94d67bbd2e5977c522fccde13c896eb04c331/packages/expo-image-manipulator/src/ImageManipulator.ts#L23-L54
 *
 * @param originX
 * @param originY
 * @param width
 * @param height
 * @param mimeType
 * @param uri
 */
export const cropImage = async (
  originX: number,
  originY: number,
  width: number,
  height: number,
  { mimeType, uri }: ImagePickerAssetExt,
): Promise<ImageResult> => {
  const format = isJpeg(mimeType) ? SaveFormat.JPEG : SaveFormat.PNG;

  const context = ImageManipulator.manipulate(uri);
  context.crop({ originX, originY, width, height });
  context.resize(
    width > height
      ? { width: Math.min(width, MAX_IMAGE_SIZE) }
      : { height: Math.min(height, MAX_IMAGE_SIZE) },
  );
  const image = await context.renderAsync();
  const result = await image.saveAsync({ format, compress: 1 });

  // These shared objects will not be used anymore, so free up some memory.
  context.release();
  image.release();

  return result;
};

/**
 * Gets the maximum scale the image can be zoomed to
 *
 * @param image
 */
export const getMaximumZoom = (image?: ImagePicked | null): number => {
  if (!image) {
    return 1;
  }

  const smallestDimension = Math.min(image.width, image.height);
  return Math.ceil(smallestDimension / MINIMUM_PIXELS);
};
