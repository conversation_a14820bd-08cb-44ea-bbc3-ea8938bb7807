import React, { useEffect, useMemo, useState, type WheelEvent } from 'react';
import {
  PixelRatio,
  StyleSheet,
  View,
  type LayoutChangeEvent,
  type ViewStyle,
} from 'react-native';
import { Button } from '@cat-home-experts/react-native-components';
import {
  alpha,
  createMortarStyles,
} from '@cat-home-experts/react-native-utilities';
import Animated, {
  cancelAnimation,
  clamp,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import type { ImagePicked } from 'src/screens/Photos/photos.types';
import { PinchGesture } from '@use-gesture/vanilla';
import {
  MAXIMUM_SIZE,
  PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
  PHOTO_CROPPING_PICKER_PADDING,
} from './constants';
import { PhotoCropperViewFinder } from './PhotoCropperViewFinder';
import type { PhotoCropperProps } from './types';
import { cropImage, getMaximumZoom, getPhotoCropperData } from './utilities';

const PIXEL_RATIO = PixelRatio.get();

export const PhotoCropper: React.FC<PhotoCropperProps> = ({
  image,
  guideAspectRatio,
  disabled,
  ruleOfThirds,
  onCrop,
  onDismiss,
}) => {
  // Ref
  const squareRef = React.useRef<HTMLDivElement>();
  const timeoutRef = React.useRef<NodeJS.Timeout | undefined>(undefined);

  // State
  const [imageLayout, setImageLayout] = useState<{
    width: number;
    height: number;
  }>({ width: 0, height: 0 });

  // Computed Values
  const viewPortWidth = useMemo(
    () => imageLayout.width - PHOTO_CROPPING_PICKER_PADDING * 2,
    [imageLayout],
  );
  const aspectRatio = useMemo(() => {
    if (!image) {
      return 0;
    }

    return image.width / image.height;
  }, [image]);
  const safeAspect = useMemo(
    () => Math.max(1, Math.min(aspectRatio, guideAspectRatio)),
    [guideAspectRatio, aspectRatio],
  );
  const { imageQuadrants, guideQuadrants } = getPhotoCropperData({
    image,
    guideRatio: guideAspectRatio,
    screenWidth: viewPortWidth,
  });

  // Animated
  const rulesOpacity = useSharedValue(0);
  const scale = useSharedValue(1);
  const trueScale = useSharedValue(1);
  const positionOffset = useSharedValue({ x: 0, y: 0 });
  const startOffset = useSharedValue({ x: 0, y: 0 });
  const scaleFloor = useMemo(
    () => Math.max(aspectRatio / guideAspectRatio, 1),
    [aspectRatio, guideAspectRatio],
  );
  const scaleCeiling = useMemo(() => getMaximumZoom(image), [image]);
  const minimumScale = useDerivedValue(() => {
    return Math.max(scaleFloor, scale.value);
  }, [scaleFloor]);
  const xBound = useDerivedValue(
    () =>
      (imageQuadrants.x * minimumScale.value - guideQuadrants.x) / PIXEL_RATIO,
    [image, imageQuadrants, guideQuadrants],
  );
  const yBound = useDerivedValue(
    () =>
      (imageQuadrants.y * minimumScale.value - guideQuadrants.y) / PIXEL_RATIO,
    [image, imageQuadrants, guideQuadrants],
  );
  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: positionOffset.value.x },
        { translateY: positionOffset.value.y },
        { scale: scale.value },
      ],
    };
  });

  // Gestures
  const panGesture = Gesture.Pan()
    .enabled(!disabled)
    .onStart((_e) => {
      rulesOpacity.value = 1;
      cancelAnimation(positionOffset);
    })
    .onUpdate((e) => {
      positionOffset.value = {
        x: clamp(
          e.translationX + startOffset.value.x,
          -xBound.value,
          xBound.value,
        ),
        y: clamp(
          e.translationY + startOffset.value.y,
          -yBound.value,
          yBound.value,
        ),
      };
    })
    .onEnd(() => {
      startOffset.value = positionOffset.value;
      rulesOpacity.value = 0;
    });

  // Methods
  const handleLayout = (event: LayoutChangeEvent) =>
    setImageLayout(event.nativeEvent.layout);

  const handleCrop = async () => {
    const viewPortHeight = viewPortWidth / guideAspectRatio;
    const scrollableX = viewPortWidth * minimumScale.value - viewPortWidth;
    const scrollableY =
      (viewPortWidth / aspectRatio) * minimumScale.value - viewPortHeight;
    const width = image!.width / minimumScale.value;
    const height = image!.width / guideAspectRatio / minimumScale.value;
    const originX =
      (Math.round(-positionOffset.value.x + scrollableX / 2) /
        (scrollableX || 1)) *
      (image!.width - width);
    const originY =
      (Math.round(-positionOffset.value.y + scrollableY / 2) /
        (scrollableY || 1)) *
      (image!.height - height);

    const newImage = await cropImage(originX, originY, width, height, image!);
    const response = await fetch(newImage.uri);
    const blob = await response.blob();

    const asImagePicked = {
      ...newImage,
      fileName: image!.fileName,
      fileSize: blob.size,
      mimeType: image!.mimeType,
    } as object as ImagePicked;

    onCrop?.(asImagePicked);
  };

  // Effects
  useEffect(() => {
    const handleZoom = (event: Event) => {
      clearTimeout(timeoutRef.current);
      const wheelEvent = event as unknown as WheelEvent;

      const delta =
        // @ts-expect-error Ancient property, but you never know
        'wheelData' in event ? event.wheelDelta : -wheelEvent.deltaY;

      const newScaleValue = clamp(
        trueScale.value * (delta > 0 ? 0.8 : 1.2),
        scaleFloor,
        scaleCeiling,
      );
      trueScale.value = newScaleValue;
      scale.value = newScaleValue;
      rulesOpacity.value = 1;

      const xScopedBound =
        (imageQuadrants.x * newScaleValue - guideQuadrants.x) / PIXEL_RATIO;
      const yScopedBound =
        (imageQuadrants.y * newScaleValue - guideQuadrants.y) / PIXEL_RATIO;

      const x = clamp(positionOffset.value.x, -xScopedBound, xScopedBound);
      const y = clamp(positionOffset.value.y, -yScopedBound, yScopedBound);
      positionOffset.value = { x, y };
      startOffset.value = { x, y };
      timeoutRef.current = setTimeout(() => {
        rulesOpacity.value = 0;
        timeoutRef.current = undefined;
      });
    };

    const cancelEvent = (event: Event) => event.preventDefault();

    const pinchGesture = new PinchGesture(
      squareRef.current as EventTarget,
      ({ offset: [newScale], last }) => {
        if (!last) {
          scale.value = newScale;
          trueScale.value = newScale;
          return;
        }

        const clamped = clamp(newScale, scaleFloor, scaleCeiling);
        trueScale.value = clamped;
        scale.value = withTiming(
          clamped,
          PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
        );

        rulesOpacity.value = 1;

        const xScopedBound =
          (imageQuadrants.x * clamped - guideQuadrants.x) / PIXEL_RATIO;
        const yScopedBound =
          (imageQuadrants.y * clamped - guideQuadrants.y) / PIXEL_RATIO;

        const x = clamp(positionOffset.value.x, -xScopedBound, xScopedBound);
        const y = clamp(positionOffset.value.y, -yScopedBound, yScopedBound);

        startOffset.value = { x, y };
        positionOffset.value = withTiming(
          { x, y },
          PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
        );
        timeoutRef.current = setTimeout(() => {
          rulesOpacity.value = 0;
          timeoutRef.current = undefined;
        });
      },
      {
        scaleBounds: { min: scaleFloor, max: scaleCeiling },
        rubberband: true,
        from: () => [scale.value, 0],
      },
    );

    squareRef.current?.addEventListener('wheel', handleZoom);
    document.addEventListener('gesturestart', cancelEvent);
    document.addEventListener('gesturechange', cancelEvent);
    document.addEventListener('gestureend', cancelEvent);

    return () => {
      squareRef.current?.removeEventListener('wheel', handleZoom);
      document.removeEventListener('gesturestart', cancelEvent);
      document.removeEventListener('gesturechange', cancelEvent);
      document.removeEventListener('gestureend', cancelEvent);
      pinchGesture.destroy();
    };
  }, [
    guideQuadrants,
    imageQuadrants,
    positionOffset,
    rulesOpacity,
    scaleFloor,
    scaleCeiling,
    scale,
    startOffset,
    trueScale,
  ]);

  useEffect(() => {
    scale.value = scaleFloor;
  }, [scale, scaleFloor]);

  return (
    <View style={[styles.flex, styles.fullWidth, styles.maxWidth]}>
      <View
        ref={(value) => {
          squareRef.current = value as unknown as HTMLDivElement;
        }}
        style={[styles.square, styles.center, styles.maxHeight]}
        onLayout={handleLayout}
      >
        <View
          style={[styles.fullWidth, styles.center, { aspectRatio: safeAspect }]}
        >
          <GestureDetector gesture={panGesture}>
            <Animated.View
              style={[
                styles.image,
                styles.fullWidth,
                { aspectRatio },
                animatedStyles,
              ]}
            >
              <Animated.Image
                source={{ uri: image?.uri }}
                style={StyleSheet.absoluteFill}
              />
            </Animated.View>
          </GestureDetector>
        </View>
        <View style={[StyleSheet.absoluteFill, styles.overlay]}>
          <View style={[styles.mask, styles.flex]} />
          <View style={styles.row}>
            <View style={[styles.sideMask, styles.mask]} />
            <PhotoCropperViewFinder
              guideAspectRatio={guideAspectRatio}
              ruleOfThirds={ruleOfThirds}
              rulesOpacity={rulesOpacity}
            />
            <View style={[styles.sideMask, styles.mask]} />
          </View>
          <View style={[styles.mask, styles.flex]} />
        </View>
      </View>
      <View style={[styles.row, styles.spaceBetween, styles.fullWidth]}>
        <Button
          size="small"
          label="Cancel"
          variant="primary"
          onPress={() => onDismiss?.()}
        />
        <Button
          size="small"
          label="Submit"
          variant="secondary"
          onPress={handleCrop}
        />
      </View>
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  flex: { flex: 1 },
  fullWidth: { width: '100%' },
  fullHeight: { height: '100%' },
  maxHeight: { maxHeight: MAXIMUM_SIZE },
  maxWidth: { maxWidth: MAXIMUM_SIZE },
  row: { flexDirection: 'row' },
  square: {
    backgroundColor: palette.mortar.tokenColorDarkGrey,
    padding: PHOTO_CROPPING_PICKER_PADDING,
    overflow: 'hidden',
    position: 'relative',
  },
  spaceBetween: {
    justifyContent: 'space-between',
    padding: spacing(2),
  },
  guide: {
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: palette.mortar.tokenColorPrimaryWhite,
    flexGrow: 1,
  },
  mask: {
    backgroundColor: alpha(palette.mortar.tokenColorBlack, 0.75),
  },
  sideMask: {
    height: '100%',
    minWidth: PHOTO_CROPPING_PICKER_PADDING,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    pointerEvents: 'none',
  },
  image: {
    ['cursor' as keyof ViewStyle]: 'grab',
  },
}));
