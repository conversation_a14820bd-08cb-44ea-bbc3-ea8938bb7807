import React, { useMemo } from 'react';
import { View, Modal } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { Typography } from '@cat-home-experts/react-native-components';
import { IS_ANDROID, IS_WEB } from 'src/constants';
import { gestureHandlerRootHOC } from 'react-native-gesture-handler';
import { PHOTO_CROPPING_PICKER_TEXT } from './constants';
import { PhotoCropper } from './PhotoCropper';
import { PhotoCroppingPickerWebWrapper } from './PhotoCroppingPickerWebWrapper';
import { PhotoCroppingPickerProps } from './types';

export const PhotoCroppingPicker: React.FC<PhotoCroppingPickerProps> = ({
  image,
  visible,
  guideAspectRatio,
  ruleOfThirds,
  onCrop,
  onDismiss,
}) => {
  const SafeCropper = useMemo(() => {
    const cropper = () => (
      <PhotoCropper
        ruleOfThirds={ruleOfThirds}
        guideAspectRatio={guideAspectRatio}
        image={image}
        onDismiss={onDismiss}
        onCrop={onCrop}
      />
    );

    return IS_ANDROID ? gestureHandlerRootHOC(cropper) : cropper;
  }, [ruleOfThirds, guideAspectRatio, image, onDismiss, onCrop]);

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType={!IS_WEB ? 'slide' : 'fade'}
      presentationStyle={!IS_WEB ? 'pageSheet' : undefined}
      hardwareAccelerated={!IS_WEB}
      onRequestClose={onDismiss}
      onDismiss={onDismiss}
      transparent={IS_WEB}
      // interactiveDismissal
    >
      <View style={styles.flex}>
        <PhotoCroppingPickerWebWrapper onDismiss={onDismiss}>
          <View style={[styles.header, IS_WEB && styles.headerWeb]}>
            <Typography
              useVariant="subHeadingRegular"
              style={!IS_WEB && styles.headerText}
            >
              {PHOTO_CROPPING_PICKER_TEXT.DEFAULT_TITLE}
            </Typography>
          </View>
          <SafeCropper />
        </PhotoCroppingPickerWebWrapper>
      </View>
    </Modal>
  );
};

const styles = createMortarStyles(({ spacing, palette }) => ({
  flex: { flexGrow: 1 },
  header: {
    backgroundColor: palette.mortar.tokenColorDarkGrey,
    height: spacing(8),
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerWeb: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderColor: palette.mortar.tokenColorLighterGrey,
    borderBottomWidth: 1,
    width: '100%',
  },
  headerText: {
    color: palette.mortar.tokenColorPrimaryWhite,
  },
  webModal: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerWebModal: {
    width: '100%',
    maxWidth: spacing(100),
  },
}));
