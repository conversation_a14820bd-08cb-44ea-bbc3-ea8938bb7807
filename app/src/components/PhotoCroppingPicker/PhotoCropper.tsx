import React, { useEffect, useMemo } from 'react';
import {
  Button,
  PixelRatio,
  StyleSheet,
  useWindowDimensions,
  View,
} from 'react-native';
import MaskedView from '@react-native-masked-view/masked-view';
import {
  alpha,
  createMortarStyles,
} from '@cat-home-experts/react-native-utilities';
import Animated, {
  cancelAnimation,
  clamp,
  interpolate,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { ImagePicked } from 'src/screens/Photos/photos.types';
import { getInfoAsync } from 'expo-file-system';
import { PhotoCropperViewFinder } from 'src/components/PhotoCroppingPicker/PhotoCropperViewFinder';
import {
  PHOTO_CROPPING_PICKER_DOUBLE_TAP_TIMING_CONFIG,
  PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
  PHOTO_CROPPING_PICKER_PADDING,
} from './constants';
import type { PhotoCropperProps } from './types';
import {
  cropImage,
  getMaximumZoom,
  getPhotoCropperData,
  isJpeg,
} from './utilities';

const PIXEL_RATIO = PixelRatio.get();

export const PhotoCropper: React.FC<PhotoCropperProps> = ({
  ruleOfThirds,
  guideAspectRatio,
  image,
  disabled,
  onDismiss,
  onCrop,
}) => {
  // Computed Values
  const { width: screenWidth } = useWindowDimensions();
  const { bottom } = useSafeAreaInsets();
  const aspectRatio = useMemo(
    () => (image ? image.width / image.height : 1),
    [image],
  );
  const viewPortWidth = useMemo(
    () => screenWidth - PHOTO_CROPPING_PICKER_PADDING * 2,
    [screenWidth],
  );
  const { imageQuadrants, guideQuadrants } = getPhotoCropperData({
    image,
    guideRatio: guideAspectRatio,
    screenWidth: viewPortWidth,
  });

  // Animations
  const scale = useSharedValue(1);
  const startScale = useSharedValue(1);
  const positionOffset = useSharedValue({ x: 0, y: 0 });
  const startOffset = useSharedValue({ x: 0, y: 0 });
  const rulesOpacity = useSharedValue(0);
  const scaleFloor = useMemo(
    () => Math.max(aspectRatio / guideAspectRatio, 1),
    [aspectRatio, guideAspectRatio],
  );
  const scaleCeiling = useMemo(() => getMaximumZoom(image), [image]);
  const minimumScale = useDerivedValue(
    () => Math.max(scaleFloor, scale.value),
    [scaleFloor],
  );
  const xBound = useDerivedValue(
    () =>
      (imageQuadrants.x * minimumScale.value - guideQuadrants.x) / PIXEL_RATIO,
    [image, imageQuadrants, guideQuadrants],
  );
  const yBound = useDerivedValue(
    () =>
      (imageQuadrants.y * minimumScale.value - guideQuadrants.y) / PIXEL_RATIO,
    [image, imageQuadrants, guideQuadrants],
  );

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: positionOffset.value.x },
        { translateY: positionOffset.value.y },
        { scale: scale.value },
      ],
    };
  });

  // Gestures
  const panGesture = Gesture.Pan()
    .enabled(!disabled)
    .onStart((_e) => {
      rulesOpacity.value = 1;
      cancelAnimation(positionOffset);
    })
    .onUpdate((e) => {
      positionOffset.value = {
        x: e.translationX + startOffset.value.x,
        y: e.translationY + startOffset.value.y,
      };
    })
    .onEnd(() => {
      const x = clamp(positionOffset.value.x, -xBound.value, xBound.value);
      const y = clamp(positionOffset.value.y, -yBound.value, yBound.value);

      positionOffset.value = withTiming(
        { x, y },
        PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
      );
      startOffset.value = withTiming(
        { x, y },
        PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
      );
      rulesOpacity.value = 0;
    });

  const pinchGesture = Gesture.Pinch()
    .enabled(!disabled)
    .onStart((_e) => {
      rulesOpacity.value = 1;
      cancelAnimation(scale);
    })
    .onUpdate((e) => {
      const value = startScale.value * e.scale;

      if (value >= 1) {
        scale.value = value;
        return;
      }

      const interpolated = 1 - Math.pow(1 - (1 - value), 3);
      scale.value = interpolate(interpolated, [0, 1], [1, 0.6]);
    })
    .onEnd(() => {
      if (scale.value < scaleFloor) {
        scale.value = withTiming(
          scaleFloor,
          PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
        );
        startScale.value = withTiming(
          scaleFloor,
          PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
        );
        return;
      }

      if (scale.value > scaleCeiling) {
        scale.value = withTiming(
          scaleCeiling,
          PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
        );
        startScale.value = withTiming(
          scaleCeiling,
          PHOTO_CROPPING_PICKER_OUT_OF_BOUNDS_TIMING_CONFIG,
        );
        return;
      }

      startScale.value = scale.value;
      rulesOpacity.value = 0;
    });

  const pinchPanGesture = Gesture.Simultaneous(panGesture, pinchGesture);
  const doubleTapGesture = Gesture.Tap()
    .enabled(!disabled)
    .numberOfTaps(2)
    .onEnd(() => {
      scale.value = withTiming(
        scaleFloor,
        PHOTO_CROPPING_PICKER_DOUBLE_TAP_TIMING_CONFIG,
      );
      startScale.value = withTiming(
        scaleFloor,
        PHOTO_CROPPING_PICKER_DOUBLE_TAP_TIMING_CONFIG,
      );

      const minBoundX =
        (imageQuadrants.x * scaleFloor - guideQuadrants.x) / PIXEL_RATIO;
      const minBoundY =
        (imageQuadrants.y * scaleFloor - guideQuadrants.y) / PIXEL_RATIO;

      const x = clamp(positionOffset.value.x, -minBoundX, minBoundX);
      const y = clamp(positionOffset.value.y, -minBoundY, minBoundY);

      positionOffset.value = withTiming(
        { x, y },
        PHOTO_CROPPING_PICKER_DOUBLE_TAP_TIMING_CONFIG,
      );
      startOffset.value = withTiming(
        { x, y },
        PHOTO_CROPPING_PICKER_DOUBLE_TAP_TIMING_CONFIG,
      );
    });

  const composedGesture = Gesture.Race(pinchPanGesture, doubleTapGesture);

  // Methods
  const handleCrop = async () => {
    const viewPortHeight = viewPortWidth / guideAspectRatio;
    const scrollableX = viewPortWidth * minimumScale.value - viewPortWidth;
    const scrollableY =
      (viewPortWidth / aspectRatio) * minimumScale.value - viewPortHeight;
    const width = image!.width / minimumScale.value;
    const height = image!.width / guideAspectRatio / minimumScale.value;
    const originX =
      (Math.round(-positionOffset.value.x + scrollableX / 2) /
        (scrollableX || 1)) *
      (image!.width - width);
    const originY =
      (Math.round(-positionOffset.value.y + scrollableY / 2) /
        (scrollableY || 1)) *
      (image!.height - height);

    const newImage = await cropImage(originX, originY, width, height, image!);
    const fileInfo = await getInfoAsync(newImage.uri);

    if (!('size' in fileInfo)) {
      throw Error('Something went wrong!');
    }

    const asImagePicked: ImagePicked = {
      ...newImage,
      fileSize: fileInfo.size,
      mimeType: isJpeg(image!.mimeType) ? 'image/jpg' : 'image/png',
    };

    onCrop?.(asImagePicked);
  };

  // Effects
  useEffect(() => {
    positionOffset.value = { x: 0, y: 0 };
    startOffset.value = { x: 0, y: 0 };

    if (!image) {
      scale.value = 1;
      startScale.value = 1;
      return;
    }

    if (image.width / image.height < 1) {
      scale.value = 1;
      startScale.value = 1;
      return;
    }

    scale.value = image.width / image.height;
    startScale.value = image.width / image.height;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [image]);

  useEffect(() => {
    scale.value = scaleFloor;
  }, [scale, scaleFloor]);

  // Renderers
  const transparentMask = useMemo(
    () => (
      <View style={[styles.flexGrow, styles.maskPassView]}>
        <View style={[styles.maskPassTranslucent, styles.flex]} />
        <View
          style={[
            styles.row,
            styles.image,
            styles.maskPassTranslucent,
            styles.paddingHorizontal,
          ]}
        >
          <View
            style={[
              styles.flexGrow,
              styles.maskPassTransparent,
              { aspectRatio: guideAspectRatio },
            ]}
          />
        </View>
        <View style={[styles.maskPassTranslucent, styles.flex]} />
      </View>
    ),
    [guideAspectRatio],
  );

  return (
    <View style={styles.flexGrow}>
      <GestureDetector gesture={composedGesture}>
        <View style={[styles.flexGrow, styles.mainView]}>
          <MaskedView
            maskElement={transparentMask}
            style={[
              styles.flexGrow,
              styles.center,
              styles.paddingHorizontal,
              StyleSheet.absoluteFill,
            ]}
          >
            <Animated.Image
              source={{ uri: image?.uri }}
              style={[styles.image, { aspectRatio }, animatedStyles]}
            />
          </MaskedView>
          <View
            style={[
              StyleSheet.absoluteFill,
              styles.center,
              styles.paddingHorizontal,
            ]}
          >
            <PhotoCropperViewFinder
              guideAspectRatio={guideAspectRatio}
              ruleOfThirds={ruleOfThirds}
              rulesOpacity={rulesOpacity}
            />
          </View>
        </View>
      </GestureDetector>
      <View
        style={[
          styles.toolbar,
          styles.row,
          { paddingBottom: Math.max(bottom, styles.toolbar.paddingVertical) },
        ]}
      >
        <Button title="Cancel" color="#ff0000" onPress={onDismiss} />
        <Button title="Done" onPress={handleCrop} disabled={!image} />
      </View>
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  flexGrow: { flexGrow: 1 },
  flex: { flex: 1 },
  image: { width: '100%' },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  row: { flexDirection: 'row' },
  padding: { padding: PHOTO_CROPPING_PICKER_PADDING },
  paddingHorizontal: { paddingHorizontal: PHOTO_CROPPING_PICKER_PADDING },
  mainView: {
    backgroundColor: palette.mortar.tokenColorBlack,
    position: 'relative',
    overflow: 'hidden',
  },
  toolbar: {
    backgroundColor: palette.mortar.tokenColorDarkGrey,
    justifyContent: 'space-between',
    paddingVertical: spacing(1.5),
    paddingHorizontal: spacing(2),
  },
  maskPassView: { backgroundColor: 'transparent' },
  maskPassTranslucent: {
    backgroundColor: alpha(palette.mortar.tokenColorPrimaryWhite, 0.35),
  },
  maskPassTransparent: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    flexShrink: 0,
  },
}));
