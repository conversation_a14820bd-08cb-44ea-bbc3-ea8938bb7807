import React from 'react';
import { StyleSheet, View } from 'react-native';
import Animated, {
  Easing,
  SharedValue,
  useAnimatedStyle,
  withDelay,
  withTiming,
} from 'react-native-reanimated';
import {
  alpha,
  createMortarStyles,
  isTruthy,
} from '@cat-home-experts/react-native-utilities';
import { IS_WEB } from 'src/constants';

interface PhotoCropperViewFinderProps {
  ruleOfThirds?: boolean;
  guideAspectRatio: number;
  rulesOpacity: SharedValue<number>;
}

export const PhotoCropperViewFinder: React.FC<PhotoCropperViewFinderProps> = ({
  ruleOfThirds,
  guideAspectRatio,
  rulesOpacity,
}) => {
  // Animated
  const animatedRulesStyle = useAnimatedStyle(() => {
    const opacity = withDelay(
      rulesOpacity.value === 1 ? 0 : 3000,
      withTiming(rulesOpacity.value, {
        duration: 500,
        easing: Easing.ease,
      }),
    );

    return { opacity };
  }, []);

  return (
    <View
      style={[
        IS_WEB ? styles.flex : styles.fullWidth,
        styles.overlayBounds,
        { aspectRatio: guideAspectRatio },
      ]}
    >
      <View style={[styles.corner, styles.topLeftCorner]} />
      <View style={[styles.corner, styles.topRightCorner]} />
      <View style={[styles.corner, styles.bottomRightCorner]} />
      <View style={[styles.corner, styles.bottomLeftCorner]} />
      {isTruthy(ruleOfThirds) && (
        <Animated.View style={[StyleSheet.absoluteFill, animatedRulesStyle]}>
          <View style={[StyleSheet.absoluteFill, styles.verticalRules]}>
            <View style={[styles.rule, styles.verticalRule]} />
            <View style={[styles.rule, styles.verticalRule]} />
          </View>
          <View style={[StyleSheet.absoluteFill, styles.horizontalRules]}>
            <View style={[styles.rule, styles.horizontalRule]} />
            <View style={[styles.rule, styles.horizontalRule]} />
          </View>
        </Animated.View>
      )}
    </View>
  );
};

const styles = createMortarStyles(({ spacing, palette }) => ({
  flex: { flex: 1 },
  fullWidth: { width: '100%' },
  overlayBounds: {
    borderWidth: 1,
    borderColor: alpha(palette.mortar.tokenColorPrimaryWhite, 0.5),
  },
  corner: {
    height: spacing(2),
    width: spacing(2),
    borderColor: palette.mortar.tokenColorPrimaryWhite,
    position: 'absolute',
  },
  topLeftCorner: {
    top: -1,
    left: -1,
    borderTopWidth: 1,
    borderLeftWidth: 1,
  },
  topRightCorner: {
    top: -1,
    right: -1,
    borderTopWidth: 1,
    borderRightWidth: 1,
  },
  bottomLeftCorner: {
    bottom: -1,
    left: -1,
    borderBottomWidth: 1,
    borderLeftWidth: 1,
  },
  bottomRightCorner: {
    bottom: -1,
    right: -1,
    borderBottomWidth: 1,
    borderRightWidth: 1,
  },
  rule: {
    backgroundColor: alpha(palette.mortar.tokenColorPrimaryWhite, 0.75),
  },
  verticalRules: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  verticalRule: {
    width: StyleSheet.hairlineWidth,
    height: '100%',
  },
  horizontalRules: {
    justifyContent: 'space-evenly',
  },
  horizontalRule: {
    height: StyleSheet.hairlineWidth,
    width: '100%',
  },
}));
