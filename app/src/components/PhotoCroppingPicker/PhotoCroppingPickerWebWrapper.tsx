import React from 'react';
import { View, type PointerEvent } from 'react-native';
import {
  alpha,
  createMortarStyles,
} from '@cat-home-experts/react-native-utilities';
import { IS_WEB } from 'src/constants';
import type { PhotoCroppingPickerProps } from './types';
import { MAXIMUM_SIZE } from './constants';

export const PhotoCroppingPickerWebWrapper: React.FC<
  React.PropsWithChildren<Pick<PhotoCroppingPickerProps, 'onDismiss'>>
> = ({ children, onDismiss }) => {
  // Methods
  const handlePointerDown = (event: PointerEvent) => {
    if (event.currentTarget === event.target) {
      onDismiss?.();
    }
  };

  if (!IS_WEB) {
    return <>{children}</>;
  }

  return (
    <View
      style={[styles.flex, styles.webModal]}
      onPointerDown={handlePointerDown}
    >
      <View style={[styles.innerWebModal, styles.maxSize]}>{children}</View>
    </View>
  );
};

const styles = createMortarStyles(({ spacing, palette }) => ({
  flex: { flexGrow: 1 },
  maxSize: { maxWidth: MAXIMUM_SIZE },
  webModal: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: alpha(palette.mortar.tokenColorPrimaryWhite, 0.9),
    padding: spacing(3),
  },
  innerWebModal: {
    width: '100%',
    maxWidth: spacing(100),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderRadius: spacing(1),
    alignItems: 'center',
    overflow: 'hidden',
    shadowColor: palette.system.tokenLegacyColorShadowDefault,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.2,
    shadowRadius: 65,
  },
}));
