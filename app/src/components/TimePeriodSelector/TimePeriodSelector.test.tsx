import React from 'react';
import {
  render,
  screen,
  fireEvent,
  cleanup,
} from '@testing-library/react-native';
import { ViewStyle, TextStyle, StyleSheet } from 'react-native';
import { TimePeriodSelector, TimePeriod } from './TimePeriodSelector';

const mockPeriods: TimePeriod[] = [
  { key: 'day', label: 'Day' },
  { key: 'week', label: 'Week' },
  { key: 'month', label: 'Month' },
];

describe('TimePeriodSelector', () => {
  let mockOnPeriodChange: jest.Mock;

  beforeEach(() => {
    mockOnPeriodChange = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  const renderSelector = (
    props: Partial<React.ComponentProps<typeof TimePeriodSelector>> = {},
  ) => {
    return render(
      <TimePeriodSelector
        periods={mockPeriods}
        selectedPeriodKey="week" // Default selected key for tests
        onPeriodChange={mockOnPeriodChange}
        {...props}
      />,
    );
  };

  it('renders all time periods with correct labels', () => {
    renderSelector();
    mockPeriods.forEach((period) => {
      expect(screen.getByText(period.label)).toBeOnTheScreen();
    });
  });

  it('calls onPeriodChange with the correct key when a period is pressed', () => {
    renderSelector();
    const dayButton = screen.getByText('Day');
    fireEvent.press(dayButton);
    expect(mockOnPeriodChange).toHaveBeenCalledWith('day');

    const monthButton = screen.getByText('Month');
    fireEvent.press(monthButton);
    expect(mockOnPeriodChange).toHaveBeenCalledWith('month');
  });

  it('applies default selected styles to the selected item', () => {
    renderSelector({ selectedPeriodKey: 'week' });
    const weekButton = screen.getByTestId(
      `${TimePeriodSelector.testIds.ROOT}-${TimePeriodSelector.testIds.TIME_ITEM}-week`,
    );
    expect(weekButton.props.style).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ backgroundColor: expect.any(String) }),
      ]),
    );
  });

  it('applies custom selected styles when provided', () => {
    const customItemStyle: ViewStyle = { backgroundColor: 'red' };
    const customTextStyle: TextStyle = { color: 'white', fontSize: 18 };

    renderSelector({
      selectedPeriodKey: 'week',
      selectedItemStyle: customItemStyle,
      selectedTextStyle: customTextStyle,
    });

    const weekButton = screen.getByTestId(
      `${TimePeriodSelector.testIds.ROOT}-${TimePeriodSelector.testIds.TIME_ITEM}-week`,
    );
    const weekText = screen.getByTestId(
      `${TimePeriodSelector.testIds.ROOT}-${TimePeriodSelector.testIds.TIME_ITEM_TITLE}-week`,
    );

    const flattenedItemStyle = StyleSheet.flatten(weekButton.props.style);
    const flattenedTextStyle = StyleSheet.flatten(weekText.props.style);

    expect(flattenedItemStyle).toMatchObject(customItemStyle);
    expect(flattenedTextStyle).toMatchObject(customTextStyle);
  });

  it('applies correct testIDs', () => {
    const customTestID = 'my-custom-selector';
    renderSelector({ testID: customTestID });

    expect(screen.getByTestId(customTestID)).toBeTruthy();
    mockPeriods.forEach((period) => {
      expect(
        screen.getByTestId(
          `${customTestID}-${TimePeriodSelector.testIds.TIME_ITEM}-${period.key}`,
        ),
      ).toBeTruthy();
      expect(
        screen.getByTestId(
          `${customTestID}-${TimePeriodSelector.testIds.TIME_ITEM_TITLE}-${period.key}`,
        ),
      ).toBeTruthy();
    });
  });
});
