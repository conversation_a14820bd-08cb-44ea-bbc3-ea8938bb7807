import React, { ReactElement, useMemo } from 'react';
import { Pressable, StyleProp, TextStyle, View, ViewStyle } from 'react-native';

import { Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';

export interface TimePeriod {
  key: string;
  label: string;
}

interface TimePeriodSelectorProps {
  periods: TimePeriod[];
  selectedPeriodKey: string;
  onPeriodChange: (periodKey: string) => void;
  containerStyle?: StyleProp<ViewStyle>;
  selectedItemStyle?: StyleProp<ViewStyle>;
  selectedTextStyle?: StyleProp<TextStyle>;
  testID?: string;
}

const TEST_IDS = createTestIds('time-period-selector', {
  TIME_ITEM: 'time-item',
  TIME_ITEM_TITLE: 'time-item-title',
});

export function TimePeriodSelector({
  periods,
  selectedPeriodKey,
  onPeriodChange,
  containerStyle,
  selectedItemStyle,
  selectedTextStyle,
  testID = TEST_IDS.ROOT,
}: TimePeriodSelectorProps): ReactElement {
  const finalSelectedItemStyle =
    selectedItemStyle ?? styles.defaultSelectedItem;
  const finalSelectedTextStyle =
    selectedTextStyle ?? styles.defaultSelectedText;

  const renderTimePeriods = useMemo(() => {
    return periods.map((period, index) => {
      const isFirstTimePeriod = index === 0;
      const isLastTimePeriod = index === periods.length - 1;
      const isTimePeriodSelected = selectedPeriodKey === period.key;

      return (
        <Pressable
          key={`timePeriod_${period.key}`}
          testID={`${testID}-${TEST_IDS.TIME_ITEM}-${period.key}`}
          onPress={() => onPeriodChange(period.key)}
          style={[
            styles.timeItem,
            { width: `${100 / periods.length}%` }, // Adjust width based on number of periods
            isFirstTimePeriod && styles.firstTimeItem,
            isLastTimePeriod && styles.lastTimeItem,
            isTimePeriodSelected && finalSelectedItemStyle,
          ]}
        >
          <Typography
            testID={`${testID}-${TEST_IDS.TIME_ITEM_TITLE}-${period.key}`}
            useVariant={isTimePeriodSelected ? 'bodySMBold' : 'bodySMSemiBold'}
            style={[
              styles.timeItemText,
              isTimePeriodSelected && finalSelectedTextStyle,
            ]}
          >
            {period.label}
          </Typography>
        </Pressable>
      );
    });
  }, [
    periods,
    selectedPeriodKey,
    onPeriodChange,
    finalSelectedItemStyle,
    finalSelectedTextStyle,
    testID,
  ]);

  return (
    <View style={[styles.timeItemsContainer, containerStyle]} testID={testID}>
      {renderTimePeriods}
    </View>
  );
}

TimePeriodSelector.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette }) => ({
  timeItemsContainer: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    borderRadius: 8,
    height: 40,
    overflow: 'hidden',
  },
  timeItem: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: palette.mortarV3.tokenNeutral200,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderRightWidth: 1,
  },
  firstTimeItem: {
    borderLeftWidth: 1,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  lastTimeItem: {
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    borderRightWidth: 1,
  },
  timeItemText: {
    color: palette.mortarV3.tokenNeutral800,
  },
  defaultSelectedItem: {
    backgroundColor: palette.mortarV3.tokenDefault100,
  },
  defaultSelectedText: {
    color: palette.mortarV3.tokenNeutral900,
  },
}));
