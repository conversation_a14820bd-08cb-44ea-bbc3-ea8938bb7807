import { render, fireEvent, cleanup } from '@testing-library/react-native';
import React from 'react';
import { palette as staticPalette } from '@cat-home-experts/react-native-utilities';
import { EmoticonRating } from './EmoticonRating';

describe('components | EmoticonRating', () => {
  afterEach(cleanup);

  it('should render correctly', () => {
    const mockOnChange = jest.fn();

    const { getByTestId } = render(<EmoticonRating onChange={mockOnChange} />);
    expect(getByTestId(EmoticonRating.testIds.ROOT)).toBeTruthy();
  });

  it('should call onChange with the correct rating when an emoticon is pressed', async () => {
    const mockOnChange = jest.fn();

    const { getByTestId } = render(<EmoticonRating onChange={mockOnChange} />);
    const emoticon = getByTestId(`${EmoticonRating.testIds.ITEM}-1`);
    await fireEvent.press(emoticon);
    expect(mockOnChange).toHaveBeenCalledWith(1);
  });

  it('should call onChange with undefined when the same emoticon is pressed twice', async () => {
    const mockOnChange = jest.fn();

    const { getByTestId } = render(
      <EmoticonRating value={1} onChange={mockOnChange} />,
    );
    const emoticon = getByTestId(`${EmoticonRating.testIds.ITEM}-1`);
    await fireEvent.press(emoticon);
    expect(mockOnChange).toHaveBeenCalledWith(undefined);
  });

  it('should display the correct emoticon as selected', () => {
    const mockOnChange = jest.fn();

    const { getByTestId } = render(
      <EmoticonRating value={3} onChange={mockOnChange} />,
    );

    // Selected highlighted
    const emoticon3 = getByTestId(`${EmoticonRating.testIds.ICON}-3`);
    // find children
    expect(emoticon3).toHaveProp(
      'fill',
      staticPalette.mortar.tokenColorPrimaryWhite,
    );

    // Unselected not highlighted
    const emoticon1 = getByTestId(`${EmoticonRating.testIds.ICON}-1`);
    expect(emoticon1).toHaveProp('fill', staticPalette.mortar.tokenColorBlack);

    const emoticon2 = getByTestId(`${EmoticonRating.testIds.ICON}-2`);
    expect(emoticon2).toHaveProp('fill', staticPalette.mortar.tokenColorBlack);
  });
});
