import { View } from 'react-native';
import React, { ReactElement, useCallback } from 'react';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { NativeMortarIcon } from '@cat-home-experts/mortar-types';
import {
  EmoticonExcited,
  EmoticonFrown,
  EmoticonMeh,
  EmoticonSad,
  EmoticonSmile,
} from '@cat-home-experts/mortar-iconography-native';
import {
  createMortarStyles,
  createTestIds,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';
import { Typography } from '@cat-home-experts/react-native-components';

export type Rating = 1 | 2 | 3 | 4 | 5;
type Props = {
  value?: Rating;
  onChange: (rating: Rating | undefined) => void;
  testId?: string;
};

type RatingMapItem = {
  value: Rating;
  label?: string;
  icon: NativeMortarIcon;
};

const ratingMap: RatingMapItem[] = [
  {
    value: 1,
    label: 'Very unsatisfied',
    icon: EmoticonFrown,
  },
  {
    value: 2,
    icon: EmoticonSad,
  },
  {
    value: 3,
    label: 'Neutral',
    icon: EmoticonMeh,
  },
  {
    value: 4,
    icon: EmoticonSmile,
  },
  {
    value: 5,
    label: 'Very satisfied',
    icon: EmoticonExcited,
  },
];

const TEST_IDS = createTestIds('emoticon-rating', {
  ITEM: 'item',
  ICON: 'icon',
});

export const EmoticonRating = ({
  onChange,
  value,
  testId,
}: Props): ReactElement => {
  const onPress = useCallback(
    (rating: Rating) => () => {
      if (value === rating) {
        onChange(undefined);
        return;
      }

      onChange(rating);
    },
    [onChange, value],
  );
  return (
    <View style={styles.emoticonRating} testID={testId || TEST_IDS.ROOT}>
      <View style={styles.emoticonWrapper}>
        {ratingMap.map((item) => {
          const isSelected = value === item.value;
          const Icon = item.icon;
          return (
            <TouchableOpacity
              style={[styles.item, isSelected && styles.selectedItem]}
              key={item.value}
              onPress={onPress(item.value)}
              testID={`${TEST_IDS.ITEM}-${item.value}`}
            >
              <Icon
                testID={`${TEST_IDS.ICON}-${item.value}`}
                color={
                  isSelected
                    ? staticPalette.mortar.tokenColorPrimaryWhite
                    : staticPalette.mortar.tokenColorBlack
                }
                size={25}
              />
            </TouchableOpacity>
          );
        })}
      </View>
      <View style={styles.textWrapper}>
        <Typography use="bodySmall" style={styles.label}>
          {ratingMap[0].label}
        </Typography>
        <Typography use="bodySmall" style={[styles.label, styles.neutral]}>
          {ratingMap[2].label}
        </Typography>
        <Typography use="bodySmall" style={styles.label}>
          {ratingMap[4].label}
        </Typography>
      </View>
    </View>
  );
};

EmoticonRating.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => {
  // Getting the text alignment correct is fiddly due to RN's lack of white-space support, causing truncation.
  const itemWidth = spacing(6);
  const itemGap = spacing(1);
  const containerWidth = itemWidth * 5 + itemGap * 4;
  return {
    emoticonRating: {
      flexDirection: 'column',
      width: containerWidth,
    },
    emoticonWrapper: {
      gap: itemGap,
      flexDirection: 'row',
      width: containerWidth,
    },
    item: {
      height: itemWidth,
      width: itemWidth,
      borderRadius: spacing(1.5),
      backgroundColor: palette.mortar.tokenColorLighterGrey,
      alignItems: 'center',
      justifyContent: 'center',
    },
    selectedItem: {
      backgroundColor: palette.mortar.tokenColorDarkGrey,
    },
    textWrapper: {
      marginTop: spacing(1),
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    neutral: {
      position: 'absolute',
      left: 0,
      right: 0,
      textAlign: 'center',
    },
    label: {
      fontSize: 12,
    },
  };
});
