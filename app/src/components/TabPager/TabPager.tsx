import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  ReactElement,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  FlatList,
  type DimensionValue,
  type LayoutChangeEvent,
  type ViewStyle,
  type ViewToken,
  type ViewabilityConfig,
  View,
} from 'react-native';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { ObjectWithKey, RenderTabBarItemFnType } from './types';
import { TabBar } from './TabBar';

type Props<T> = {
  data: T[];
  renderTabBarItem: RenderTabBarItemFnType<T>;
  renderItem: (item: T) => ReactElement;
  testID?: string;
  containerStyle?: ViewStyle;
  keyExtractor?: (item: T) => string;
  padding?: number;
  cardHeight?: DimensionValue;
  maxWidth?: number;
  onActiveCardIndexChanged?: (index: number) => void;
  animateOnScroll?: boolean;
};

const handleExtractKey = <T extends ObjectWithKey>(item: T): string => {
  if (item && 'key' in item) {
    return String(item.key);
  }

  return '';
};

const viewabilityConfig: ViewabilityConfig = {
  itemVisiblePercentThreshold: 50,
};

/** forwardRef makes component not accept generics, this type is
 * a workaround to fix this.
 */
type WithForwardRefType = <T extends ObjectWithKey>(
  props: Props<T> & { ref?: React.Ref<FlatList<T>> },
) => React.ReactElement;

/**
 * TODO: This should be moved to the react-native-components library.
 */
export const TabPager = forwardRef(
  <T extends ObjectWithKey>(
    {
      testID,
      data,
      renderItem,
      renderTabBarItem,
      containerStyle,
      keyExtractor = handleExtractKey,
      maxWidth = 400,
      padding = 0,
      cardHeight = 450,
      onActiveCardIndexChanged,
      animateOnScroll = true,
    }: Props<T>,
    forwardedRef: React.Ref<FlatList<T>>,
  ): ReactElement => {
    const isDesktop = useDesktopMediaQuery();

    // State/Refs
    const flatListRef = useRef<FlatList<T>>(null);
    const [containerWidth, setContainerWidth] = useState(0);
    const [activeCardIndex, setActiveCardIndex] = useState(0);

    // Computed
    const itemWidth = useMemo(() => {
      const containerSize = containerWidth - padding * 2;
      return isDesktop ? Math.min(maxWidth, containerSize) : containerSize;
    }, [containerWidth, isDesktop, maxWidth, padding]);

    const baseItemStyle: ViewStyle = useMemo(
      () => ({
        height: cardHeight,
        width: itemWidth,
      }),
      [cardHeight, itemWidth],
    );

    // Callbacks
    const handleLayout = useCallback((event: LayoutChangeEvent) => {
      const { width } = event.nativeEvent.layout;
      if (width) {
        setContainerWidth(width);
      }
    }, []);

    const getItemLayout = useCallback(
      (_data: ArrayLike<T> | null | undefined, index: number) => ({
        length: itemWidth,
        offset: itemWidth * index,
        index,
      }),
      [itemWidth],
    );

    const onViewableItemsChanged = useCallback(
      ({ viewableItems }: { viewableItems: ViewToken[] }) => {
        if (viewableItems.length > 0) {
          const firstViewableIndex = viewableItems[0].index;
          if (firstViewableIndex !== null) {
            setActiveCardIndex(firstViewableIndex);
            onActiveCardIndexChanged?.(firstViewableIndex); // Optional callback.
          }
        }
      },
      [onActiveCardIndexChanged],
    );

    const handlePanTo = useCallback(
      (index: number) => {
        flatListRef.current?.scrollToIndex({
          index,
          animated: animateOnScroll,
          viewPosition: 0,
        });
      },
      [animateOnScroll],
    );

    useImperativeHandle(
      forwardedRef,
      () =>
        ({
          handlePanTo,
        }) as unknown as FlatList<T>,
    );

    const renderFlatListItem = useCallback(
      ({ item }: { item: T }) => (
        <View style={baseItemStyle}>{renderItem(item)}</View>
      ),
      [baseItemStyle, renderItem],
    );

    // ref
    const viewabilityConfigCallbackPairs = useRef([
      { viewabilityConfig, onViewableItemsChanged },
    ]);

    return (
      <>
        <TabBar
          onChangeTab={handlePanTo}
          routes={data}
          renderTabBarItem={renderTabBarItem}
          tabIndex={activeCardIndex}
        />
        <View
          testID={testID}
          style={[styles.container, containerStyle]}
          onLayout={handleLayout}
        >
          {containerWidth > 0 ? (
            <FlatList
              ref={flatListRef}
              data={data}
              renderItem={renderFlatListItem}
              keyExtractor={keyExtractor}
              initialScrollIndex={0}
              horizontal
              pagingEnabled={!isDesktop}
              decelerationRate="fast"
              showsHorizontalScrollIndicator={false}
              getItemLayout={getItemLayout}
              viewabilityConfigCallbackPairs={
                viewabilityConfigCallbackPairs.current
              }
              style={styles.flatList}
              contentContainerStyle={[
                styles.scrollViewContent,
                isDesktop && styles.scrollViewContentDesktop,
              ]}
            />
          ) : null}
        </View>
      </>
    );
  },
  // This is to fix generic types issue caused by forwardRef.
  // See: https://stackoverflow.com/a/73795494/10524906.
) as WithForwardRefType;

const styles = createMortarStyles(({ spacing }) => ({
  container: {
    width: '100%',
    paddingVertical: spacing(2),
    alignItems: 'center',
    overflow: 'hidden',
  },
  flatList: {
    width: '100%',
  },
  scrollViewContent: {
    alignItems: 'center',
  },
  scrollViewContentDesktop: {
    paddingHorizontal: spacing(6),
    gap: spacing(2),
  },
}));
