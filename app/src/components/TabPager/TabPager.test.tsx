import React from 'react';
import { cleanup, fireEvent, render } from '@testing-library/react-native';
import { Typography } from '@cat-home-experts/react-native-components';
import { TouchableOpacity } from 'react-native';
import { TabPager } from './TabPager';

const mockOnActiveCardIndexChanged = jest.fn();

const mockTabRoutes = [
  { key: 'tab1', title: 'Tab 1', content: 'Content for Tab 1' },
  { key: 'tab2', title: 'Tab 2', content: 'Content for Tab 2' },
];

describe('Components | TabPager', () => {
  afterEach(() => {
    jest.clearAllMocks();
    cleanup();
  });

  it('renders content correctly and allows tab switching', () => {
    const { getByText, getByTestId } = render(
      <TabPager
        testID="tab-pager"
        data={mockTabRoutes}
        renderItem={(item) => (
          <Typography key={item.key}>{item.content}</Typography>
        )}
        renderTabBarItem={(item, _, { onPress }) => (
          <TouchableOpacity
            testID={`tab-${item.key}`}
            onPress={onPress}
            key={item.key}
          >
            <Typography>{item.title}</Typography>
          </TouchableOpacity>
        )}
        cardHeight={400}
        animateOnScroll={false}
        onActiveCardIndexChanged={mockOnActiveCardIndexChanged}
        keyExtractor={(item) => item.key}
      />,
    );

    const container = getByTestId('tab-pager');
    fireEvent(container, 'layout', {
      nativeEvent: {
        layout: {
          width: 400,
          height: 800,
        },
      },
    });

    expect(getByText('Tab 1')).toBeOnTheScreen();
    expect(getByText('Content for Tab 1')).toBeOnTheScreen();

    fireEvent.press(getByText('Tab 2'));
    expect(getByText('Content for Tab 2')).toBeOnTheScreen();
  });
});
