import {
  createMortarStyles,
  createTestIds,
  spacing,
} from '@cat-home-experts/react-native-utilities';
import React, { ReactElement, useEffect, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { IS_WEB } from 'src/constants';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { RenderTabBarItemFnType } from './types';

type Props<T> = {
  routes: T[];
  tabIndex: number;
  onChangeTab: (index: number) => void;
  renderTabBarItem: RenderTabBarItemFnType<T>;
};

const TEST_IDS = createTestIds('tab-bar', {});

export const TabBar = <T,>({
  routes,
  tabIndex,
  onChangeTab,
  renderTabBarItem,
}: Props<T>): ReactElement => {
  const isDesktop = useDesktopMediaQuery();
  const [localActiveIndex, setLocalActiveIndex] = useState(tabIndex); // Separate state for quicker UI feedback on tab change.

  useEffect(() => {
    setLocalActiveIndex(tabIndex);
  }, [tabIndex]);

  const handleTabPress = (index: number) => {
    setLocalActiveIndex(index);
    onChangeTab(index);
  };

  return (
    <View>
      <ScrollView
        contentContainerStyle={[styles.tabBar, IS_WEB && styles.fullWidth]}
        horizontal={!isDesktop}
        showsHorizontalScrollIndicator={false}
      >
        {routes.map((route, index) =>
          renderTabBarItem(route, index, {
            onPress: () => handleTabPress(index),
            localActiveIndex,
          }),
        )}
      </ScrollView>
    </View>
  );
};

TabBar.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette }) => ({
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    borderBottomColor: palette.mortarV3.tokenNeutral200,
    borderBottomWidth: 2,
    paddingHorizontal: spacing(2),
  },
  fullWidth: {
    width: '100%',
  },
}));
