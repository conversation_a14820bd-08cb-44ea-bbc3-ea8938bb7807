/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import hoistNonReactStatics from 'hoist-non-react-statics';
import { ErrorBoundary, type ErrorBoundaryProps } from './ErrorBoundary';

export const UNKNOWN_COMPONENT = 'unknown';

/**
 * Statically wrap a component with an error boundary.
 *
 * DO NOT use within a react render method.
 * https://legacy.reactjs.org/docs/higher-order-components.html#dont-use-hocs-inside-the-render-method
 *
 * TODO: use with layout prop in react-navigation v7
 * https://github.com/react-navigation/react-navigation/discussions/11152
 * https://github.com/react-navigation/react-navigation/pull/11741
 *
 */
export function withErrorBoundary<P extends Record<string, any>>(
  WrappedComponent: React.ComponentType<P> | React.ComponentType<any>,
  errorBoundaryOptions: Partial<ErrorBoundaryProps> = {},
): React.FC<P> {
  const componentDisplayName =
    WrappedComponent.displayName || WrappedComponent.name || UNKNOWN_COMPONENT;

  const Wrapped: React.FC<P> = (props: P) => (
    <ErrorBoundary {...errorBoundaryOptions}>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  Wrapped.displayName = `errorBoundary(${componentDisplayName})`;

  // Copy over static methods from Wrapped component
  // See: https://reactjs.org/docs/higher-order-components.html#static-methods-must-be-copied-over
  hoistNonReactStatics(Wrapped, WrappedComponent);
  return Wrapped;
}
