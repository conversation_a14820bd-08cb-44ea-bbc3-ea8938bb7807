import React, { ReactElement, Component, ComponentType } from 'react';
import { captureException } from 'src/services/datadog';
import {
  FallbackComponent as DefaultFallbackComponent,
  type FallbackComponentProps,
} from './FallbackComponent';

type Primitive = number | string | boolean | bigint | symbol | null | undefined;
type Context = {
  extra?: Record<string, Primitive>;
  tags?: Record<string, Primitive>;
};
export interface ErrorBoundaryProps {
  children: ReactElement[] | ReactElement;
  FallbackComponent: ComponentType<FallbackComponentProps>;
  captureContext?: Context;
}

interface ErrorBoundaryState {
  error: Error | null;
}

export class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  static defaultProps: {
    FallbackComponent: ComponentType<FallbackComponentProps>;
  } = {
    FallbackComponent: DefaultFallbackComponent,
  };

  state = { error: null };

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { error };
  }

  componentDidCatch(error: Error): void {
    captureException(error, {
      ...this.props.captureContext,
      level: 'error',
    });
  }

  resetError: () => void = () => {
    this.setState({ error: null });
  };

  render(): ReactElement | ReactElement[] {
    const { FallbackComponent } = this.props;
    const { error } = this.state;
    return error ? (
      <FallbackComponent error={error} resetError={this.resetError} />
    ) : (
      this.props.children
    );
  }
}
