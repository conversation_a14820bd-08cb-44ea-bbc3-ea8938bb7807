import React, { ReactElement } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import ErrorImage from '../../assets/images/errors/man-with-broken-computer.svg';

const TEST_IDS = createTestIds('error-boundary', {
  RELOAD_BUTTON: 'reload-button',
});

export type FallbackComponentProps = {
  error: Error;
  resetError: () => void;
};

export const FallbackComponent = ({
  resetError,
}: FallbackComponentProps): ReactElement => (
  <SafeAreaView testID={TEST_IDS.ROOT} style={styles.container}>
    <ErrorImage />
    <Typography use="header">{'Something has gone wrong'}</Typography>
    <Typography use="bodyRegular">
      {'Please reload app and try again'}
    </Typography>
    <Button
      testID={TEST_IDS.RELOAD_BUTTON}
      label="Reload"
      variant="secondary"
      onPress={resetError}
      size="small"
      style={styles.button}
    />
  </SafeAreaView>
);

FallbackComponent.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  button: {
    marginTop: spacing(4),
  },
}));
