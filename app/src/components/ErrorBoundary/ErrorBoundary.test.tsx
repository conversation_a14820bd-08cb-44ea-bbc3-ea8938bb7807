import React from 'react';
import { View, Pressable } from 'react-native';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { ErrorBoundary } from './ErrorBoundary';
import { FallbackComponent as DefaultFallbackComponent } from './FallbackComponent';

const error = new Error('Error');

const ErrorComponent = () => {
  throw error;
};

describe('ErrorBoundary Component', () => {
  const fallbackTestId = 'fallback';
  let errorSpy: jest.SpyInstance<void, [Error]>;

  beforeEach(() => {
    jest.spyOn(console, 'error').mockImplementationOnce(jest.fn());
    errorSpy = jest
      .spyOn(ErrorBoundary.prototype, 'componentDidCatch')
      .mockImplementation(() => {
        return error;
      });
  });

  afterEach(() => {
    jest.resetAllMocks();
    jest.restoreAllMocks();
    cleanup();
  });

  it('should display a full screen default error message', () => {
    const { getByTestId } = render(
      <ErrorBoundary>
        <ErrorComponent />
      </ErrorBoundary>,
    );

    const errorElement = getByTestId(DefaultFallbackComponent.testIds.ROOT);

    expect(errorElement).toBeDefined();
    expect(errorSpy).toHaveBeenCalled();
  });

  it('should display a fallback component when one is available', () => {
    const FallbackComponent = () => <View testID={fallbackTestId} />;

    const { getByTestId } = render(
      <ErrorBoundary FallbackComponent={FallbackComponent}>
        <ErrorComponent />
      </ErrorBoundary>,
    );

    const errorElement = getByTestId(fallbackTestId);

    expect(errorElement).toBeDefined();
    expect(errorSpy).toHaveBeenCalled();
  });

  it('should catch error in closest ErrorBoundary', () => {
    const FallbackComponent = () => <View testID={fallbackTestId} />;
    const outerViewTestId = 'I-should-be-visible';
    const unaffectedOnPress = jest.fn();

    const { getByTestId, queryByTestId } = render(
      <ErrorBoundary>
        <Pressable testID={outerViewTestId} onPress={unaffectedOnPress} />
        <ErrorBoundary FallbackComponent={FallbackComponent}>
          <ErrorComponent />
        </ErrorBoundary>
      </ErrorBoundary>,
    );

    const unaffectedButtonElement = getByTestId(outerViewTestId);
    const childErrorBoundary = getByTestId(fallbackTestId);
    const parentErrorBoundary = queryByTestId(
      DefaultFallbackComponent.testIds.ROOT,
    );

    fireEvent.press(unaffectedButtonElement);

    expect(unaffectedOnPress).toBeCalledTimes(1);
    expect(childErrorBoundary).toBeDefined();
    expect(parentErrorBoundary).toBeNull();

    expect(errorSpy).toHaveBeenCalled();
  });
});
