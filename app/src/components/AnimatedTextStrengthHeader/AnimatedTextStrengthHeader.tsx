/* eslint-disable react-native/no-inline-styles */
import React, { ReactElement } from 'react';
import { StyleSheet, View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';

import { tokenLegacyColorBorderDisabled } from '@cat-home-experts/design-tokens/dist/colours/system/light/js/system';
import { tokenColorPrimaryWhite } from '@cat-home-experts/design-tokens/dist/colours/mortar/js/mortarV2';
import { TextStrengthBox } from '@cat-home-experts/react-native-components';

import { getTestID } from 'src/utilities/testIds';
import { getTextStrength } from 'src/utilities/text';
import {
  useMobileMediaQuery,
  useMobileSmallScreenMediaQuery,
} from 'src/hooks/useMediaQuery';
import { AnimatedTextStrengthHeaderProps } from './AnimatedTextStrengthHeader.types';
import {
  TEXT_STRENGTH_PROMPT,
  TEXT_STRENGTH_PROMPT_ANIMATION,
} from './constants';

const rootTestId = 'strength-indicator';

const testIds = {
  ROOT: rootTestId,
};

export const AnimatedTextStrengthHeader = ({
  idealCharacterLength,
  maxCharacterLength,
  textLength,
  isParentComponentFocused,
}: AnimatedTextStrengthHeaderProps): ReactElement => {
  const isMobileSmallScreen = useMobileSmallScreenMediaQuery();
  const isMobileScreen = useMobileMediaQuery();

  const textStrengthContainerAnimatedStyles = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: withTiming(
            isParentComponentFocused
              ? TEXT_STRENGTH_PROMPT_ANIMATION.WITH_TIMING_OFFSET_VALUE
              : TEXT_STRENGTH_PROMPT_ANIMATION.INITIAL_OFFSET_VALUE,
          ),
        },
      ],
    };
  }, [isParentComponentFocused]);

  return (
    <Animated.View
      style={[
        styles.strengthIndicatorContainer,
        { position: isMobileScreen ? 'absolute' : 'relative' },
        isMobileScreen && textStrengthContainerAnimatedStyles,
      ]}
    >
      <View
        style={[
          styles.strengthIndicator,
          { paddingHorizontal: isMobileSmallScreen ? 16 : 24 },
          { paddingVertical: isMobileSmallScreen ? 12 : 16 },
        ]}
      >
        <TextStrengthBox
          textStrength={getTextStrength(
            textLength,
            idealCharacterLength,
            maxCharacterLength,
          )}
          emptyPrompt={TEXT_STRENGTH_PROMPT.EMPTY}
          weakestPrompt={TEXT_STRENGTH_PROMPT.WEAKEST}
          weakPrompt={TEXT_STRENGTH_PROMPT.WEAK}
          strongPrompt={TEXT_STRENGTH_PROMPT.STRONG}
          maximumPrompt={TEXT_STRENGTH_PROMPT.MAXIMUM}
          testID={getTestID(testIds.ROOT)}
        />
      </View>
      <View style={styles.horizontalLine} />
    </Animated.View>
  );
};

AnimatedTextStrengthHeader.testIds = testIds;

const styles = StyleSheet.create({
  horizontalLine: {
    borderTopColor: tokenLegacyColorBorderDisabled,
    borderTopWidth: 1,
  },
  strengthIndicator: {
    alignSelf: 'center',
    maxWidth: 800,
    width: '100%',
  },
  strengthIndicatorContainer: {
    width: '100%',
    zIndex: 1,
    backgroundColor: tokenColorPrimaryWhite,
  },
});
