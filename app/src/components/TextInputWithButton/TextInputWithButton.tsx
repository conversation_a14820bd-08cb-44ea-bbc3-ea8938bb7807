import React from 'react';
import { TextInput, View } from 'react-native';
import {
  Button,
  type ButtonVariants,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';

interface TextInputWithButtonProps {
  text: string;
  onButtonPress: () => void;
  buttonVariant?: ButtonVariants;
  buttonDisabled?: boolean;
  editable?: boolean;
}

export const TEST_IDS = createTestIds('text-input-with-button', {
  TEXT_INPUT: 'text-input',
  BUTTON: 'button',
});

export const TextInputWithButton: React.NativeFC<
  TextInputWithButtonProps,
  typeof TEST_IDS
> = ({ text, onButtonPress, editable, buttonVariant, buttonDisabled }) => {
  const isDesktop = useDesktopMediaQuery();

  return (
    <View
      style={[styles.wrapper, isDesktop && styles.whiteBackground]}
      testID={TEST_IDS.ROOT}
    >
      <View style={styles.textInputWrapper}>
        <TextInput
          onChangeText={() => null}
          style={styles.textInput}
          onSubmitEditing={() => null}
          value={text}
          editable={editable}
          testID={TEST_IDS.TEXT_INPUT}
        />
      </View>
      <Button
        onPress={onButtonPress}
        label="Copy"
        isDisabled={buttonDisabled}
        variant={buttonVariant}
        testID={TEST_IDS.BUTTON}
      />
    </View>
  );
};

TextInputWithButton.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing(1),
    borderWidth: 1,
    borderColor: palette.mortar.tokenColorLightGrey,
    borderRadius: spacing(0.5),
    minHeight: spacing(7),
  },
  textInputWrapper: {
    paddingHorizontal: spacing(1),
    flex: 1,
    minWidth: spacing(25),
  },
  textInput: { color: palette.mortar.tokenColorBlueGrey },
  whiteBackground: { backgroundColor: palette.mortar.tokenColorPrimaryWhite },
}));
