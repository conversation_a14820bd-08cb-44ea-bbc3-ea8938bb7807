import React from 'react';
import { cleanup, fireEvent, render } from '@testing-library/react-native';
import { TextInputWithButton } from './TextInputWithButton';

const TEST_PROPS = { text: 'test string', editable: false };

describe('RequestReview | Components | TextInputWithButton', () => {
  afterEach(cleanup);

  it('shows text props in TextInput', () => {
    // Act
    const { getByTestId } = render(
      <TextInputWithButton {...TEST_PROPS} onButtonPress={jest.fn()} />,
    );
    const input = getByTestId(TextInputWithButton.testIds.TEXT_INPUT);

    // Assert
    expect(input.props.value).toBe(TEST_PROPS.text);
  });

  it('calls onButtonPress when button is pressed', () => {
    // Act
    const onButtonPress = jest.fn();
    const { getByTestId } = render(
      <TextInputWithButton {...TEST_PROPS} onButtonPress={onButtonPress} />,
    );
    const button = getByTestId(TextInputWithButton.testIds.BUTTON);
    fireEvent.press(button);

    // Assert
    expect(onButtonPress).toHaveBeenCalled();
  });

  it('does not allow text change if editable is false', () => {
    // Act
    const onButtonPress = jest.fn();
    const { getByTestId } = render(
      <TextInputWithButton {...TEST_PROPS} onButtonPress={onButtonPress} />,
    );
    const input = getByTestId(TextInputWithButton.testIds.TEXT_INPUT);
    fireEvent.changeText(input, 'new text');

    // Assert
    expect(input.props.value).toBe(TEST_PROPS.text);
  });
});
