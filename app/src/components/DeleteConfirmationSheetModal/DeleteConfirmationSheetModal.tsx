import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Icon, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { IS_WEB } from 'src/constants';
import { SheetOrModal } from 'src/components/SheetOrModal';
import { NO_BUTTON_TEXT, YES_BUTTON_TEXT } from './constants';

interface DeleteConfirmationSheetModalProps {
  title: string;
  subtitle: string;
  presented: boolean;
  onDelete: () => void;
  onDismiss: () => void;
  yesButtonText?: string;
  noButtonText?: string;
}

const TEST_IDS = createTestIds('delete-confirmation-sheet-modal', {
  TITLE: 'title',
  SUBTITLE: 'subtitle',
  YES_BUTTON: 'yes-button',
  NO_BUTTON: 'no-button',
});

export const DeleteConfirmationSheetModal: React.NativeFC<
  DeleteConfirmationSheetModalProps,
  typeof TEST_IDS
> = ({
  title,
  subtitle,
  presented,
  onDelete,
  onDismiss,
  yesButtonText = YES_BUTTON_TEXT,
  noButtonText = NO_BUTTON_TEXT,
}) => {
  // Computed Values
  const { bottom } = useSafeAreaInsets();
  const nativeOffset = !IS_WEB ? bottom : 0;

  // Methods
  const handleDismiss = () => onDismiss();
  const handleDelete = () => {
    onDelete();
    onDismiss();
  };

  return (
    <SheetOrModal
      visible={presented}
      style={[styles.modal, IS_WEB && styles.webRadii]}
      onDismiss={onDismiss}
    >
      <View
        style={[styles.root, styles.webRadii, { paddingBottom: nativeOffset }]}
      >
        <View style={styles.textContent}>
          <Icon
            name="warning-circle-fill"
            color={staticPalette.mortar.tokenColorSystemOrange}
            size={48}
          />
          <View style={styles.blurb}>
            <Typography
              useVariant="subHeader"
              style={[styles.center, styles.darkText]}
              testID={TEST_IDS.TITLE}
            >
              {title}
            </Typography>
            <Typography
              useVariant="bodyRegular"
              style={[styles.center, styles.darkText]}
              testID={TEST_IDS.SUBTITLE}
            >
              {subtitle}
            </Typography>
          </View>
        </View>
        <View style={styles.divider} />
        <View>
          <TouchableOpacity
            testID={TEST_IDS.YES_BUTTON}
            onPress={handleDelete}
            style={styles.button}
          >
            <Typography useVariant="bodySemiBold" style={styles.link}>
              {yesButtonText}
            </Typography>
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity
            testID={TEST_IDS.NO_BUTTON}
            onPress={handleDismiss}
            style={styles.button}
          >
            <Typography useVariant="bodySemiBold" style={styles.darkText}>
              {noButtonText}
            </Typography>
          </TouchableOpacity>
        </View>
      </View>
    </SheetOrModal>
  );
};

DeleteConfirmationSheetModal.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  modal: { padding: 0 },
  root: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    width: '100%',
  },
  webRadii: { borderRadius: spacing(1) },
  textContent: {
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(2),
    gap: spacing(2),
    alignItems: 'center',
  },
  blurb: {
    paddingBottom: spacing(1),
    gap: spacing(1),
  },
  link: { color: palette.mortar.tokenColorSystemLinkBlue },
  divider: {
    height: 1,
    width: '100%',
    backgroundColor: palette.mortar.tokenColorLighterGrey,
  },
  button: { padding: spacing(2), alignItems: 'center' },
  center: { textAlign: 'center' },
  darkText: { color: palette.mortar.tokenColorBlack },
}));
