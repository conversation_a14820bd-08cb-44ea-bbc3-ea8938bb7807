import React from 'react';
import { fireEvent, render, cleanup } from '@testing-library/react-native';
import { DeleteConfirmationSheetModal } from 'src/components/DeleteConfirmationSheetModal';

jest.mock('@gorhom/bottom-sheet', () =>
  jest.requireActual('@gorhom/bottom-sheet/mock'),
);

const TEST_STRINGS = {
  TITLE: 'title',
  SUBTITLE: 'subtitle',
};

describe('Screens | Accreditations | Components | AccreditationDeleteModal', () => {
  afterEach(cleanup);

  it('Should render the correct text', () => {
    // Act
    const { getByTestId } = render(
      <DeleteConfirmationSheetModal
        title={TEST_STRINGS.TITLE}
        subtitle={TEST_STRINGS.SUBTITLE}
        presented={true}
        onDismiss={jest.fn()}
        onDelete={jest.fn()}
      />,
    );
    const title = getByTestId(DeleteConfirmationSheetModal.testIds.TITLE);
    const subtitle = getByTestId(DeleteConfirmationSheetModal.testIds.SUBTITLE);

    // Assert
    expect(title).toHaveTextContent(TEST_STRINGS.TITLE);
    expect(subtitle).toHaveTextContent(TEST_STRINGS.SUBTITLE);
  });

  it('Should dismiss modal when clicking no button', () => {
    // Arrange
    const onDismiss = jest.fn();

    // Act
    const { getByTestId } = render(
      <DeleteConfirmationSheetModal
        title={TEST_STRINGS.TITLE}
        subtitle={TEST_STRINGS.SUBTITLE}
        presented={true}
        onDismiss={onDismiss}
        onDelete={jest.fn()}
      />,
    );
    const noButton = getByTestId(
      DeleteConfirmationSheetModal.testIds.NO_BUTTON,
    );

    fireEvent.press(noButton);

    // Assert
    expect(onDismiss).toHaveBeenCalled();
  });

  it('Should dismiss modal and call onDelete when clicking yes button', () => {
    // Arrange
    const onDismiss = jest.fn();
    const onDelete = jest.fn();

    // Act
    const { getByTestId } = render(
      <DeleteConfirmationSheetModal
        title={TEST_STRINGS.TITLE}
        subtitle={TEST_STRINGS.SUBTITLE}
        presented={true}
        onDismiss={onDismiss}
        onDelete={onDelete}
      />,
    );
    const yesButton = getByTestId(
      DeleteConfirmationSheetModal.testIds.YES_BUTTON,
    );

    fireEvent.press(yesButton);

    // Assert
    expect(onDismiss).toHaveBeenCalled();
    expect(onDelete).toHaveBeenCalled();
  });
});
