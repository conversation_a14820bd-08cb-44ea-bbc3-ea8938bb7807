import {
  NewTypographyVariants,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  palette,
  spacing,
} from '@cat-home-experts/react-native-utilities';
import React, { ReactElement } from 'react';
import { View } from 'react-native';

type ChipProps = {
  label: string;
  typographyVariant?: NewTypographyVariants;
  backgroundColor?: string;
  textColour?: string;
  size?: 'md' | 'sm';
};

/**
 * TODO: This should be moved to the react-native-components library.
 */
export const Chip = ({
  label,
  typographyVariant = 'labelSemiBold',
  backgroundColor = palette.mortar.tokenColorPrimaryRed,
  textColour = 'white',
  size = 'md',
}: ChipProps): ReactElement => {
  const bgColourStyles = {
    backgroundColor: backgroundColor,
  };

  const textColourStyles = {
    color: textColour,
  };

  return (
    <View
      style={[
        styles.container,
        bgColourStyles,
        size === 'sm' ? styles.paddingSmall : styles.paddingMedium,
      ]}
    >
      <Typography style={textColourStyles} useVariant={typographyVariant}>
        {label}
      </Typography>
    </View>
  );
};

const styles = createMortarStyles(() => ({
  container: {
    alignSelf: 'flex-start',
    borderRadius: spacing(0.5),
    marginVertical: 'auto',
  },
  paddingSmall: {
    paddingHorizontal: spacing(0.5),
    paddingVertical: spacing(0.25),
  },
  paddingMedium: {
    paddingHorizontal: spacing(1),
    paddingVertical: spacing(0.5),
  },
}));
