import React from 'react';
import { fireEvent, render, cleanup } from '@testing-library/react-native';
import { INPUT_FIELD_TEST_IDS } from '@cat-home-experts/react-native-components/src/ui/InputField/constants';
import { MultiItemInput } from './MultiItemInput';

describe('MultiItemInput', () => {
  const mockOnChange = jest.fn();
  const mockOnAddItem = jest.fn();
  const mockOnRemoveItem = jest.fn();

  const defaultProps = {
    items: [],
    onChange: mockOnChange,
    onAddItem: mockOnAddItem,
    onRemoveItem: mockOnRemoveItem,
  };

  beforeEach(() => {
    mockOnChange.mockClear();
    mockOnAddItem.mockClear();
    mockOnRemoveItem.mockClear();
  });

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    const { getByTestId, getByText } = render(
      <MultiItemInput {...defaultProps} />,
    );

    expect(getByTestId(MultiItemInput.testIds.ROOT)).toBeOnTheScreen();
    expect(getByText('Add an item (optional)')).toBeOnTheScreen();
  });

  it('renders items correctly', () => {
    const items = ['Item 1', 'Item 2', 'Item 3'];
    const { getAllByTestId } = render(
      <MultiItemInput {...defaultProps} items={items} />,
    );

    const inputElements = getAllByTestId(/input-\d+/);
    expect(inputElements.length).toBe(3);
  });

  it('calls onAddItem when add button is pressed', () => {
    const { getByTestId } = render(<MultiItemInput {...defaultProps} />);

    const addButton = getByTestId(MultiItemInput.testIds.ADD_ITEM_BUTTON);
    fireEvent.press(addButton);

    expect(mockOnAddItem).toHaveBeenCalledTimes(1);
  });

  it('calls onChange when item text is changed', () => {
    const items = ['Item 1'];
    const { getByTestId } = render(
      <MultiItemInput {...defaultProps} items={items} />,
    );

    const input = getByTestId(`${MultiItemInput.testIds.INPUT}-0`);
    fireEvent.changeText(input, 'Updated Item 1');

    expect(mockOnChange).toHaveBeenCalledWith(0, 'Updated Item 1');
  });

  it('calls onRemoveItem when remove icon is pressed', async () => {
    const items = ['Item 1'];
    const { getByTestId } = render(
      <MultiItemInput {...defaultProps} items={items} />,
    );

    const addButton = getByTestId(MultiItemInput.testIds.ADD_ITEM_BUTTON);
    fireEvent.press(addButton);

    const removeIcon = getByTestId(INPUT_FIELD_TEST_IDS.RIGHT_ICON);
    fireEvent.press(removeIcon);

    expect(mockOnRemoveItem).toHaveBeenCalledWith(0);
  });

  it('shows "Add new item" text when items exist', () => {
    const items = ['Item 1'];
    const { getByText } = render(
      <MultiItemInput {...defaultProps} items={items} />,
    );

    expect(getByText('Add new item')).toBeOnTheScreen();
  });

  it('shows "Add an item (optional)" text when no items exist', () => {
    const { getByText } = render(<MultiItemInput {...defaultProps} />);

    expect(getByText('Add an item (optional)')).toBeOnTheScreen();
  });

  it('uses custom labels when provided', () => {
    const customProps = {
      ...defaultProps,
      addItemLabel: 'Custom add item',
      addNewItemLabel: 'Custom add new',
      itemPlaceholder: 'Custom placeholder',
    };

    const { getByText } = render(<MultiItemInput {...customProps} />);

    expect(getByText('Custom add item')).toBeOnTheScreen();

    // Add an item to test the addNewItemLabel
    fireEvent.press(getByText('Custom add item'));
    expect(mockOnAddItem).toHaveBeenCalledTimes(1);

    // Re-render with an item to see the new label
    cleanup();
    const { getByText: getTextAfterAdd } = render(
      <MultiItemInput {...customProps} items={['Test']} />,
    );

    expect(getTextAfterAdd('Custom add new')).toBeOnTheScreen();
  });
});
