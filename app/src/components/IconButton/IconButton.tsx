import React, { ReactElement, useMemo } from 'react';
import { FlexStyle, TextStyle } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { NativeMortarIcon } from '@cat-home-experts/mortar-types';
import { Typography } from '@cat-home-experts/react-native-components';
import {
  adjustMortarIconMargins,
  createMortarStyles,
  createTestIds,
  isTruthy,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';

type IconButtonSizes = 'small' | 'default';

interface IconButtonProps {
  label: string;
  mortarIcon?: NativeMortarIcon;
  onPress?: () => void;
  size?: IconButtonSizes;
  testID?: string;
}

const TEST_IDS = createTestIds('icon-button', {
  ICON: 'icon',
  LABEL: 'label',
});

interface IconButtonSizeStyle {
  buttonStyle: FlexStyle;
  textStyle: TextStyle;
}
const setIconButtonSizeStyles = (
  size: IconButtonSizes,
): IconButtonSizeStyle => {
  switch (size) {
    case 'small':
      return {
        buttonStyle: styles.buttonSmall,
        textStyle: styles.buttonTextSmall,
      };
    default:
      return {
        buttonStyle: styles.buttonDefault,
        textStyle: styles.buttonTextDefault,
      };
  }
};

export const IconButton = ({
  mortarIcon: MortarIcon,
  onPress,
  label,
  size = 'default',
  testID,
}: IconButtonProps): ReactElement => {
  const { buttonStyle, textStyle } = useMemo(
    () => setIconButtonSizeStyles(size),
    [size],
  );

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.button, buttonStyle]}
      testID={testID}
    >
      {isTruthy(MortarIcon) && (
        <MortarIcon
          size={16}
          color={staticPalette.mortarV3.tokenNeutral900}
          testID={TEST_IDS.ICON}
          style={styles.icon}
        />
      )}
      <Typography use="bodySmall" style={textStyle} testID={TEST_IDS.LABEL}>
        {label}
      </Typography>
    </TouchableOpacity>
  );
};

IconButton.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => {
  return {
    button: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: spacing(1.5),
      gap: spacing(1.25),
      borderRadius: spacing(2),
      borderWidth: 1,
      borderColor: palette.mortar.tokenColorLightGrey,
      flexShrink: 1,
    },
    buttonDefault: {
      paddingVertical: spacing(1),
    },
    buttonTextDefault: {
      fontSize: 14,
      lineHeight: spacing(2.5) - 2,
    },
    buttonSmall: {
      paddingVertical: spacing(0.75),
      alignSelf: 'flex-start',
    },
    buttonTextSmall: {
      fontSize: 12,
      lineHeight: spacing(2) - 1.5,
    },
    icon: {
      marginHorizontal: adjustMortarIconMargins(16),
    },
  };
});
