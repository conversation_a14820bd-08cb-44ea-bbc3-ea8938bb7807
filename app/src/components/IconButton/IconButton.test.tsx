import React from 'react';
import { render, cleanup, fireEvent } from '@testing-library/react-native';
import { Briefcase } from '@cat-home-experts/mortar-iconography-native';
import { IconButton } from './IconButton';

describe('Components | IconButton', () => {
  const labelText = 'Testing 123';
  const mortarIcon = Briefcase;

  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('renders', () => {
    const { getByTestId } = render(
      <IconButton label={labelText} mortarIcon={mortarIcon} />,
    );

    const label = getByTestId(IconButton.testIds.LABEL);
    const iIcon = getByTestId(IconButton.testIds.ICON);

    expect(label).toHaveTextContent(labelText);
    expect(iIcon).toBeDefined();
  });

  it('fires press event', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <IconButton
        label={labelText}
        mortarIcon={mortarIcon}
        onPress={onPress}
        testID={IconButton.testIds.ROOT}
      />,
    );

    const button = getByTestId(IconButton.testIds.ROOT);
    fireEvent.press(button);
    expect(onPress).toHaveBeenCalledTimes(1);
  });
});
