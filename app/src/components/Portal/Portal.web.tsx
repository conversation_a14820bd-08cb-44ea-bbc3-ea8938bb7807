import * as React from 'react';
import { createPortal } from 'react-dom';

type PortalProps = {
  children: React.ReactNode;
};

export const Portal: React.FC<PortalProps> = ({ children }) => {
  // Refs
  const ref = React.useRef<HTMLElement>();

  // State
  const [mounted, setMounted] = React.useState(false);

  // Effects
  React.useEffect(() => {
    const element = document.querySelector<HTMLElement>('body');
    if (!element) {
      return;
    }

    ref.current = element;
    setMounted(true);
  }, []);

  return mounted && ref.current ? createPortal(children, ref.current) : null;
};
