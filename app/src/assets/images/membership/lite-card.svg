<svg width="327" height="216" viewBox="0 0 327 216" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_12754:108069)">
<g filter="url(#filter0_d_12754:108069)">
<rect width="327" height="216" rx="5" fill="#FEFEFE"/>
<rect x="0.5" y="0.5" width="326" height="215" rx="4.5" stroke="#F5F5F5"/>
</g>
<mask id="mask0_12754:108069" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="327" height="216">
<rect x="0.5" y="0.5" width="326" height="215" rx="4.5" fill="#FEFEFE" stroke="#F5F5F5"/>
</mask>
<g mask="url(#mask0_12754:108069)">
<circle cx="299.5" cy="-14.5" r="156.5" fill="#0058A2"/>
</g>
</g>
<defs>
<filter id="filter0_d_12754:108069" x="-20" y="-16" width="367" height="256" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.434896 0 0 0 0 0.53375 0 0 0 0 0.625 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12754:108069"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12754:108069" result="shape"/>
</filter>
<clipPath id="clip0_12754:108069">
<rect width="327" height="216" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>
