import React, { createContext, useState, useEffect } from 'react';
import NetInfo from '@react-native-community/netinfo';

type Context = {
  isOnline: boolean | null;
};

export const NetworkInfoContext = createContext<Context>({
  isOnline: null,
});

export const NetworkInfoContextProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [isOnline, setIsOnline] = useState<boolean | null>(null);
  useEffect(() => {
    // useNetInfo hook doesn't work correctly, so using the listener instead
    const unsubscribe = NetInfo.addEventListener(
      ({ isConnected, isInternetReachable }) =>
        setIsOnline(Boolean(isConnected && isInternetReachable)),
    );
    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <NetworkInfoContext.Provider value={{ isOnline }}>
      {children}
    </NetworkInfoContext.Provider>
  );
};
