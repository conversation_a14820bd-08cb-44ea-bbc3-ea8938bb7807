import { useSet<PERSON>tom } from 'jotai';
import React, {
  createContext,
  useState,
  ReactElement,
  useCallback,
  useContext,
  useEffect,
} from 'react';
import { useAppStateEffect } from 'src/hooks/useAppStateEffect';
import { fetchRemoteConfig } from 'src/services/firebase/remoteConfig';
import { updateLabsConfigAtom } from 'src/state/lab/atoms/labAtoms';

type Props = {
  children: ReactElement;
};

type Context = {
  configLastFetchedAt?: Date;
};

export const RemoteConfigContext = createContext<Context>({
  configLastFetchedAt: undefined,
});

export function RemoteConfigContextProvider({ children }: Props): ReactElement {
  const [configLastFetchedAt, setConfigLastFetchedAt] = useState<Date>();
  const updateLabsConfig = useSetAtom(updateLabsConfigAtom);

  useAppStateEffect(
    useCallback(() => {
      fetchRemoteConfig()
        .then(() => setConfigLastFetchedAt(new Date()))
        .catch(() => setConfigLastFetchedAt(new Date()));
    }, []),
  );

  useEffect(() => {
    if (configLastFetchedAt) {
      // merge local labs config with remote values
      updateLabsConfig();
    }
  }, [configLastFetchedAt, updateLabsConfig]);

  return (
    <RemoteConfigContext.Provider value={{ configLastFetchedAt }}>
      {children}
    </RemoteConfigContext.Provider>
  );
}

export function useRemoteConfigContext(): Context {
  return useContext(RemoteConfigContext);
}
