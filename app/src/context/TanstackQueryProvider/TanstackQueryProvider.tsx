import React from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  DefaultOptions,
  defaultShouldDehydrateQuery,
  QueryClient,
} from '@tanstack/react-query';
import {
  PersistQueryClientProvider,
  PersistQueryClientProviderProps,
} from '@tanstack/react-query-persist-client';
import { createAsyncStoragePersister } from '@tanstack/query-async-storage-persister';
import { setupTanstackNetworkListener } from 'src/utilities/tanstack-query';

setupTanstackNetworkListener();

/**
 * Tanstack Query Options
 */
const DEFAULT_TANSTACK_QUERY_OPTIONS: DefaultOptions = {
  queries: {
    gcTime: 1000 * 60 * 5, // 5 minutes
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  },
};

// Setup tanstack-query
const queryClient = new QueryClient({
  defaultOptions: DEFAULT_TANSTACK_QUERY_OPTIONS,
});

const asyncStoragePersister = createAsyncStoragePersister({
  key: 'TS_QUERY_OFFLINE_CACHE',
  storage: AsyncStorage,
  throttleTime: 1000 * 15, // 15 seconds
});

const persistOptions: PersistQueryClientProviderProps['persistOptions'] = {
  persister: asyncStoragePersister,
  maxAge: 1000 * 60 * 60 * 24 * 3, // 3 days
  buster: '0195d798-9a39-7c48-90fa-714a4ae0cb1d', // change this to wipe cache
  dehydrateOptions: {
    shouldDehydrateMutation: () => false,
    shouldDehydrateQuery: (query) =>
      defaultShouldDehydrateQuery(query) &&
      query.options.networkMode === 'offlineFirst',
  },
};

export const TanstackQueryProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => (
  <PersistQueryClientProvider
    client={queryClient}
    persistOptions={persistOptions}
  >
    {children}
  </PersistQueryClientProvider>
);
