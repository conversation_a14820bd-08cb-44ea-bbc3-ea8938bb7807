import React, {
  createContext,
  useState,
  ReactElement,
  Dispatch,
  SetStateAction,
} from 'react';

import type { CompanyType } from 'src/data/schemas/firestore/company';
import type { CompaniesType as CompanyV2 } from 'src/data/schemas/firestore/companies';

export type CompanyContextType = {
  company?: CompanyType;
  companyV2?: CompanyV2;
  setCompany: Dispatch<SetStateAction<CompanyType | undefined>>;
  setCompanyV2: Dispatch<SetStateAction<CompanyV2 | undefined>>;
};

export const CompanyContext = createContext<CompanyContextType>({
  company: undefined,
  companyV2: undefined,
  setCompany: () => null,
  setCompanyV2: () => null,
});

interface Props extends React.PropsWithChildren {
  company?: CompanyType;
}

export function CompanyContextProvider({
  company: companyArg,
  children,
}: Props): ReactElement {
  const [company, setCompany] = useState<CompanyType | undefined>(companyArg);
  const [companyV2, setCompanyV2] = useState<CompanyV2 | undefined>();

  return (
    <CompanyContext.Provider
      value={{
        company,
        companyV2,
        setCompany,
        setCompanyV2,
      }}
    >
      {children}
    </CompanyContext.Provider>
  );
}
