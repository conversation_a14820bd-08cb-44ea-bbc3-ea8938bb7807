import { differenceInMinutes } from 'date-fns';
import {
  DefaultStreamChatGenerics,
  MessageActionsParams,
  MessageActionType,
} from 'stream-chat-expo';

export const messageActions = <
  StreamChatGenerics extends
    DefaultStreamChatGenerics = DefaultStreamChatGenerics,
>({
  banUser,
  copyMessage,
  deleteMessage,
  editMessage,
  error,
  flagMessage,
  isMyMessage,
  isThreadMessage,
  message,
  ownCapabilities,
  pinMessage,
  quotedReply,
  retry,
  unpinMessage,
  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
}: MessageActionsParams<StreamChatGenerics>) => {
  const actions: Array<MessageActionType> = [];

  const minutesSinceSent = message.created_at
    ? differenceInMinutes(new Date(), message.created_at)
    : 0;

  if (error && isMyMessage) {
    actions.push(retry);
  }

  if (ownCapabilities.quoteMessage && !isThreadMessage && !error) {
    actions.push(quotedReply);
  }

  const canUpdateMessage =
    (isMyMessage && ownCapabilities.updateOwnMessage) ||
    (!isMyMessage && ownCapabilities.updateAnyMessage);

  if (canUpdateMessage && minutesSinceSent <= 15) {
    actions.push(editMessage);
  }

  if (copyMessage !== undefined && message.text && !error) {
    actions.push(copyMessage);
  }

  if (!isMyMessage && ownCapabilities.flagMessage) {
    actions.push(flagMessage);
  }

  if (ownCapabilities.pinMessage && !message.pinned) {
    actions.push(pinMessage);
  }

  if (ownCapabilities.pinMessage && message.pinned) {
    actions.push(unpinMessage);
  }

  if (!isMyMessage && ownCapabilities.banChannelMembers) {
    actions.push(banUser);
  }

  const canDeleteMessage =
    (isMyMessage && ownCapabilities.deleteOwnMessage) ||
    (!isMyMessage && ownCapabilities.deleteAnyMessage);

  if (canDeleteMessage && minutesSinceSent <= 15) {
    actions.push(deleteMessage);
  }

  return actions;
};
