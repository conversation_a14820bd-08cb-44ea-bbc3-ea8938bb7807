import React, { type PropsWithChildren } from 'react';
import { View } from 'react-native';
import { cleanup, render } from '@testing-library/react-native';
import { noop } from 'lodash';
import { StreamProvider } from 'src/context/StreamProvider';
import { QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from 'src/utilities/tanstack-query/tanstack-test-utils';

jest.mock('stream-chat', () =>
  jest.requireActual('src/__mocks__/src/context/StreamProvider'),
);

jest.mock('src/services/stream/hooks/useStreamUserConnection', () => ({
  useStreamUserConnection: jest.fn(),
}));

jest.mock('src/config', () => ({
  config: { streamApiKey: 'STREAM_API_KEY' },
}));

jest.mock('src/services/sentry', () => ({
  captureException: jest.fn(),
}));

jest.mock('src/data/api/trade-app-bff', () => {
  return { tradeAppBff: { authChat: { getChatToken: jest.fn() } } };
});

jest.mock('stream-chat-expo', () => ({
  Chat: ({ children }: PropsWithChildren) => <>{children}</>,
  OverlayProvider: ({ children }: PropsWithChildren) => <>{children}</>,
}));

jest.mock('src/hooks/useUser', () => ({
  useUserContext: () => ({
    companyId: '12345',
  }),
}));

jest.mock('src/hooks/useCompany', () => ({
  useCompanyContext: () => ({
    company: { name: 'Some great company' },
    companyV2: {
      companyId: '12345',
      details: {
        logo: { url: 'https://picsum.photos/200' },
      },
    },
  }),
}));

describe('Context | StreamProvider', () => {
  // Arrange
  const client = createQueryClient();
  const wrapper: React.FC<PropsWithChildren> = ({ children }) => (
    <QueryClientProvider client={client}>
      <StreamProvider>{children}</StreamProvider>
    </QueryClientProvider>
  );

  afterEach(cleanup);

  it('Passes through any children', async () => {
    // Arrange
    const TestView = () => <View testID="TEST_VIEW" />;
    jest.spyOn(console, 'error').mockImplementation(noop); // Stop jest complaining about the effect

    // Act
    const { getByTestId } = render(<TestView />, { wrapper });
    const testView = getByTestId('TEST_VIEW');

    // Assert
    expect(testView).toBeDefined();
  });
});
