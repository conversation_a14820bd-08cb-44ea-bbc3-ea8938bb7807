import React, { ReactElement, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { renderHook, waitFor, cleanup } from '@testing-library/react-native';
import { v4 as uuidv4 } from 'uuid';
import { sub } from 'date-fns';
import {
  STORAGE_KEY_BACKGROUND_DATE_TIME,
  STORAGE_KEY_SESSION_COUNT,
  STORAGE_KEY_SESSION_ID,
} from 'src/constants';
import { useAppStateEffect as mockUseAppStateEffect } from 'src/hooks/useAppStateEffect';
import { AppContext } from './AppContext';
import { AppContextProvider } from './AppContextProvider';
import * as AppContextStorage from './AppContextStorage';

jest.mock('src/hooks/useAppStateEffect', () => ({
  useAppStateEffect: jest.fn(),
}));

const now = new Date();
const wrapper = ({ children }: { children: ReactElement }) => (
  <AppContextProvider>{children}</AppContextProvider>
);

describe('AppContextProvider', () => {
  afterEach(async () => {
    cleanup();
    await AsyncStorage.clear();
    AppContextStorage.clear();
    jest.clearAllMocks();
    jest.resetAllMocks();
    jest.restoreAllMocks();
  });

  it('should grab sessionCount if available in asyncStorage', async () => {
    const existingSessionCount = '2';

    AppContextStorage.setItem(STORAGE_KEY_SESSION_COUNT, existingSessionCount);

    const { result } = renderHook(() => useContext(AppContext), { wrapper });

    await waitFor(() => expect(result.current?.sessionCount).toBeDefined());

    expect(result.current.sessionCount).toEqual(
      parseInt(existingSessionCount, 10),
    );
  });

  it('should be initialised with default values', async () => {
    const { result } = renderHook(() => useContext(AppContext), { wrapper });

    expect(result.current?.sessionCount).toBeUndefined();
    expect(result.current?.sessionId).toBeUndefined();

    await waitFor(() => expect(result.current?.sessionCount).toBeDefined());
  });

  it('should initialise with previous session if < 30mins', async () => {
    const backgroundTimeDate = sub(now, { minutes: 20 });
    AppContextStorage.setItem(
      STORAGE_KEY_BACKGROUND_DATE_TIME,
      backgroundTimeDate.toString(),
    );

    const previousSessionId = uuidv4();
    AppContextStorage.setItem(STORAGE_KEY_SESSION_ID, previousSessionId);

    const { result } = renderHook(() => useContext(AppContext), { wrapper });

    await waitFor(() =>
      expect(result.current?.sessionId).toBe(previousSessionId),
    );
    expect(result.current?.sessionCount).toBe(1);
  });

  it('sessionCount increases when user returns to app after 30 mins', async () => {
    let activeCallback: () => void = jest.fn();
    (mockUseAppStateEffect as jest.Mock).mockImplementation(
      (active: () => void) => {
        activeCallback = active;
      },
    );

    const previousSessionId = uuidv4();
    AppContextStorage.setItem(STORAGE_KEY_SESSION_ID, previousSessionId);

    const { result } = renderHook(() => useContext(AppContext), { wrapper });

    await waitFor(() => expect(result.current?.sessionCount).toBe(1));

    // Fake background time
    const backgroundTimeDate = sub(now, { minutes: 40 });
    AppContextStorage.setItem(
      STORAGE_KEY_BACKGROUND_DATE_TIME,
      backgroundTimeDate.toString(),
    );
    activeCallback();

    await waitFor(() =>
      expect(result.current?.sessionId).not.toBe(previousSessionId),
    );
    expect(result.current?.sessionCount).toBe(2);
  });

  it('sessionCount increases when user returns to app after 30 mins (initial mount)', async () => {
    const backgroundTimeDate = sub(now, { minutes: 40 });
    AppContextStorage.setItem(
      STORAGE_KEY_BACKGROUND_DATE_TIME,
      backgroundTimeDate.toString(),
    );

    const previousSessionId = uuidv4();
    AppContextStorage.setItem(STORAGE_KEY_SESSION_ID, previousSessionId);

    const { result } = renderHook(() => useContext(AppContext), {
      wrapper,
    });

    await waitFor(() => expect(result.current?.sessionCount).toBe(2));
    expect(result.current?.sessionId).not.toBe(previousSessionId);
  });
});
