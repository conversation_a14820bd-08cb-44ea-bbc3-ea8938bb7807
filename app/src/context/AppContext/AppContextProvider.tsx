import React, { useState, ReactElement, useCallback, useEffect } from 'react';
import { differenceInMinutes } from 'date-fns';
import useLatestCallback from 'use-latest-callback';
import { v4 as uuidv4 } from 'uuid';

import { useAppStateEffect } from 'src/hooks/useAppStateEffect';
import {
  MAX_MINUTES_INACTIVE,
  STORAGE_KEY_BACKGROUND_DATE_TIME,
  STORAGE_KEY_SESSION_COUNT,
  STORAGE_KEY_SESSION_ID,
} from 'src/constants';
import { updateAnalyticsSessionId } from 'src/services/analytics';
import { AppContext } from './AppContext';
import { getItem, setItem } from './AppContextStorage';

const shouldGenerateNewSessionId = async (): Promise<boolean> => {
  const backgroundDateTime = await getItem(STORAGE_KEY_BACKGROUND_DATE_TIME);
  if (backgroundDateTime) {
    const minutesPast = differenceInMinutes(
      new Date(),
      new Date(backgroundDateTime),
    );
    return minutesPast >= MAX_MINUTES_INACTIVE;
  }

  return false;
};

export function AppContextProvider({
  children,
}: React.PropsWithChildren): ReactElement | null {
  // state
  const [sessionCount, setSessionCount] = useState<number>(1);
  const [sessionId, setSessionId] = useState<string>();

  // callbacks
  const generateNewSessionId = useCallback(async () => {
    const newSessionId = uuidv4();
    await setItem(STORAGE_KEY_SESSION_ID, newSessionId);
    setSessionId(newSessionId);
    updateAnalyticsSessionId(newSessionId);
  }, []);

  const incrementAndSaveSessionCount = useCallback(async () => {
    // Fetch from storage then fallback to state
    const sessionCountValue = await getItem(STORAGE_KEY_SESSION_COUNT);
    const sessionCountNumber = sessionCountValue
      ? parseInt(sessionCountValue, 10)
      : 1;
    const newSessionCount = sessionCountNumber + 1;

    // set to state and storage
    setSessionCount(newSessionCount);
    await setItem(STORAGE_KEY_SESSION_COUNT, newSessionCount.toString());
  }, []);

  // on-mount useEffect - load stored values into state on initial render
  useEffect(() => {
    const loadAppContextStorage = async () => {
      const sessionCountValue = await getItem(STORAGE_KEY_SESSION_COUNT);
      if (sessionCountValue) {
        setSessionCount(parseInt(sessionCountValue, 10));
      }

      const sessionIdValue = await getItem(STORAGE_KEY_SESSION_ID);
      if (sessionIdValue) {
        if (await shouldGenerateNewSessionId()) {
          // Create new session
          await incrementAndSaveSessionCount();
          await generateNewSessionId();
        } else {
          // Assign previous session
          setSessionId(sessionIdValue);
          updateAnalyticsSessionId(sessionIdValue);
        }
      } else {
        // First time user
        generateNewSessionId();
      }
    };

    loadAppContextStorage();
  }, [generateNewSessionId, incrementAndSaveSessionCount]);

  // App active and background event callbacks
  const activeCallback = useLatestCallback(() => {
    const updateSession = async () => {
      if (await shouldGenerateNewSessionId()) {
        await incrementAndSaveSessionCount();
        await generateNewSessionId();
      }
    };

    updateSession();
  });

  const handleSavingBackgroundDateTime = useCallback(() => {
    setItem(STORAGE_KEY_BACKGROUND_DATE_TIME, new Date().toString());
  }, []);

  useAppStateEffect(activeCallback, handleSavingBackgroundDateTime, {
    disableOnInitialRender: true,
  });

  return (
    <AppContext.Provider
      value={{
        sessionCount,
        sessionId,
      }}
    >
      {sessionId ? children : null}
    </AppContext.Provider>
  );
}
