import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  STORAGE_KEY_BACKGROUND_DATE_TIME,
  STORAGE_KEY_SESSION_COUNT,
  STORAGE_KEY_SESSION_ID,
} from 'src/constants';

type AppContextKeys =
  | typeof STORAGE_KEY_BACKGROUND_DATE_TIME
  | typeof STORAGE_KEY_SESSION_COUNT
  | typeof STORAGE_KEY_SESSION_ID;

const AppContextStorage: Partial<Record<AppContextKeys, string>> = {};

export async function getItem(key: AppContextKeys): Promise<string | null> {
  const cachedValue = AppContextStorage[key];
  if (cachedValue) {
    return Promise.resolve(cachedValue);
  }

  const value = await AsyncStorage.getItem(key);
  if (value) {
    AppContextStorage[key] = value;
    return Promise.resolve(value);
  }

  return Promise.resolve(null);
}

export async function setItem(
  key: AppContextKeys,
  value: string,
): Promise<void> {
  AppContextStorage[key] = value;
  return AsyncStorage.setItem(key, value);
}

/**
 * Used for testing
 * @private
 */
export function clear(): void {
  Object.keys(AppContextStorage).forEach((key) => {
    // @ts-expect-error - used for testing
    delete AppContextStorage[key];
  });
}
