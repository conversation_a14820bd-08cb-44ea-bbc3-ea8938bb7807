import React, {
  createContext,
  useState,
  ReactElement,
  useCallback,
  useMemo,
} from 'react';
import type { UserAccessType } from 'src/auth/auth.types';
import { getSelectedAccount } from 'src/auth/utils/getSelectedAccount';
import { STORAGE_KEY_SELECTED_COMPANY_ID } from 'src/constants';
import { useAsyncStorage } from 'src/hooks/useAsyncStorage';

type Context = {
  user?: User;
  setUser: (user: User | undefined) => void;
  isUserLoading: boolean;
  setIsUserLoading: (isUserLoading: boolean) => void;
  isAuthenticating: boolean;
  setIsAuthenticating: (authenticating: boolean) => void;
  selectedAccount?: CompanyAccount | undefined;
  setSelectedAccount: (companyId: number) => void;
};

export interface CompanyAccount {
  companyId: number;
  userId: string;
  vettingStatus: UserAccessType;
  isActive: boolean;
}

export interface User {
  /**
   * New Checkatrade identity user_id (SlashId).
   * To update data on a trade user account then use the userId
   * from the accounts array. `accounts[0].userId`
   */
  catIdUid: string;
  /**
   * user's email from the identity service
   */
  email: string | null;
  /**
   * List of companies the user is associated with
   */
  accounts: CompanyAccount[];
}

export const UserContext = createContext<Context>({
  user: undefined,
  setUser: () => null,
  isUserLoading: true,
  setIsUserLoading: () => null,
  isAuthenticating: false,
  setIsAuthenticating: () => null,
  setSelectedAccount: () => null,
});

export function UserContextProvider({
  children,
}: React.PropsWithChildren): ReactElement {
  const [user, setUser] = useState<User | undefined>();
  const [isAuthenticating, setIsAuthenticating] = useState<boolean>(false);
  const [isUserLoading, setIsUserLoading] = useState<boolean>(true);
  const { value: selectedCompanyIdStr, storeValue: setSelectedCompanyId } =
    useAsyncStorage(STORAGE_KEY_SELECTED_COMPANY_ID);

  // Computed
  const selectedAccount = useMemo(
    () =>
      user?.accounts
        ? getSelectedAccount(user.accounts, Number(selectedCompanyIdStr))
        : undefined,
    [user?.accounts, selectedCompanyIdStr],
  );

  // Effects/Callbacks
  const setSelectedAccount = useCallback(
    (companyId: number | null) => {
      setSelectedCompanyId(companyId?.toString() ?? null);
    },
    [setSelectedCompanyId],
  );

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
        isUserLoading,
        setIsUserLoading,
        isAuthenticating,
        setIsAuthenticating,
        selectedAccount,
        setSelectedAccount,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}
