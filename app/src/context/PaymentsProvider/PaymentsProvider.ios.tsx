import React, {
  type PropsWithChildren,
  type ReactElement,
  useCallback,
  useEffect,
  useRef,
} from 'react';
import ExpoAdyenTapToPay from '@checkatrade/expo-adyen-tap-to-pay';
import { captureException } from 'src/services/datadog';
import { useUserContext } from 'src/hooks/useUser';
import { config } from 'src/config';
import { getAccessToken } from 'src/auth/utils/authSession';
import { useQueryClient } from '@tanstack/react-query';
import { TAP_TO_PAY_WARM_UP_KEY } from 'src/screens/JobPayments/hooks/useTapToPayWarmUp';
import { IS_ABOVE_IOS_17 } from 'src/constants';

ExpoAdyenTapToPay.setSessionsUrl(
  `${config.capiUrl}/v2/trade-app/payments/session`,
);

export function PaymentsProvider({
  children,
}: PropsWithChildren): ReactElement {
  // Refs
  const intervalRef = useRef<NodeJS.Timeout | undefined>();

  // Computed Values
  const { companyId } = useUserContext();
  const queryClient = useQueryClient();

  // Methods
  const storeToken = useCallback(async () => {
    try {
      const token = await getAccessToken();
      if (!token || !companyId) {
        return;
      }

      ExpoAdyenTapToPay.storeAuthToken(token);
    } catch (error) {
      captureException(error, {
        tags: {
          module: 'PaymentsProvider',
          method: 'getAccessToken',
          call: 'storeToken',
        },
      });
    }
  }, [companyId]);

  // Effects
  useEffect(() => {
    ExpoAdyenTapToPay.storeCompanyId(String(companyId));
    queryClient.invalidateQueries({ queryKey: [TAP_TO_PAY_WARM_UP_KEY] });
  }, [companyId, queryClient]);

  useEffect(() => {
    console.info('setting', `${config.capiUrl}/v2/trade-app/payments/session`);

    intervalRef.current = setInterval(
      storeToken,
      15 * 60 * 1000, // 15 minutes
    );

    storeToken();

    return () => clearInterval(intervalRef.current);
  }, [storeToken]);

  useEffect(() => {
    if (IS_ABOVE_IOS_17) {
      const { remove } = ExpoAdyenTapToPay.addEventListener(
        'adyenTapToPayRegisterResult',
        (event) => {
          switch (event.result) {
            case 'failure':
              captureException(event, {
                tags: {
                  module: 'PaymentsProviderIos',
                  method: 'ExpoAdyenTapToPay.addEventListener',
                  call: 'adyenTapToPayRegisterResult',
                },
              });
              break;
            case 'success':
              // We omit the success case logging as it is called for every
              // warmup and is not indicative of a problem
              break;
          }
        },
      );

      return remove;
    }

    return () => {
      // Noop
    };
  }, []);

  return <>{children}</>;
}
