import React from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { Typography } from '@cat-home-experts/react-native-components';

import { captureException as mockCaptureException } from 'src/services/datadog';
import { createQueryClientWrapper } from 'src/utilities/tanstack-query/tanstack-test-utils';
import ExpoAdyenTapToPay from '@checkatrade/expo-adyen-tap-to-pay';
import { PaymentsProvider } from './PaymentsProvider.ios';

jest.mock('src/auth/utils/authSession', () => ({
  getAccessToken: jest.fn(),
}));

jest.mock('@checkatrade/expo-adyen-tap-to-pay', () => ({
  setSessionsUrl: jest.fn(),
  storeAuthToken: jest.fn(),
  storeCompanyId: jest.fn(),
  addEventListener: jest.fn(),
}));

jest.mock('src/hooks/useUser', () => ({
  useUserContext: () => ({ companyId: 12345 }),
}));

describe('PaymentsProvider', () => {
  const testComponentLabel = 'Test Child Component';
  const wrapper = createQueryClientWrapper();

  beforeEach(() => {
    (ExpoAdyenTapToPay.addEventListener as jest.Mock).mockReturnValue(
      jest.fn(),
    );
  });
  afterEach(() => {
    jest.resetAllMocks();
    cleanup();
  });

  it('renders children correctly', () => {
    const { getByText } = render(
      <PaymentsProvider>
        <Typography>{testComponentLabel}</Typography>
      </PaymentsProvider>,
      { wrapper },
    );

    expect(getByText(testComponentLabel)).toBeTruthy();
  });

  // eslint-disable-next-line jest/no-disabled-tests
  it.skip('calls to store token initially', () => {
    // (getFirebaseAuthToken as jest.Mock).mockResolvedValue('mock-token');
    const { getByText } = render(
      <PaymentsProvider>
        <Typography>{testComponentLabel}</Typography>
      </PaymentsProvider>,
      { wrapper },
    );

    // TODO: BAP-99 add check call to ExpoModule
    expect(getByText(testComponentLabel)).toBeDefined();
    // expect(getFirebaseAuthToken).toHaveBeenCalledTimes(1);
  });

  // eslint-disable-next-line jest/no-disabled-tests
  it.skip('calls storeToken every 15 minutes', async () => {
    // (getFirebaseAuthToken as jest.Mock).mockResolvedValue('mock-token');

    render(
      <PaymentsProvider>
        <Typography>{testComponentLabel}</Typography>
      </PaymentsProvider>,
      { wrapper },
    );

    // TODO: BAP-99 add check call to ExpoModule
    // expect(getFirebaseAuthToken).toHaveBeenCalledTimes(2);
  });

  // eslint-disable-next-line jest/no-disabled-tests
  it.skip('still renders if it captures exception during token fetch', async () => {
    // (getFirebaseAuthToken as jest.Mock).mockImplementation(() => {
    //   throw new Error('error');
    // });

    const { getByText } = render(
      <PaymentsProvider>
        <Typography>{testComponentLabel}</Typography>
      </PaymentsProvider>,
    );

    // TODO: BAP-99 add check call wasn't made to ExpoModule
    expect(mockCaptureException).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        tags: {
          module: 'PaymentsProvider',
          method: 'getFirebaseAuthToken',
          call: 'storeToken',
        },
      }),
    );

    expect(getByText(testComponentLabel)).toBeTruthy();
  });
});
