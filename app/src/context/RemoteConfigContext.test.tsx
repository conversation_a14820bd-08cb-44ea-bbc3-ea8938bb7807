import React, { ReactElement, useContext } from 'react';
import { cleanup, renderHook, waitFor } from '@testing-library/react-native';
import { addMinutes } from 'date-fns';
import { useAppStateEffect as mockUseAppStateEffect } from 'src/hooks/useAppStateEffect';
import { fetchRemoteConfig } from 'src/services/firebase/remoteConfig';
import { mockCurrentDate } from 'src/utilities/jest';
import { useSetAtom } from 'jotai';
import {
  RemoteConfigContextProvider,
  RemoteConfigContext,
} from './RemoteConfigContext';

jest.mock('src/hooks/useAppStateEffect', () => ({
  useAppStateEffect: jest.fn(),
}));

jest.mock('src/services/firebase/remoteConfig', () => ({
  fetchRemoteConfig: jest.fn(),
  getAllRemoteConfigValuesAsString: jest.fn(),
}));

jest.mock('src/state/lab/atoms/labAtoms', () => ({
  updateLabsConfigAtom: jest.fn(),
}));
jest.mock('jotai', () => ({
  useSetAtom: jest.fn(),
}));

const createWrapper =
  () =>
  ({ children }: { children: ReactElement }) => (
    <RemoteConfigContextProvider>{children}</RemoteConfigContextProvider>
  );

describe('context | RemoteConfigContext', () => {
  beforeEach(() => {
    mockCurrentDate(new Date(2023, 0, 5));
    (fetchRemoteConfig as jest.Mock).mockResolvedValue(true);
    (useSetAtom as jest.Mock).mockReturnValue(jest.fn());
  });

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
    jest.resetAllMocks();
    jest.restoreAllMocks();
  });

  it('returns undefined configLastFetchedAt on initial load', () => {
    // Act
    const { result } = renderHook(() => useContext(RemoteConfigContext), {
      wrapper: createWrapper(),
    });

    // Assert
    expect(result.current.configLastFetchedAt).toBeUndefined();
  });

  it('calls fetchRemoteConfig on initial load, then updates configLastFetchedAt', async () => {
    // Arrange
    let activeCallback: () => void = jest.fn();
    (mockUseAppStateEffect as jest.Mock).mockImplementation(
      (active: () => void) => {
        activeCallback = active;
      },
    );

    // Act
    const { result } = renderHook(() => useContext(RemoteConfigContext), {
      wrapper: createWrapper(),
    });

    // Assert
    expect(result.current.configLastFetchedAt).toBeUndefined();

    // Act
    activeCallback();

    // Assert
    await waitFor(() =>
      expect(result.current.configLastFetchedAt).toBeDefined(),
    );
    expect(fetchRemoteConfig).toHaveBeenCalledTimes(1);
  });

  it('calls fetchRemoteConfig initial load then on app active, then updates configLastFetchedAt', async () => {
    // Arrange
    let activeCallback: () => void = jest.fn();
    (mockUseAppStateEffect as jest.Mock).mockImplementation(
      (active: () => void) => {
        activeCallback = active;
      },
    );

    // Act
    const { result } = renderHook(() => useContext(RemoteConfigContext), {
      wrapper: createWrapper(),
    });

    // Assert
    expect(result.current.configLastFetchedAt).toBeUndefined();

    // Act - 1st time
    activeCallback();
    await waitFor(() =>
      expect(result.current.configLastFetchedAt).toBeDefined(),
    );
    // Assert
    expect(fetchRemoteConfig).toHaveBeenCalledTimes(1);
    const prevTime = result.current.configLastFetchedAt;
    expect(result.current.configLastFetchedAt).toBeDefined();

    // Act - 2nd time
    mockCurrentDate(addMinutes(new Date(), 1));
    activeCallback();
    // Assert
    expect(fetchRemoteConfig).toHaveBeenCalledTimes(2);
    expect(result.current.configLastFetchedAt).toBeDefined();
    await waitFor(() =>
      expect(result.current.configLastFetchedAt).not.toEqual(prevTime),
    );
  });

  it('calls updateLabsConfig on when configLastFetchedAt changes', async () => {
    // Arrange
    const updateLabsConfigMock = jest.fn();
    (useSetAtom as jest.Mock).mockReturnValue(updateLabsConfigMock);
    let activeCallback: () => void = jest.fn();
    (mockUseAppStateEffect as jest.Mock).mockImplementation(
      (active: () => void) => {
        activeCallback = active;
      },
    );

    // Act
    const { result } = renderHook(() => useContext(RemoteConfigContext), {
      wrapper: createWrapper(),
    });

    // Assert
    expect(result.current.configLastFetchedAt).toBeUndefined();
    activeCallback();
    await waitFor(() =>
      expect(result.current.configLastFetchedAt).toBeDefined(),
    );
    expect(updateLabsConfigMock).toHaveBeenCalledTimes(1);
  });
});
