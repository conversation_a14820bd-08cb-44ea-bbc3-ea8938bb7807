import { cleanup, renderHook } from '@testing-library/react-native';
import React, { ReactElement } from 'react';
import { useFeatureFlag as useFeatureFlagMock } from 'src/hooks/useFeatureFlag';
import { UserAccessLevel } from 'src/hooks/useUserAccessLevels';
import { MainNavigationType } from 'src/navigation/MainNavigation/useMainNavType';
import { useEnableMyVettingChecks } from 'src/screens/MyTeam/hooks/useEnableMyVettingChecks';
import { useNavigationLinks } from './NavigationLinksContext';
import { NavigationLinksContextProvider } from './NavigationLinksContextProvider';

jest.mock('src/hooks/useAppStateEffect', () => ({
  useAppStateEffect: jest.fn(),
}));

jest.mock('src/hooks/useFeatureFlag', () => ({
  useFeatureFlag: jest.fn(),
}));

jest.mock('src/hooks/useIsTestUser', () => ({
  useIsTestUser: jest.fn(),
}));

jest.mock('src/hooks/useFeatureFlagWithBetaTesters', () => ({
  useFeatureFlagWithBetaTesters: () => true,
}));

jest.mock('src/hooks/useIsBetaTester', () => ({
  useIsBetaTester: () => true,
}));

jest.mock('src/state/navigationBadge/hooks/useNavBadges', () => ({
  useNavBadges: () => ({ badges: {} }),
}));

jest.mock('src/hooks/useRemoteConfig', () => ({
  useRemoteConfigString: jest.fn(),
}));

jest.mock('src/hooks/useTapToPay', () => ({
  useTapToPay: () => ({
    tapToPayEnabled: true,
  }),
}));

jest.mock('jotai', () => ({
  atom: jest.fn(() => ({})),
  useAtomValue: jest.fn(() => true),
}));

jest.mock('src/hooks/useUserAccessLevels', () => {
  const MockUserAccessLevel = {
    Active: 'Active',
    ActiveOnboarding: 'ActiveOnboarding',
    Essentials: 'Essentials',
    EssentialsOnboarding: 'EssentialsOnboarding',
    Inactive: 'Inactive',
    Onboarding: 'Onboarding',
    Suspended: 'Suspended',
  };

  return {
    UserAccessLevel: MockUserAccessLevel,
    useUserAccessLevels: jest.fn(() => MockUserAccessLevel.Active),
  };
});

jest.mock('src/screens/JobPayments/hooks/useGetOnboardingStatus', () => ({
  useGetOnboardingStatus: jest.fn(() => ({
    data: {
      onboarding: {
        status: 'READY_FOR_PAYMENT',
        providedTaxInformation: true,
      },
    },
  })),
}));

jest.mock('src/screens/MyTeam/hooks/useGetTeamPersonsVettingDetails', () => ({
  useGetTeamPersonsVettingDetails: jest.fn(() => ({
    consentStatus: { consentStatus: 'NotProvided' },
  })),
}));

jest.mock('src/hooks/useCompany', () => ({
  useCompanyContext: jest.fn(() => ({
    company: {
      isAccountOwner: true,
    },
  })),
}));

jest.mock('src/screens/MyTeam/hooks/useEnableCMATeamManagement', () => ({
  useEnableCMATeamManagement: jest.fn(() => true),
}));

jest.mock('src/screens/MyTeam/hooks/useEnableMyVettingChecks', () => ({
  useEnableMyVettingChecks: jest.fn(() => true),
}));

const wrapper = ({ children }: { children: ReactElement }) => (
  <NavigationLinksContextProvider>{children}</NavigationLinksContextProvider>
);

describe('NavigationLinksContextProvider', () => {
  beforeEach(() => {
    (useFeatureFlagMock as jest.Mock).mockReturnValue(true);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
    cleanup();
  });

  it('returns mainRoutes from inside the Context Provider', () => {
    const { result } = renderHook(() => useNavigationLinks(), {
      wrapper,
    });

    expect(result.current.mainNavType).toBe(MainNavigationType.BOTTOM_TABS);
    expect(result.current.linkingOptions).toBeDefined();
    expect(result.current.globalRoutes).toBeDefined();
    expect(result.current.globalRoutesKeyedByName).toBeDefined();
    expect(result.current.mainRoutes).toBeDefined();
    expect(result.current.moreLinks).toBeDefined();
  });

  it('uses useEnableMyVettingChecks with the correct Essentials parameter', () => {
    const mockUseUserAccessLevels = jest.requireMock(
      'src/hooks/useUserAccessLevels',
    ).useUserAccessLevels;
    mockUseUserAccessLevels.mockReturnValue(UserAccessLevel.Essentials);

    renderHook(() => useNavigationLinks(), { wrapper });

    expect(useEnableMyVettingChecks).toHaveBeenCalledWith(true);
  });

  it('uses useEnableMyVettingChecks with isEssentials=false when user is not an Essentials user', () => {
    const mockUseUserAccessLevels = jest.requireMock(
      'src/hooks/useUserAccessLevels',
    ).useUserAccessLevels;
    mockUseUserAccessLevels.mockReturnValue(UserAccessLevel.Active);

    renderHook(() => useNavigationLinks(), { wrapper });

    expect(useEnableMyVettingChecks).toHaveBeenCalledWith(false);
  });

  it('includes My Vetting Checks link when enableMyVettingChecks is true for Essentials users', () => {
    const mockUseUserAccessLevels = jest.requireMock(
      'src/hooks/useUserAccessLevels',
    ).useUserAccessLevels;
    mockUseUserAccessLevels.mockReturnValue(UserAccessLevel.Essentials);

    (useEnableMyVettingChecks as jest.Mock).mockReturnValue(true);

    const { result } = renderHook(() => useNavigationLinks(), { wrapper });

    const myAccountSection = result.current.moreLinks.find(
      (section) => section.id === 'myMembership',
    );
    const myVettingChecksLink = myAccountSection?.links?.find(
      (link) => link.id === 'myVettingChecks',
    );

    expect(myVettingChecksLink).toBeDefined();
  });

  it('does not include My Vetting Checks link when enableMyVettingChecks is false for Essentials users', () => {
    const mockUseUserAccessLevels = jest.requireMock(
      'src/hooks/useUserAccessLevels',
    ).useUserAccessLevels;
    mockUseUserAccessLevels.mockReturnValue(UserAccessLevel.Essentials);

    (useEnableMyVettingChecks as jest.Mock).mockReturnValue(false);

    const { result } = renderHook(() => useNavigationLinks(), { wrapper });

    const myAccountSection = result.current.moreLinks.find(
      (section) => section.id === 'myMembership',
    );
    const myVettingChecksLink = myAccountSection?.links?.find(
      (link) => link.id === 'myVettingChecks',
    );

    expect(myVettingChecksLink).toBeUndefined();
  });
});
