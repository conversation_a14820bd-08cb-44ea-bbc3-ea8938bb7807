import type { LinkingOptions } from '@react-navigation/native';
import { createContext, useContext, useMemo } from 'react';
import { UserAccessLevel } from 'src/hooks/useUserAccessLevels';
import type { MainNavigationType } from 'src/navigation/MainNavigation/useMainNavType';
import type { AllScreensParamList } from 'src/navigation/routes';
import type { RouteItem } from 'src/navigation/types/routeTypes';
import type { MoreLink } from 'src/screens/MoreLinks/links';

export type NavigationLinksContext = {
  userAccessLevel: UserAccessLevel | null;
  mainRoutes: RouteItem[];
  globalRoutes: RouteItem[];
  moreLinks: MoreLink[];
  globalRoutesKeyedByName: { [key: string]: RouteItem };
  linkingOptions?: LinkingOptions<AllScreensParamList>;
  mainNavType?: MainNavigationType;
  isPayPerLeadMember?: boolean;
  enableCMATeamManagement?: boolean;
  hasNavigationLoaded?: boolean;
  isHelpInMainRoutes?: boolean;
};

export const NavigationLinksContext = createContext<NavigationLinksContext>({
  userAccessLevel: null,
  mainRoutes: [],
  globalRoutes: [],
  moreLinks: [],
  globalRoutesKeyedByName: {},
});

type UseNavigationLinksReturn = NavigationLinksContext & {
  isReviewsInMainRoutes: boolean;
  isPaymentsInMainRoutes: boolean;
  isPhotosInMainRoutes: boolean;
};

export function useNavigationLinks(): UseNavigationLinksReturn {
  const {
    userAccessLevel,
    globalRoutes,
    globalRoutesKeyedByName,
    mainRoutes,
    moreLinks,
    linkingOptions,
    mainNavType,
    hasNavigationLoaded,
    isPayPerLeadMember,
    enableCMATeamManagement,
  } = useContext(NavigationLinksContext);

  const isReviewsInMainRoutes = useMemo(
    () => mainRoutes.some((route) => route.key === 'reviews'),
    [mainRoutes],
  );

  const isPaymentsInMainRoutes = useMemo(
    () => mainRoutes.some((route) => route.key === 'job-payments'),
    [mainRoutes],
  );

  const isPhotosInMainRoutes = useMemo(
    () => mainRoutes.some((route) => route.key === 'photos'),
    [mainRoutes],
  );

  const isHelpInMainRoutes = useMemo(
    () => mainRoutes.some((route) => route.key === 'help'),
    [mainRoutes],
  );

  return {
    userAccessLevel,
    globalRoutes,
    globalRoutesKeyedByName,
    mainRoutes,
    moreLinks,
    linkingOptions,
    mainNavType,
    hasNavigationLoaded,
    isPayPerLeadMember,
    enableCMATeamManagement,
    isReviewsInMainRoutes,
    isPaymentsInMainRoutes,
    isPhotosInMainRoutes,
    isHelpInMainRoutes,
  };
}
