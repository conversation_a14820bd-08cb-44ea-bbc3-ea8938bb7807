import React, { useCallback, useEffect, useState } from 'react';
import { isTruthy } from '@cat-home-experts/react-native-utilities';
import { logEvent } from 'src/services/analytics';
import { EventTypeKey } from 'src/services/analytics/logEmitter';
import { captureException } from 'src/services/datadog';
import { showToast } from 'src/components';
import { FeedbackBottomSheet } from 'src/components/FeedbackBottomSheet/FeedbackBottomSheet';
import { EVENT_TYPE } from 'src/constants.events';
import { useLogEventSubscription } from 'src/hooks/useLogEventSubscription';
import {
  clearTarsDataFromStorage,
  readTarsDataFromStorage,
  writeTarsDatumToStorage,
} from 'src/utilities/storage/tarsFlowData';
import { useUserContext } from 'src/hooks/useUser';
import { usePreviousUserId } from 'src/state/common/previousUserId';

const APPROVED_EVENTS: EventTypeKey[] = [
  EVENT_TYPE.INBOX_CHANNEL_BACK,
  EVENT_TYPE.CAMPAIGN_PAUSE_SUCCESS_SUBMITTED,
  EVENT_TYPE.CAMPAIGN_REVIEW_AND_CONFIRM_PAGE_CONTINUE_CLICKED,
];

export const FeedbackProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  // State
  const [showFeedbackModalForEvent, setShowFeedbackModalForEvent] =
    useState<EventTypeKey | null>(null);
  const [rating, setRating] = useState<1 | 2 | 3 | 4 | 5 | undefined>();
  const [feedback, setFeedback] = useState<string>('');

  // Computed Values
  const { user } = useUserContext();
  const [previousUserId, setPreviousUserId] = usePreviousUserId();

  // Methods
  const onDismiss = useCallback(() => {
    setShowFeedbackModalForEvent(null);
  }, [setShowFeedbackModalForEvent]);

  const resetFeedback = useCallback(() => {
    setShowFeedbackModalForEvent(null);
    setRating(undefined);
    setFeedback('');
  }, [setRating, setFeedback, setShowFeedbackModalForEvent]);

  const onSubmit = useCallback(async () => {
    if (!isTruthy(rating) || !showFeedbackModalForEvent) {
      return;
    }

    try {
      await logEvent(EVENT_TYPE.FEEDBACK_MODAL, {
        rating: rating,
        eventName: showFeedbackModalForEvent,
        feedback,
      });

      showToast({
        text1: 'Thanks for your feedback!',
        type: 'success',
      });

      resetFeedback();
      setShowFeedbackModalForEvent(null);
    } catch (error) {
      showToast({
        text1: 'Feedback failed to send',
        type: 'error',
      });

      resetFeedback();
      setShowFeedbackModalForEvent(null);

      captureException(error);
    }
  }, [rating, showFeedbackModalForEvent, feedback, resetFeedback]);

  // Effects
  useLogEventSubscription(async (event) => {
    if (!APPROVED_EVENTS.includes(event.eventName)) {
      return;
    }

    const tarsFlowData = await readTarsDataFromStorage();

    // Currently this has been built just to trigger if this exact event
    // has not triggered before. This functionality may need to be extended for
    // new use cases of the feedback modal.

    const anyExactMatches = tarsFlowData.some((datum) => {
      return datum.eventName === event.eventName;
    });

    if (anyExactMatches) {
      return;
    }

    await writeTarsDatumToStorage({
      eventName: event.eventName,
      dateShown: new Date(),
    });

    setShowFeedbackModalForEvent(event.eventName);
  }, []);

  /**
   * Clear TARS data if previous user UID has been set, and does not match
   * current user UID. To prevent excessive surveying.
   */
  useEffect(() => {
    if (!user) {
      return;
    }

    switch (previousUserId) {
      case undefined:
      case user.catIdUid:
        return;
      case null:
        setPreviousUserId(user.catIdUid);
        return;
      default:
        clearTarsDataFromStorage().then(() => {
          setPreviousUserId(user.catIdUid);
        });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.catIdUid, previousUserId, setPreviousUserId]);

  return (
    <>
      {children}
      <FeedbackBottomSheet
        visible={isTruthy(showFeedbackModalForEvent)}
        onDismiss={onDismiss}
        onSubmit={onSubmit}
        rating={rating}
        setRating={setRating}
        feedback={feedback}
        setFeedback={setFeedback}
      />
    </>
  );
};
