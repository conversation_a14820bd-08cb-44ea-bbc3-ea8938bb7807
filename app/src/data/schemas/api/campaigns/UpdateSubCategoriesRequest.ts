import { z } from 'zod';
import { UpdateApiTypeEnum } from './schemas';

export const UpdateCategoryApiSchema = z.object({
  categoryId: z
    .number()
    .describe('The unique identifier for the category or sub-category'),
  parentCategoryId: z
    .number()
    .optional()
    .describe(
      "The sub-category's parent category (0 is not a valid category in this API)",
    ),
  isSearchable: z
    .boolean()
    .optional()
    .describe(
      'Should the trade show in search results for this category.  Can only be set for the parent category, will be true for any child categories and if null.',
    ),
});

export const UpdateSubcategoriesApiSchema = z.object({
  category: UpdateCategoryApiSchema,
  subCategories: z.array(UpdateCategoryApiSchema),
  updateType: UpdateApiTypeEnum,
});

export type UpdateSubCategoriesBodyType = z.infer<
  typeof UpdateSubcategoriesApiSchema
>;

export type UpdateCategoryApiType = z.infer<typeof UpdateCategoryApiSchema>;
