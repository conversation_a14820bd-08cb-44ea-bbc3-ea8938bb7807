import { z } from 'zod';

export enum PriceRangeType {
  CALL = 'call',
  CLICK = 'click',
  DIRECTORY = 'directory',
  MESSAGE = 'message',
  REQUEST_A_QUOTE = 'requestAQuote',
}

const PriceRangeDto = z.object({
  maxPrice: z.number(),
  minPrice: z.number(),
});

const PriceRangesDto = z.object({
  categoryId: z.number(),
  subCategoryId: z.number(),
  [PriceRangeType.CALL]: PriceRangeDto,
  [PriceRangeType.CLICK]: PriceRangeDto,
  [PriceRangeType.DIRECTORY]: PriceRangeDto,
  [PriceRangeType.MESSAGE]: PriceRangeDto,
  [PriceRangeType.REQUEST_A_QUOTE]: PriceRangeDto,
  averageRate: z.number(),
});

export const PriceRangesDtoPageResult = z.object({
  items: z.array(PriceRangesDto),
});

export type PriceRangesDtoType = z.infer<typeof PriceRangesDto>;
export type PriceRangeDtoType = z.infer<typeof PriceRangeDto>;

export type PriceRangesDtoPageResultType = z.infer<
  typeof PriceRangesDtoPageResult
>;
