import { nativeEnum, z } from 'zod';
import { coerceNullishToOptional } from 'src/data/schemas/helpers/coerceNullishToOptional';
import { workAreaReducerState } from 'src/screens/Campaigns/CampaignDetails/reducers/WorkAreaReducer';
import { safeNativeEnum } from '../../helpers/safeNativeEnum';

export enum CampaignTypeEnum {
  Ppl = 'Ppl',
  Alsp = 'Alsp',
  Fixed = 'Fixed',
  Sponsored = 'Sponsored',
  MdpSponsoredSearch = 'MdpSponsoredSearch',
  MdpSponsoredDisplay = 'MdpSponsoredDisplay',
}

export enum CampaignHeadingsType {
  Ppl = 'Ppl',
  Fixed = 'Fixed',
  MinimumCommitment = 'MinimumCommitment',
  Alsp = 'Alsp',
  Sponsored = 'Sponsored',
  MdpSponsoredSearch = 'MdpSponsoredSearch',
  MdpSponsoredDisplay = 'MdpSponsoredDisplay',
}

export enum BiddingStrategyApiEnum {
  FullyAutomatic = 'FullyAutomatic',
  SemiAutomatic = 'SemiAutomatic',
  Manual = 'Manual',
}

export enum GeographyTypeApiEnum {
  PostcodeDistrict = 'PostcodeDistrict',
  PostcodeArea = 'PostcodeArea',
  PostcodeSector = 'PostcodeSector',
  DirectoryArea = 'DirectoryArea',
}

export enum UpdateApiType {
  Add = 'Add',
  Replace = 'Replace',
  Remove = 'Remove',
}

export enum QuoteType {
  Fixed = 'Fixed',
  Ppl = 'Ppl',
}

export enum QuoteRequestBehaviour {
  DenyUpgradeOffTarget = 'DenyUpgradeOffTarget',
  OfferCampaignQuotes = 'OfferCampaignQuotes',
  CallSupport = 'CallSupport',
  Alsp = 'Alsp',
}

export enum QuoteRequestStatus {
  Active = 'Active',
  Expired = 'Expired',
  Converted = 'Converted',
  Superseded = 'Superseded',
}

export enum QuoteRequestSource {
  Dsu = 'Dsu',
  Gsf = 'Gsf',
  TradeApp = 'TradeApp',
  Migration = 'Migration',
}

export enum CompanySize {
  One = 'One',
  TwoToFive = 'TwoToFive',
  FiveToTen = 'FiveToTen',
  TenOrMore = 'TenOrMore',
}

export enum OfferingType {
  NewProduct = 'NewProduct',
  Upgrade = 'Upgrade',
  Downgrade = 'Downgrade',
  Amendment = 'Amendment',
  BudgetRecalculation = 'BudgetRecalculation',
  Renewal = 'Renewal',
  RenewalAmendment = 'RenewalAmendment',
}

export enum AmendmentEnum {
  NotApplicable = 'NotApplicable',
  AvailableLeadCommitmentChanged = 'AvailableLeadCommitmentChanged',
  AvailableLeadCommitmentUnchanged = 'AvailableLeadCommitmentUnchanged',
  Unavailable = 'Unavailable',
  NotEnoughLeads = 'NotEnoughLeads',
}

export enum BudgetRecalculationEnum {
  NotApplicable = 'NotApplicable',
  Recommended = 'Recommended',
}

export enum NewProductEnum {
  NotApplicable = 'NotApplicable',
  Available = 'Available',
  Unavailable = 'Unavailable',
}

export enum PplEnum {
  NotApplicable = 'NotApplicable',
  Available = 'Available',
  Unavailable = 'Unavailable',
}

export enum UpgradeEnum {
  NotApplicable = 'NotApplicable',
  Available = 'Available',
  Unavailable = 'Unavailable',
  OffTarget = 'OffTarget',
  NotEnoughLeads = 'NotEnoughLeads',
  NotEnoughTimeRemaining = 'NotEnoughTimeRemaining',
}

export enum QuoteRequestType {
  Acquisition = 'Acquisition',
  Renewal = 'Renewal',
  RenewalAmendment = 'RenewalAmendment',
  Upgrade = 'Upgrade',
  AdditionalProduct = 'AdditionalProduct',
  Amendment = 'Amendment',
  Lifecycle = 'Lifecycle',
}

export const UpdateApiTypeEnum = z.enum([
  UpdateApiType.Add,
  UpdateApiType.Replace,
  UpdateApiType.Remove,
]);

export const CategoryApiSchema = z.object({
  categoryId: z.number().int().positive(),
  isSearchable: z.boolean(),
});

export const SubCategoryApiSchema = z.object({
  categoryId: z.number().int().positive(),
  parentCategoryId: z.number().int().positive(),
});

const RateBidApiSchema = z.object({
  call: z.number(),
  click: z.number(),
  directory: z.number(),
  message: z.number(),
  requestAQuote: z.number(),
  categoryId: z.number().int().positive(),
  subCategoryId: coerceNullishToOptional(z.number().int().positive()),
  dateAgreed: z.string().datetime({ offset: true }),
});

const quoteLeadForecastSchema = z.object({
  min: z.number().int(),
  max: z.number().int(),
  monthlyDistribution: z.object({}),
  selectedBudget: z.number().nullable(),
});

const fixedQuoteDetailsSchema = z.object({
  pricePerMonth: z.number(),
  commitmentPeriodMonths: z.number().int(),
});

const pplQuoteDetailsSchema = z.object({
  selectedBudget: z.number().nullable(),
  recommendedBudget: z.number(),
  maxBudget: z.number(),
  minBudget: z.number(),
  maxAverageLeadPrice: z.number(),
  minAverageLeadPrice: z.number(),
});

export const quoteCategorySchema = z.object({
  categoryId: z.number().int().positive(),
  parentCategoryId: z.number().int().positive().nullable(),
  isSearchable: z.boolean().nullable(),
});

export const campaignQuoteSchema = z.object({
  quoteId: z.string(),
  quoteType: safeNativeEnum(QuoteType),
  offeringType: safeNativeEnum(OfferingType),
  leadForecast: quoteLeadForecastSchema,
  leadForecastUpgradeDelta: quoteLeadForecastSchema.optional().nullable(),
  fixedDetails: fixedQuoteDetailsSchema.optional().nullable(),
  pplDetails: pplQuoteDetailsSchema.optional().nullable(),
  tierLabel: z.string().nullable(),
  order: z.number().int(),
  isRecommended: z.boolean(),
  isSelected: z.boolean(),
  isSpecialOffer: z.boolean(),
});

export type CampaignDetailsPagesInitialState = {
  campaignId?: string;
  selectedCategoryId: number;
  selectedCategoryName: string;
  selectedSubCategoriesIds: number[];
  selectedWorkAreas: workAreaReducerState;
  manualBiddingPrices?: RateBidCollection | null;
  isPrimaryCategorySearchable: boolean;
  currentMaxBudget?: number;
  budgetBoostAmount?: number;
  campaignType?: CampaignTypeEnum;
};

export const GeographyApiSchema = z.object({
  type: nativeEnum(GeographyTypeApiEnum),
  value: z.string(),
  centroid: z
    .object({
      latitude: z.number().optional(),
      longitude: z.number().optional(),
    })
    .optional(),
  name: z.string().optional().nullable(),
});

export const SubCategoryDtoSchema = z.object({
  categoryId: z.number().int().positive(), // same as webCatId
  name: z.string().nullable(),
  isSelectedByDefault: z.boolean(),
});

export const ParentCategoryDtoSchema = z.object({
  parentCategoryId: z.number().int().positive(),
  name: z.string().nullable(),
  subCategories: z.array(SubCategoryDtoSchema).nullable(),
});

export const SubCategoryArrayApiSchema = z.array(SubCategoryApiSchema);
export const GeographyArrayApiSchema = z.array(GeographyApiSchema);
export const RateBidsArrayApiSchema = z.array(RateBidApiSchema).optional();
export const RateBidsCollectionSchema = z.record(RateBidApiSchema).nullish();
export type RateBidCollection = z.infer<typeof RateBidsCollectionSchema>;
export type RateBid = z.infer<typeof RateBidApiSchema>;
export type GeographyArrayApiType = z.infer<typeof GeographyArrayApiSchema>;
export type GeographyApiType = z.infer<typeof GeographyApiSchema>;
export type CampaignQuote = z.infer<typeof campaignQuoteSchema>;
