import { nativeEnum, z } from 'zod';
import { GeographyTypeApiEnum } from './schemas';

export enum CampaignAcceptedMembershipEnum {
  Ppl = 'Ppl',
  Alsp = 'Alsp',
}

export enum BiddingStrategyEnum {
  FullyAutomatic = 'FullyAutomatic',
  SemiAutomatic = 'SemiAutomatic',
  Manual = 'Manual',
}

const CategorySchema = z.object({
  categoryId: z.number().int().positive(),
});

const SubCategorySchema = z.object({
  categoryId: z.number().int().positive(),
  parentCategoryId: z.number().int().positive(),
});

const GeographySchema = z.object({
  type: nativeEnum(GeographyTypeApiEnum),
  value: z.string(),
});

const RateBidSchema = z.object({
  call: z.number().positive(),
  click: z.number().positive(),
  directory: z.number().positive(),
  message: z.number().positive(),
  requestAQuote: z.number().positive(),
  categoryId: z.number().int().positive(),
  subCategoryId: z.number().int().positive(),
});

const SubCategoryArraySchema = z.array(SubCategorySchema);
const GeographyArraySchema = z.array(GeographySchema);
const RateBidsArraySchema = z.array(RateBidSchema).optional();

export const CreateCampaignSchema = z.object({
  type: nativeEnum(CampaignAcceptedMembershipEnum),
  biddingStrategyType: nativeEnum(BiddingStrategyEnum),
  category: CategorySchema,
  subCategories: SubCategoryArraySchema,
  maxBudget: z.number().nonnegative(),
  budgetPeriod: z.string(),
  geographies: GeographyArraySchema,
  isActive: z.boolean(),
  rateBids: RateBidsArraySchema,
});
