import { z } from 'zod';
import { GeographyTypeApiEnum } from './schemas';

export const recommendedLocationsRequestBodySchema = z.object({
  geographies: z.array(
    z.object({
      value: z.string(),
      type: z.nativeEnum(GeographyTypeApiEnum),
    }),
  ),
  subCategories: z.array(
    z.object({
      categoryId: z.number(),
    }),
  ),
  category: z.object({
    categoryId: z.number(),
    isSearchable: z.boolean(),
  }),
});

export type RecommendedLocationsRequestBodyType = z.infer<
  typeof recommendedLocationsRequestBodySchema
>;
