import { z } from 'zod';
import { safeNativeEnum } from 'src/data/schemas/helpers/safeNativeEnum';

export enum LeadDisputeType {
  SPAM = 'Spam',
  OUT_OF_AREA = 'OutOfArea',
  WRONG_CATEGORY = 'WrongCategory',
  CONTACT_DETAILS = 'ContactDetails',
  DUPLICATE = 'Duplicate',
  OTHER = 'Other',
}

export const CreateLeadDisputeBatchItem = z.object({
  leadId: z.string(),
  leadDisputeType: safeNativeEnum(LeadDisputeType).default(
    LeadDisputeType.CONTACT_DETAILS,
  ),
  disputeReason: z.string().nullish(),
});

export const DisputeLeadApiSchema = z.object({
  items: z.array(CreateLeadDisputeBatchItem),
});

export type DisputeLeadBodyType = z.infer<typeof DisputeLeadApiSchema>;
