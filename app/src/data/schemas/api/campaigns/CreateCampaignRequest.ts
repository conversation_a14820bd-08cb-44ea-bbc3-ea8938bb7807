import { nativeEnum, z } from 'zod';
import {
  BiddingStrategyApiEnum,
  CampaignTypeEnum,
  CategoryApiSchema,
  GeographyArrayApiSchema,
  RateBidsArrayApiSchema,
  SubCategoryArrayApiSchema,
} from 'src/data/schemas/api/campaigns/schemas';

export const CreateCampaignApiSchema = z.object({
  type: nativeEnum(CampaignTypeEnum),
  biddingStrategyType: nativeEnum(BiddingStrategyApiEnum),
  category: CategoryApiSchema,
  subCategories: SubCategoryArrayApiSchema,
  maxBudget: z.number().nonnegative(),
  budgetPeriod: z.string(),
  geographies: GeographyArrayApiSchema,
  isActive: z.boolean(),
  rateBids: RateBidsArrayApiSchema,
});

export type CreateCampaignRequestBody = z.infer<typeof CreateCampaignApiSchema>;
