import { z } from 'zod';
import { safeNativeEnum } from '../../helpers/safeNativeEnum';
import {
  AmendmentEnum,
  BudgetRecalculationEnum,
  CompanySize,
  GeographyApiSchema,
  NewProductEnum,
  PplEnum,
  QuoteRequestBehaviour,
  QuoteRequestSource,
  QuoteRequestStatus,
  UpgradeEnum,
  campaignQuoteSchema,
  quoteCategorySchema,
} from './schemas';

export const quoteRequestResponseSchema = z.object({
  quoteRequestId: z.string(),
  behavior: safeNativeEnum(QuoteRequestBehaviour),
  dateCreated: z.string().datetime({ offset: true }),
  previousQuoteRequestId: z.string().nullable(),
  supersedingQuoteRequestId: z.string().nullable(),
  companyId: z.string().nullable(),
  status: safeNativeEnum(QuoteRequestStatus),
  selectedQuoteId: z.string().nullable(),
  quoteSet: z.array(campaignQuoteSchema),
  validTo: z.string().datetime({ offset: true }),
  source: safeNativeEnum(QuoteRequestSource),
  geographies: z.array(GeographyApiSchema),
  category: quoteCategorySchema,
  subCategories: z.array(quoteCategorySchema),
  companySize: safeNativeEnum(CompanySize),
  sourceCampaignId: z.string().nullable(),
  summary: z.object({
    amendment: safeNativeEnum(AmendmentEnum),
    budgetRecalculation: safeNativeEnum(BudgetRecalculationEnum),
    newProduct: safeNativeEnum(NewProductEnum),
    ppl: safeNativeEnum(PplEnum),
    upgrade: safeNativeEnum(UpgradeEnum),
  }),
});

export type QuoteRequestResponse = z.infer<typeof quoteRequestResponseSchema>;
