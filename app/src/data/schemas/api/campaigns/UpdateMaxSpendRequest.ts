import { z } from 'zod';

export const UpdateMaxSpendApiSchema = z.object({
  maxSpend: z
    .number()
    .describe(
      'Maximum amount the Trade is willing to pay for a campaign in a given budget period regardless of\r\nminimum commitment budget boosts',
    ),
  budgetPeriod: z.string(),
  isOneTimeTopUp: z
    .boolean()
    .optional()
    .describe(
      "If true and the following month's budget isn't explicitly set, then we need to take this month's budget and then\r\nclone it for next month. If this is false, it's just their way of saying \"here's the new max spend starting\r\nin this period and applying into the future until said otherwise.\"",
    ),
});

export type UpdateMaxSpendApiRequestBody = z.infer<
  typeof UpdateMaxSpendApiSchema
>;
