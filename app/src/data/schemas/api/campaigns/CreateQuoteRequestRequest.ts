import { nativeEnum, z } from 'zod';
import {
  CategoryApiSchema,
  CompanySize,
  GeographyArrayApiSchema,
  QuoteRequestSource,
  QuoteRequestType,
  SubCategoryArrayApiSchema,
} from './schemas';

export const CreateQuoteRequestRequestApiSchema = z.object({
  companyId: z.string().optional(),
  source: nativeEnum(QuoteRequestSource)
    .optional()
    .default(QuoteRequestSource.TradeApp),
  quoteRequestType: nativeEnum(QuoteRequestType)
    .optional()
    .default(QuoteRequestType.AdditionalProduct),
  geographies: GeographyArrayApiSchema,
  category: CategoryApiSchema,
  subCategories: SubCategoryArrayApiSchema,
  companySize: nativeEnum(CompanySize).optional(),
  previousQuoteRequestId: z.string().optional(),
  sourceCampaignId: z
    .string()
    .optional()
    .describe(
      'If this is an upgrade or a modification, this is the ID of the campaign that is being upgraded',
    ),
  sourceReferenceId: z.string().optional(),
});

export type CreateQuoteRequestRequestBody = z.infer<
  typeof CreateQuoteRequestRequestApiSchema
>;
