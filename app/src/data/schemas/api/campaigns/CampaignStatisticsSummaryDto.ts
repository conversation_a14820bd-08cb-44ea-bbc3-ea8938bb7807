import { z } from 'zod';
import { safeNativeEnum } from '../../helpers/safeNativeEnum';

export enum CampaignStatisticsSummaryLevel {
  Last7Days = 'Last7Days',
  Last30Days = 'Last30Days',
  Last90Days = 'Last90Days',
  BudgetPeriod = 'BudgetPeriod',
  LastBudgetPeriod = 'LastBudgetPeriod',
  Lifetime = 'Lifetime',
}

export const CampaignStatisticsSummaryDtoSchema = z.object({
  summaryLevel: safeNativeEnum(CampaignStatisticsSummaryLevel),
  days: z.number().int(),
  startDate: z.coerce.date(),
  endDate: z.coerce.date(),
  budgetPeriod: z.string().nullable(),
  balance: z.number(),
  counts: z.object({
    totalLeads: z.number().int().nonnegative(),
    click: z.number().int().nonnegative(),
    call: z.number().int().nonnegative(),
    directory: z.number().int().nonnegative(),
    message: z.number().int().nonnegative(),
    requestAQuote: z.number().int().nonnegative(),
  }),
});

export type CampaignStatisticsSummaryDto = z.infer<
  typeof CampaignStatisticsSummaryDtoSchema
>;
