import { generateMock } from '@anatine/zod-mock';
import {
  PriceRangesDtoPageResult,
  PriceRangesDtoPageResultType,
  PriceRangeType,
} from './PriceRangesDtoPageResult';

export const generatePriceRangesDtoPageResultMockData =
  (): PriceRangesDtoPageResultType => generateMock(PriceRangesDtoPageResult);

export const mockDefaultPriceRangesData = (): PriceRangesDtoPageResultType => {
  const prices = generatePriceRangesDtoPageResultMockData();
  prices.items = [
    {
      categoryId: 1,
      subCategoryId: 2,
      [PriceRangeType.MESSAGE]: {
        maxPrice: 1.0,
        minPrice: 1.5,
      },
      [PriceRangeType.CALL]: {
        maxPrice: 2.0,
        minPrice: 2.5,
      },
      [PriceRangeType.CLICK]: {
        maxPrice: 3.0,
        minPrice: 3.5,
      },
      [PriceRangeType.DIRECTORY]: {
        maxPrice: 4.0,
        minPrice: 4.5,
      },
      [PriceRangeType.REQUEST_A_QUOTE]: {
        maxPrice: 5.0,
        minPrice: 5.5,
      },
      averageRate: 3.2,
    },
  ];
  return prices;
};
