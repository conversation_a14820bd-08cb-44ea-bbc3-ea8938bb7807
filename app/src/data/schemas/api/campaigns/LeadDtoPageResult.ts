import { z } from 'zod';
import { safeNativeEnum } from 'src/data/schemas/helpers/safeNativeEnum';

export enum LeadType {
  LEAD = 'Lead',
  CLICK = 'Click',
}

export enum LeadChannelType {
  Call = 'Call', // Call leads (Mobile, Landline, SMS)
  Click = 'Click', // Web click leads
  Directory = 'Directory', // Directory leads
  Message = 'Message', // Direct message leads
  RequestAQuote = 'RequestAQuote', // Request a quote leads
}

export const CategoryDto = z.object({
  categoryId: z.number(),
  name: z.string().nullable(),
});

export const LeadDto = z.object({
  leadId: z.string(),
  isDisputed: z.boolean().optional(),
  leadType: safeNativeEnum(LeadType),
  dateGenerated: z.string().transform((dateString) => new Date(dateString)),
  channel: safeNativeEnum(LeadChannelType),
  category: CategoryDto,
  subCategory: CategoryDto.nullable(),
  campaignId: z.string(),
  disputeStatus: z.string().nullish(),
  price: z.number().optional(),
  postcode: z.string().nullish(),
  jobId: z.string().nullish(),
  secureContactLogId: z.string().nullish(),
  jobIdV2: z.string().nullish(),
  opportunityId: z.string().nullish(),
  budgetPeriod: z.string().transform((dateString) => new Date(dateString)),
  source: z.string().optional(),
});

export const LeadDtoPageResult = z.object({
  items: z.array(LeadDto),
  skip: z.number(),
  top: z.number(),
  count: z.number(),
});

export type LeadDtoType = z.infer<typeof LeadDto>;

export type LeadDtoPageResultType = z.infer<typeof LeadDtoPageResult>;
