import { z } from 'zod';
import { GeographyApiSchema } from './schemas';

export const recommendLocationSchema = z.object({
  geography: GeographyApiSchema,
  averageHouseholdIncome: z.number(),
  households: z.number(),
  leadsAvailable: z.number(),
  leadsConsumed: z.number(),
  population: z.number(),
  tradeCount: z.number(),
  demandScore: z.number(),
  demandClassification: z.enum(['Low', 'Middle', 'High']),
});

export const recommendedLocationsResponseSchema = z.object({
  items: z.array(recommendLocationSchema),
});

export type RecommendedLocationsResponse = z.infer<
  typeof recommendedLocationsResponseSchema
>;
export type RecommendedLocationApiType = z.infer<
  typeof recommendLocationSchema
>;
