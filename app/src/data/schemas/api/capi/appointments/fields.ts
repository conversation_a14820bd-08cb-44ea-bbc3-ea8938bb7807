import { CalendarEventStatus } from '@cat-home-experts/react-native-components';
import { z } from 'zod';

export const TITLE_MAX_LENGTH = 100;

export const AppointmentTypeCategory = {
  OTHER: 'OTHER',
  JOB_START: 'JOB_START',
} as const;
export type AppointmentTypeCategory =
  (typeof AppointmentTypeCategory)[keyof typeof AppointmentTypeCategory];

// Very weird
export const AppointmentQueryActive = {
  ALL: 'all',
  YES: 'yes',
} as const;
export type AppointmentQueryActive =
  (typeof AppointmentQueryActive)[keyof typeof AppointmentQueryActive];

export const appointmentParams = z.object({
  appointmentId: z.string().uuid(),
});

export const appointmentTypeTitleFieldSchema = z
  .string()
  .min(5)
  .max(TITLE_MAX_LENGTH);

export const appointmentTypeFieldRequestSchema = z.object({
  id: z.string().uuid(),
  customTitle: z.optional(appointmentTypeTitleFieldSchema),
});

export const appointmentTypeFieldResponseSchema = z.object({
  id: z.string().uuid(),
  title: z.string(),
  category: z.nativeEnum(AppointmentTypeCategory),
});

export const alternativeFieldRequestSchema = z.object({
  start: z.string().datetime(),
  end: z.optional(z.string().datetime()),
});

type Tuple<T extends string> = [T, ...T[]];

export const appointmentStatusFieldSchema = z.enum<
  CalendarEventStatus,
  Tuple<CalendarEventStatus>
>([
  CalendarEventStatus.CREATED,
  CalendarEventStatus.CANCELLED,
  CalendarEventStatus.REJECTED,
  CalendarEventStatus.ACCEPTED,
]);

export const alternativeFieldResponseSchema = z.object({
  id: z.string().uuid(),
  start: z.string().datetime(),
  end: z.optional(z.string().datetime()),
  status: appointmentStatusFieldSchema,
});

export const appointmentFieldSchema = z.object({
  id: z.string().uuid(),
  jobId: z.string().uuid(),
  consumerId: z.string().uuid(),
  companyId: z.number(),
  type: appointmentTypeFieldResponseSchema,
  alternatives: z.array(alternativeFieldResponseSchema).min(1).max(10),
  status: appointmentStatusFieldSchema,
});

export type AppointmentType = z.infer<typeof appointmentFieldSchema>;

// Annoying
export const jobAppointmentFieldSchema = appointmentFieldSchema
  .omit({ consumerId: true, companyId: true })
  .extend({ type: appointmentTypeFieldResponseSchema.omit({ id: true }) });

export type JobAppointmentType = z.infer<typeof jobAppointmentFieldSchema>;
