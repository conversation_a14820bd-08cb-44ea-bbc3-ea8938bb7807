import { getCollection } from 'src/services/firebase/firestore';
import type {
  CollectionSnapshot,
  DocumentData,
  GetCollectionOptions,
} from 'src/services/firebase/firestore.types';
import { captureException } from 'src/services/sentry';
import { createFirestoreErrorContext } from 'src/utilities/firestore';
import { findArraySchemaForCollection } from './collectionSchemas';

export const getCollectionWithSchema = async <
  T extends DocumentData = DocumentData,
>(
  options: GetCollectionOptions,
): Promise<CollectionSnapshot<T>> => {
  try {
    // find schema
    const schema = findArraySchemaForCollection<T>(options);
    if (!schema) {
      throw new Error(
        `No schema found for collection: ${JSON.stringify({
          collection: options.collection,
          sub: options.sub,
        })}`,
      );
    }

    // fetch data
    const response = await getCollection<T>(options);
    const data = response.data();
    const parsedData = data ? schema.parse(data) : undefined;
    return {
      ...response,
      data: () => parsedData,
    };
  } catch (error) {
    captureException(error, createFirestoreErrorContext(options));
    throw error;
  }
};
