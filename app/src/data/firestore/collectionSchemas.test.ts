import { z } from 'zod';
import {
  CollectionSchemaMap,
  collectionSchemas,
  findSchemaForCollection,
  getCollectionSchemasForApp,
  findSchemaInMap,
} from './collectionSchemas';
import { FirestoreCollection } from './collections';

describe('data/firestore | collectionSchemas | findSchemaForCollection', () => {
  test('that findSchemaForCollection returns top level collection schema', () => {
    const schema = findSchemaForCollection({
      collection: FirestoreCollection.Users,
    });
    expect(schema).toBeDefined();
  });

  test('that findSchemaForCollection returns sub collection schema', () => {
    const schema = findSchemaForCollection({
      collection: FirestoreCollection.Companies,
      doc: '123',
      sub: { collection: FirestoreCollection.Dashboard },
    });
    expect(schema).toBeDefined();
  });
});

describe('data/firestore | collectionSchemas | getCollectionSchemasForApp', () => {
  it('should return collectionSchemas by default', () => {
    const result = getCollectionSchemasForApp();
    expect(result).toBe(collectionSchemas);
  });
});

describe('data/firestore | collectionSchemas | findSchemaInMap', () => {
  const mockSchemaMap: CollectionSchemaMap = {
    random: z.object({}),
    users: {
      schema: z.object({}),
      sub: {
        dashboard: z.object({}),
      },
    },
    usersNested2: {
      schema: z.object({}),
      sub: {
        preferences: {
          schema: z.object({}),
          sub: {
            thirdParty: z.object({}),
          },
        },
      },
    },
    usersNested3: {
      schema: z.object({}),
      sub: {
        preferences: {
          schema: z.object({}),
          sub: {
            thirdParty: {
              schema: z.object({}),
              sub: {
                fourthParty: z.object({}),
              },
            },
          },
        },
      },
    },
  };
  test('that findSchemaForCollection returns top level collection schema', () => {
    const schema = findSchemaInMap(
      { collection: 'random' as FirestoreCollection },
      mockSchemaMap,
    );
    expect(schema).toBeDefined();
  });

  test('that findSchemaForCollection returns sub collection schema', () => {
    const schema = findSchemaInMap(
      {
        collection: FirestoreCollection.Users,
        doc: '123',
        sub: { collection: FirestoreCollection.Dashboard },
      },
      mockSchemaMap,
    );
    expect(schema).toBeDefined();
  });

  test('that findSchemaForCollection returns sub->sub collection schema', () => {
    const schema = findSchemaInMap(
      {
        collection: 'usersNested2' as FirestoreCollection,
        doc: '123',
        sub: {
          collection: 'preferences' as FirestoreCollection,
          doc: '123',
          sub: {
            collection: 'thirdParty' as FirestoreCollection,
          },
        },
      },
      mockSchemaMap,
    );
    expect(schema).toBeDefined();
  });

  test('that findSchemaForCollection returns sub->sub->sub collection schema', () => {
    const schema = findSchemaInMap(
      {
        collection: 'usersNested3' as FirestoreCollection,
        doc: '123',
        sub: {
          collection: 'preferences' as FirestoreCollection,
          doc: '123',
          sub: {
            collection: 'thirdParty' as FirestoreCollection,
            doc: '123',
            sub: {
              collection: 'fourthParty' as FirestoreCollection,
            },
          },
        },
      },
      mockSchemaMap,
    );
    expect(schema).toBeDefined();
  });
});
