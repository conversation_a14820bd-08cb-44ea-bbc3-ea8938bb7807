import { z } from 'zod';
import { getCollection } from 'src/services/firebase/firestore';
import type {
  CollectionSnapshot,
  GetCollectionOptions,
} from 'src/services/firebase/firestore.types';
import { captureException } from 'src/services/sentry';
import { FirestoreCollection } from './collections';
import { getCollectionWithSchema } from './firestoreWithSchema';
import { findArraySchemaForCollection } from './collectionSchemas';

jest.mock('src/services/firebase/firestore', () => ({
  getCollection: jest.fn(),
}));

jest.mock('./collectionSchemas', () => ({
  findArraySchemaForCollection: jest.fn(),
}));

jest.mock('src/services/sentry', () => ({
  captureException: jest.fn(),
}));

describe('data | firestore | firestoreWithSchema', () => {
  const dummyData = [
    { id: '1', name: 'Company 1' },
    { id: '2', name: 'Company 2' },
    { id: '3', name: 'Company 3' },
  ];

  beforeEach(() => {
    (getCollection as jest.Mock).mockResolvedValue({
      data: () => dummyData,
      empty: false,
      size: dummyData.length,
    } satisfies CollectionSnapshot);

    (findArraySchemaForCollection as jest.Mock).mockReturnValue(
      z.array(z.any()),
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  test('should fetch a firestore collection & parse with a schema', async () => {
    const options: GetCollectionOptions = {
      collection: FirestoreCollection.Companies,
      doc: '123',
    };
    const result = await getCollectionWithSchema(options);

    expect(result.data()).toStrictEqual(dummyData);
    expect(getCollection).toHaveBeenNthCalledWith(1, options);
    expect(findArraySchemaForCollection).toHaveBeenNthCalledWith(1, options);
  });

  test('should throw an error if no schema is found', async () => {
    (findArraySchemaForCollection as jest.Mock).mockReturnValue(undefined);
    const options: GetCollectionOptions = {
      collection: FirestoreCollection.Companies,
    };
    const errorText = `No schema found for collection: ${JSON.stringify({
      collection: options.collection,
      sub: undefined,
    })}`;

    await expect(getCollectionWithSchema(options)).rejects.toThrow(errorText);
    expect(captureException).toHaveBeenCalledTimes(1);
    expect(captureException).toHaveBeenCalledWith(new Error(errorText), {
      extra: { collection: 'companies' },
    });
  });

  test('should throw an error from getCollection', async () => {
    const error = new Error('Error fetching collection');
    (getCollection as jest.Mock).mockRejectedValue(error);

    const options: GetCollectionOptions = {
      collection: FirestoreCollection.Companies,
    };
    await expect(getCollectionWithSchema(options)).rejects.toThrow(error);
    expect(captureException).toHaveBeenCalledTimes(1);
    expect(captureException).toHaveBeenCalledWith(error, {
      extra: { collection: 'companies' },
    });
  });

  test('should fetch a firestore collection & parse with a schema for a subcollection', async () => {
    const options: GetCollectionOptions = {
      collection: FirestoreCollection.Companies,
      sub: {
        collection: FirestoreCollection.Albums,
        doc: '123',
      },
    };
    const result = await getCollectionWithSchema(options);

    expect(result.data()).toStrictEqual(dummyData);
    expect(getCollection).toHaveBeenNthCalledWith(1, options);
    expect(findArraySchemaForCollection).toHaveBeenNthCalledWith(1, options);
  });

  test('should throw on zod error', async () => {
    const options: GetCollectionOptions = {
      collection: FirestoreCollection.Companies,
      doc: '123',
    };
    (findArraySchemaForCollection as jest.Mock).mockReturnValue(
      z.array(z.string()),
    );

    await expect(getCollectionWithSchema(options)).rejects.toThrow(
      expect.any(z.ZodError),
    );
    expect(captureException).toHaveBeenCalledTimes(1);
    expect(captureException).toHaveBeenCalledWith(expect.any(z.ZodError), {
      extra: { collection: 'companies', doc: '123' },
    });
  });
});
