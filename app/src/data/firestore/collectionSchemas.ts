import { ZodA<PERSON>y, ZodType, ZodTypeAny, z } from 'zod';

import type {
  DocumentData,
  FirestoreRefOptions,
} from 'src/services/firebase/firestore.types';
import { PostcodeArea, RegionSchema } from 'src/data/schemas/firestore/regions';
import { SubcontractingSchema } from 'src/data/schemas/firestore/subcontracting';
import { FirestoreCollection } from './collections';

import { AccreditationSchema } from '../schemas/firestore/accreditations';
import { AvailabilitySchema } from '../schemas/firestore/availability';
import { CategorySchema } from '../schemas/firestore/categories';
import {
  AlbumSchema,
  CompaniesSchema,
  CompanyAccreditationSchema,
  DashboardSchema,
  ServicesSchema,
} from '../schemas/firestore/companies';
import { CompanySchema } from '../schemas/firestore/company';
import { MembershipSwitchSchema } from '../schemas/firestore/membershipSwitch';
import { ProfileSchema } from '../schemas/firestore/profile';
import { QuoteSchema } from '../schemas/firestore/quotes';
import { UserSchema } from '../schemas/firestore/users';

type SubSchemaValue<CollectionName extends string = string> = {
  schema: undefined | ZodTypeAny;
  sub: CollectionSchemaMap<CollectionName>;
};

type SchemaValue<CollectionName extends string = string> =
  | undefined
  | ZodTypeAny
  | SubSchemaValue<CollectionName>;

export type CollectionSchemaMap<CollectionName extends string = string> =
  Partial<Record<CollectionName, SchemaValue<CollectionName>>>;

// Use a dynamic import to allow code splitting
export const collectionSchemas: CollectionSchemaMap<FirestoreCollection> = {
  [FirestoreCollection.Accreditations]: AccreditationSchema,
  [FirestoreCollection.Availability]: AvailabilitySchema,
  [FirestoreCollection.Categories]: CategorySchema,
  [FirestoreCollection.Companies]: {
    schema: CompaniesSchema,
    sub: {
      [FirestoreCollection.Albums]: AlbumSchema,
      [FirestoreCollection.CompanyAccreditations]: CompanyAccreditationSchema,
      [FirestoreCollection.Dashboard]: DashboardSchema,
      [FirestoreCollection.Quotes]: QuoteSchema,
    },
  },
  [FirestoreCollection.Company]: CompanySchema,
  [FirestoreCollection.MembershipSwitch]: MembershipSwitchSchema,
  [FirestoreCollection.PostcodeAreas]: PostcodeArea,
  [FirestoreCollection.Profile]: ProfileSchema,
  [FirestoreCollection.Regions]: RegionSchema,
  [FirestoreCollection.Services]: ServicesSchema,
  [FirestoreCollection.Subcontracting]: SubcontractingSchema,
  [FirestoreCollection.Users]: UserSchema,
};

const isSubSchema = <CollectionName extends string = string>(
  schema: SchemaValue<CollectionName>,
): schema is SubSchemaValue<CollectionName> =>
  typeof (schema as SubSchemaValue)?.sub === 'object';

export const findSchemaInMap = <DocumentType extends DocumentData>(
  options: FirestoreRefOptions,
  collectionSchema: CollectionSchemaMap,
): ZodType<DocumentType> | undefined => {
  const schema = collectionSchema[options.collection];

  if (isSubSchema(schema)) {
    if (options.sub) {
      return findSchemaInMap(options.sub, schema.sub);
    } else {
      return schema.schema as ZodType<DocumentType> | undefined;
    }
  }

  return schema as ZodType<DocumentType> | undefined;
};

export const getCollectionSchemasForApp = (): CollectionSchemaMap =>
  collectionSchemas;

export const findSchemaForCollection = <DocumentType extends DocumentData>(
  options: FirestoreRefOptions,
): ZodType<DocumentType> | undefined => {
  const collectionSchemasForApp = getCollectionSchemasForApp();
  return findSchemaInMap(options, collectionSchemasForApp);
};

export const findArraySchemaForCollection = <DocumentType extends DocumentData>(
  options: FirestoreRefOptions,
): ZodArray<ZodType<DocumentType>> | undefined => {
  const singleDocSchema = findSchemaForCollection<DocumentType>(options);
  return singleDocSchema ? z.array(singleDocSchema) : undefined;
};
