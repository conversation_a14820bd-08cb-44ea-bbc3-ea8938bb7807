/* eslint-disable @typescript-eslint/no-explicit-any */
import * as Application from 'expo-application';
import * as Device from 'expo-device';
import { GIT_SHA } from 'src/services/dotenv';
import axios from 'axios';
import type {
  AxiosRequestConfig,
  AxiosResponse,
  CreateAxiosDefaults,
  Method as AxiosMethod,
  AxiosHeaderValue,
} from 'axios';
import { IS_WEB } from 'src/constants';

// settings & const
const DEFAULT_HEADERS = {
  ...(!IS_WEB
    ? {
        'X-CAT-User-Agent': `Checkatrade/v${Application.nativeBuildVersion}(${GIT_SHA}) ${Device.deviceName}/${Device.osName}/${Device.osVersion}`,
      }
    : {}),
  'Content-Type': 'application/json' as const,
};
const TRAILING_SLASH_RE = /\/*$/;

/** options for each client instance */
interface ClientOptions {
  /** set the common root URL for all API requests */
  baseUrl?: string;
  axiosConfig?: CreateAxiosDefaults;
  getBearerToken?: (forceRefresh?: boolean) => Promise<string | null>;
}
interface BaseParams {
  params?: { query?: Record<string, unknown>; header?: Header };
}

type PathItemObject = { [M in HttpMethod]: OperationObject } & {
  parameters?: any;
};
interface OperationObject {
  parameters: any;
  requestBody: any; // note: "any" will get overridden in inference
  responses: any;
}
type HttpMethod = AxiosMethod;
type OkStatus = 200 | 201 | 202 | 203 | 204 | 206 | 207;

// util
/** Get a union of paths which have method */
type PathsWith<
  Paths extends Record<string, PathItemObject>,
  PathnameMethod extends HttpMethod,
> = {
  [Pathname in keyof Paths]: Paths[Pathname] extends {
    [K in PathnameMethod]: any;
  }
    ? Pathname
    : never;
}[keyof Paths];
/** Find first match of multiple keys */
type FilterKeys<Obj, Matchers> = {
  [K in keyof Obj]: K extends Matchers ? Obj[K] : never;
}[keyof Obj];
/** handle "application/json", "application/vnd.api+json", "appliacation/json;charset=utf-8" and more */
type JSONLike = `${string}json${string}`;

// general purpose types
type Header = Record<string, AxiosHeaderValue>;
type Params<O> = O extends { parameters: any }
  ? { params: NonNullable<O['parameters']> }
  : BaseParams;
type RequestBodyObj<O> = O extends { requestBody?: any }
  ? O['requestBody']
  : never;
type RequestBodyContent<O> =
  undefined extends RequestBodyObj<O>
    ? FilterKeys<NonNullable<RequestBodyObj<O>>, 'content'> | undefined
    : FilterKeys<RequestBodyObj<O>, 'content'>;
type RequestBodyJSON<O> =
  FilterKeys<RequestBodyContent<O>, JSONLike> extends never
    ? FilterKeys<NonNullable<RequestBodyContent<O>>, JSONLike> | undefined
    : FilterKeys<RequestBodyContent<O>, JSONLike>;
type RequestBody<O> =
  undefined extends RequestBodyJSON<O>
    ? { body?: RequestBodyJSON<O> }
    : { body: RequestBodyJSON<O> };
type QuerySerializer<O> = (
  query: O extends { parameters: any }
    ? NonNullable<O['parameters']['query']>
    : Record<string, unknown>,
) => string;
export type RequestOptions<T> = Params<T> &
  RequestBody<T> & { querySerializer?: QuerySerializer<T> };
type Success<O> = FilterKeys<FilterKeys<O, OkStatus>, 'content'>;

// fetch types
type AxiosApiRequestConfig = Omit<
  AxiosRequestConfig,
  'url' | 'method' | 'baseURL' | 'params'
>;

/** Call URLSearchParams() on the object, but remove `undefined` and `null` params */
export function defaultSerializer(q: unknown): string {
  const search = new URLSearchParams();
  if (q && typeof q === 'object') {
    for (const [k, v] of Object.entries(q)) {
      if (v === undefined || v === null) {
        continue;
      }

      search.set(k, String(v));
    }
  }

  return search.toString();
}

/** Construct URL string from baseUrl and handle path and query params */
export function createFinalURL<O>(
  url: string,
  options: {
    baseUrl?: string;
    params: { query?: Record<string, unknown>; path?: Record<string, unknown> };
    querySerializer: QuerySerializer<O>;
  },
): string {
  let finalURL = `${
    options.baseUrl ? options.baseUrl.replace(TRAILING_SLASH_RE, '') : ''
  }${url as string}`;
  if (options.params.path) {
    for (const [k, v] of Object.entries(options.params.path)) {
      finalURL = finalURL.replace(`{${k}}`, encodeURIComponent(String(v)));
    }
  }

  if (options.params.query) {
    const search = options.querySerializer(options.params.query as any);
    if (search) {
      finalURL += `?${search}`;
    }
  }

  return finalURL;
}

type ResponseData<T> = T extends { responses: any }
  ? NonNullable<FilterKeys<Success<T['responses']>, JSONLike>>
  : unknown;

type ApiMethodFunction<
  Paths extends Record<string, PathItemObject>,
  M extends HttpMethod,
> = <P extends PathsWith<Paths, M>>(
  url: P,
  requestOptions: RequestOptions<FilterKeys<Paths[P], M>>,
  axiosRequestConfig?: AxiosApiRequestConfig,
) => Promise<
  AxiosResponse<ResponseData<M extends keyof Paths[P] ? Paths[P][M] : unknown>>
>;

export function createApiClient<Paths extends NonNullable<unknown>>(
  clientOptions: ClientOptions = {},
): {
  get: ApiMethodFunction<Paths, 'get'>;
  put: ApiMethodFunction<Paths, 'put'>;
  post: ApiMethodFunction<Paths, 'post'>;
  delete: ApiMethodFunction<Paths, 'delete'>;
  patch: ApiMethodFunction<Paths, 'patch'>;
  head: ApiMethodFunction<Paths, 'head'>;
  options: ApiMethodFunction<Paths, 'options'>;
} {
  const axiosInstance = axios.create({
    ...clientOptions.axiosConfig,
    headers: DEFAULT_HEADERS,
  });
  const { baseUrl, getBearerToken } = clientOptions;
  // set Authorization header for all requests
  if (getBearerToken) {
    axiosInstance.interceptors.request.use(async (config) => {
      // Skip if a Authorization header is already set with the request
      if (config.headers.Authorization) {
        return config;
      }

      const bearerToken = await getBearerToken();
      if (!bearerToken) {
        throw new Error('getBearerToken() must return a token');
      }

      config.headers.Authorization = `Bearer ${bearerToken}`;
      return config;
    });

    axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const { config } = error;

        if (!config) {
          return Promise.reject(error);
        }

        if (error.response?.status === 401 && !config._retry) {
          config._retry = true;

          // force refresh token
          const bearerToken = await getBearerToken(true);
          if (!bearerToken) {
            throw new Error('getBearerToken() must return a token');
          }

          config.headers.Authorization = `Bearer ${bearerToken}`;

          return axiosInstance.request(config);
        }

        return Promise.reject(error);
      },
    );
  }

  function getFinalUrl<P extends keyof Paths, M extends HttpMethod>(
    url: P,
    requestOptions: RequestOptions<
      FilterKeys<Paths[P] extends { parameters: any } ? Paths[P] : never, M>
    >,
  ): string {
    const { params = {}, querySerializer = defaultSerializer } = requestOptions;
    return createFinalURL(url as string, {
      baseUrl,
      params,
      querySerializer,
    });
  }

  function mergeHeaderIntoAxiosRequestConfig(
    axiosRequestConfig: AxiosApiRequestConfig | undefined,
    requestHeaders: Header | undefined,
  ): AxiosApiRequestConfig | undefined {
    if (!requestHeaders) {
      return axiosRequestConfig;
    }

    return {
      ...axiosRequestConfig,
      headers: {
        ...axiosRequestConfig?.headers,
        ...requestHeaders,
      },
    };
  }

  return {
    /** Call a GET endpoint */
    async get<P extends PathsWith<Paths, 'get'>>(
      url: P,
      requestOptions: RequestOptions<FilterKeys<Paths[P], 'get'>>,
      axiosRequestConfig?: AxiosApiRequestConfig,
    ) {
      return axiosInstance.get(
        getFinalUrl<P, 'get'>(url, requestOptions as any),
        mergeHeaderIntoAxiosRequestConfig(
          axiosRequestConfig,
          (requestOptions as BaseParams).params?.header,
        ),
      );
    },
    /** Call a PUT endpoint */
    async put<P extends PathsWith<Paths, 'put'>>(
      url: P,
      requestOptions: RequestOptions<FilterKeys<Paths[P], 'put'>>,
      axiosRequestConfig?: AxiosApiRequestConfig,
    ) {
      return axiosInstance.put(
        getFinalUrl<P, 'put'>(url, requestOptions as any),
        requestOptions.body,
        mergeHeaderIntoAxiosRequestConfig(
          axiosRequestConfig,
          (requestOptions as BaseParams).params?.header,
        ),
      );
    },
    /** Call a POST endpoint */
    async post<P extends PathsWith<Paths, 'post'>>(
      url: P,
      requestOptions: RequestOptions<FilterKeys<Paths[P], 'post'>>,
      axiosRequestConfig?: AxiosApiRequestConfig,
    ) {
      return axiosInstance.post(
        getFinalUrl<P, 'post'>(url, requestOptions as any),
        requestOptions.body,
        mergeHeaderIntoAxiosRequestConfig(
          axiosRequestConfig,
          (requestOptions as BaseParams).params?.header,
        ),
      );
    },
    /** Call a DELETE endpoint */
    async delete<P extends PathsWith<Paths, 'delete'>>(
      url: P,
      requestOptions: RequestOptions<FilterKeys<Paths[P], 'delete'>>,
      axiosRequestConfig?: AxiosApiRequestConfig,
    ) {
      return axiosInstance.delete(
        getFinalUrl<P, 'delete'>(url, requestOptions as any),
        mergeHeaderIntoAxiosRequestConfig(
          axiosRequestConfig,
          (requestOptions as BaseParams).params?.header,
        ),
      );
    },
    /** Call a OPTIONS endpoint */
    async options<P extends PathsWith<Paths, 'options'>>(
      url: P,
      requestOptions: RequestOptions<FilterKeys<Paths[P], 'options'>>,
      axiosRequestConfig?: AxiosApiRequestConfig,
    ) {
      return axiosInstance.options(
        getFinalUrl<P, 'options'>(url, requestOptions as any),
        mergeHeaderIntoAxiosRequestConfig(
          axiosRequestConfig,
          (requestOptions as BaseParams).params?.header,
        ),
      );
    },
    /** Call a HEAD endpoint */
    async head<P extends PathsWith<Paths, 'head'>>(
      url: P,
      requestOptions: RequestOptions<FilterKeys<Paths[P], 'head'>>,
      axiosRequestConfig?: AxiosApiRequestConfig,
    ) {
      return axiosInstance.head(
        getFinalUrl<P, 'head'>(url, requestOptions as any),
        mergeHeaderIntoAxiosRequestConfig(
          axiosRequestConfig,
          (requestOptions as BaseParams).params?.header,
        ),
      );
    },
    /** Call a PATCH endpoint */
    async patch<P extends PathsWith<Paths, 'patch'>>(
      url: P,
      requestOptions: RequestOptions<FilterKeys<Paths[P], 'patch'>>,
      axiosRequestConfig?: AxiosApiRequestConfig,
    ) {
      return axiosInstance.patch(
        getFinalUrl<P, 'patch'>(url, requestOptions as any),
        requestOptions.body,
        mergeHeaderIntoAxiosRequestConfig(
          axiosRequestConfig,
          (requestOptions as BaseParams).params?.header,
        ),
      );
    },
  };
}
