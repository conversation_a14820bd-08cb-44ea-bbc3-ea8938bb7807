/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/v1/trades/{companyId}/adjustments/{adjustmentId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Get an Adjustment for the given companyId and adjustmentId. Returns the adjustmentDTO. */
    get: operations['GetAdjustment'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/adjustments': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Get the paged list of Adjustments for the given companyId. Returns a paged list of adjustments. */
    get: operations['GetAdjustments'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/bulk/campaigns/recalculateBudgets': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Recalculate budgets in bulk */
    get: operations['RecalculateCampaignBudgets'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/bulk/campaigns/refresh': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Trigger a "refresh" of all active campaigns by issuing a `CampaignUpdated` or `CampaignBalanceUpdated` message */
    get: operations['RefreshCampaigns'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/bulk/campaigns/recalculate': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Recalculates the Lead Count, Click Count, and Balance of all active or inactive (if updated in the last 2 months)
     *     campaigns for all (or companyId) trades. */
    get: operations['RecalculateCampaigns'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['GetCampaigns'];
    put?: never;
    /** Create a new Campaign for the given companyId. Returns the new campaign's unique identifier. */
    post: operations['CreateCampaign'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['GetCampaign'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/statistics': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Retrieve summary statistics for this campaign including lead counts by channel */
    get: operations['GetCampaignStatistics'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/priceRanges': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['GetPriceRanges'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/confirm': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Record that a Trade has confirmed changes to a Campaign */
    post: operations['ConfirmCampaign'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateSubCategories': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Update the sub-categories of an existing campaign. Note that you cannot change the campaign's category. */
    post: operations['UpdateSubCategories'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateMaxSpend': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Update the Campaign's budget relative to the maximum amount the Trade is willing to spend in a given budget period. This
     *     endpoint does not require knowledge of Minimum Commitment or Budget Boost Amounts (beyond validation requirements).
     * @description When Role Based access filters are applied, this should be accessible to Trades (and UpdateBudget should not)
     */
    post: operations['UpdateMaxSpend'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateRateBids': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Update the Campaign's manual bidding strategy's individual bids */
    post: operations['UpdateRateBids'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateGeographies': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Update the Campaign's assigned geographies */
    post: operations['UpdateGeographies'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateAvailability': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Update the Campaign's availability by pausing until a set date or un-pausing.
     *
     *     To pause a campaign indefinitely, use UpdateStatus to make it inactive. */
    post: operations['UpdateAvailability'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateStatus': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Update the Campaign's active, directory opt-out, and RaQ opt-out statuses */
    post: operations['UpdateStatus'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateParentCategory': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Update the Campaign's parent category's attributes */
    post: operations['UpdateParentCategory'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/updateBiddingStrategy': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Update the Campaign's Bidding Strategy Type from Manual to Fully Automatic */
    post: operations['UpdateBiddingStrategy'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/categories/{parentCategoryId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Get a Parent Category by Id. Includes display names and all Sub-Categories. */
    get: operations['GetCategoryById'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/auth/tradeApp': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Creates a token for trade app to read Campaigns firestore */
    post: operations['TradeAppFirebaseToken'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/leadDisputes/{leadDisputeId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['GetLeadDispute'];
    put?: never;
    post: operations['UpdateLeadDispute'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/leadDisputes': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['GetLeadDisputes'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/leadDisputes/batch': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['CreateLeadDisputes'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/leads/{leadId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['GetLeadById'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/leads': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations['GetLeads'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/leads/export': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations['ExportLeads'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/quoteRequests': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Creates a new Quote Request. If a Previous Quote Request Id is specified, it will be set as "Superseded." */
    post: operations['CreateQuoteRequest'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/quoteRequests/{quoteRequestId}/refresh': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** After a Quote Request has expired, refresh it using all the same inputs as the original. This will create a new
     *     Quote Request with a "Previous Quote Request ID" that links to the expired one.
     *
     *     If the provided Quote Request isn't already expired, this will set it to "Superseded" (thus preventing it from being used).
     *
     *     Any previously "selected" or re-forecasted budget will not be persisted. */
    post: operations['RefreshRequestForQuote'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/quoteRequests/{quoteRequestId}/selectQuote': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Register which Quote on a Quote Request a Trade selected if the selection happens before converting. This can be
     *     changed multiple times. */
    post: operations['SelectQuote'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/quoteRequests/{quoteRequestId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Gets an existing Quote Request by ID. This can be used to get the details of the Quote Request, check whether
     *     it is expired, or see if a quote has been "selected." */
    get: operations['GetQuoteRequest'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/quoteRequests/{quoteRequestId}/quotes/{quoteId}/forecasts': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Gets all the lead forecast(s) generated for a specific Quote of a Quote Request. Non-PPL Quotes will
     *     only have one forecast. */
    get: operations['GetQuoteLeadForecasts'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/quoteRequests/{quoteRequestId}/quotes/{quoteId}/convert': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Converts a Quote Request's Quote to an actual campaign. The Quote Request's status becomes "Converted" and can no longer
     *     be refreshed, forecasted, selected, etc. */
    post: operations['ConvertQuote'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/quoteRequests/{quoteRequestId}/quotes/{quoteId}/reforecast': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** For a PPL Quote, get a new lead forecast for the given budget selection. When selecting or convert a PPL quote, the
     *     selected budget must have been forecast, rounding up to 10 (and thus presented to the user). */
    post: operations['ReforecastQuote'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/regions': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Get a paged list of Regions, which contain Postcode Areas and Postcode Districts */
    get: operations['GetRegions'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/regions/recommendations': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Get insights of recommended locations for specific geographies (supports both Postcode Districts and Postcode Areas). Currently
     *     returns up to 10 Postcode Districts, ordered by highest Demand Score.
     * @description Currently returns all districts of provided area and all but provided district from the same area.
     */
    post: operations['CreateRegionalRecommendations'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/upgradeOptions': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Create an upgrade Quote Request based on the given Fixed campaign */
    post: operations['CreateUpgradeOptions'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/trades/{companyId}/campaigns/{campaignId}/upgrade': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Upgrade the Fixed campaign based on the given upgrade Quote Request */
    post: operations['UpgradeCampaign'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: {
    AdjustmentDto: {
      adjustmentId?: string | null;
      companyId?: string | null;
      /** Format: double */
      amount?: number;
      adjustmentType?: components['schemas']['AdjustmentTypeDto'];
      comment?: string | null;
      description?: string | null;
      /** Format: date-time */
      dateCreated?: string;
      /** Format: date-time */
      dateUpdated?: string;
    };
    AdjustmentDtoPageResult: {
      /** @description Gets the collection of entities for this page. */
      items?: components['schemas']['AdjustmentDto'][] | null;
      /** Format: int32 */
      skip?: number | null;
      /** Format: int32 */
      top?: number | null;
      /**
       * Format: int32
       * @description Gets the total count of items in the result set
       */
      count?: number;
    };
    /** @enum {string} */
    AdjustmentTypeDto: 'LeadDisputes';
    /** @enum {string} */
    BiddingStrategyTypeDto: 'FullyAutomatic' | 'SemiAutomatic' | 'Manual';
    BudgetAndBalanceDto: {
      /** Format: date */
      period?: string;
      /** Format: date */
      periodStartDate?: string;
      /** Format: date */
      periodEndDate?: string;
      /** Format: double */
      maxBudget?: number;
      /** Format: double */
      maxSpend?: number;
      /** Format: double */
      balance?: number;
      /** Format: double */
      threshold?: number;
      /** Format: int32 */
      leadCount?: number;
      /** Format: int32 */
      clickCount?: number;
      /** Format: int32 */
      cumulativeLeadCount?: number;
      /** Format: int32 */
      daysRemainingInPeriod?: number;
      /** Format: double */
      leadCommitmentThreshold?: number;
      /** Format: double */
      budgetBoostAmount?: number;
      isMinimumCommitmentActive?: boolean;
      /** Format: double */
      minimumCommitmentAmount?: number;
      /** Format: date */
      minimumCommitmentUntil?: string;
    };
    CampaignAmendmentDto: {
      /** @description The geographies this campaign should be updated to use */
      geographies: components['schemas']['GeographyDto'][];
      category: components['schemas']['CategoryDto'];
      /** @description One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Dtos.CampaignAmendmentDto.Category) this campaign should be updated to use */
      subCategories: components['schemas']['CategoryDto'][];
    };
    /** @enum {string} */
    CampaignAttributeUpdateTypeDto: 'Add' | 'Replace' | 'Remove';
    /** @description Boilerplate bulk handling message for Cathex.Campaigns.Contracts.PubSub.CampaignRefreshBulkRequestMessage. */
    CampaignBulkResponseMessage: {
      correlationId?: string | null;
      originator?: string | null;
      statusCode?: components['schemas']['PubSubResponseStatusCode'];
      /** Format: int32 */
      count?: number;
    };
    /** @description Information for a Campaign's Parent Category */
    CampaignCategoryDto: {
      /**
       * Format: int32
       * @description The unique identifier for the category
       */
      categoryId?: number;
      /** @description Should the trade show in search results for this category? */
      isSearchable?: boolean;
      /** @description The name of this parent category */
      name?: string | null;
    };
    CampaignCountsDto: {
      /**
       * Format: int32
       * @description Total count of Leads (which is the sum of all channels except for click)
       */
      totalLeads?: number;
      /**
       * Format: int32
       * @description Total count of Clicks
       */
      click?: number;
      /**
       * Format: int32
       * @description Total call leads
       */
      call?: number;
      /**
       * Format: int32
       * @description Total directory call leads
       */
      directory?: number;
      /**
       * Format: int32
       * @description Total direct message leads
       */
      message?: number;
      /**
       * Format: int32
       * @description Total Request a Quote leads
       */
      requestAQuote?: number;
    };
    CampaignDto: {
      campaignId?: string | null;
      companyId?: string | null;
      /**
       * @description Describes the type of campaign. Different campaign types have different traits.
       * @enum {string}
       */
      campaignType?:
        | 'Ppl'
        | 'Alsp'
        | 'Fixed'
        | 'Sponsored'
        | 'MdpSponsoredSearch'
        | 'MdpSponsoredDisplay';
      isActive?: boolean;
      isPaused?: boolean;
      /** Format: date */
      pausedUntil?: string | null;
      /** Format: date */
      startDate?: string;
      category?: components['schemas']['CategoryDto'];
      subCategories?: components['schemas']['CategoryDto'][] | null;
      geographies?: components['schemas']['GeographyDto'][] | null;
      budgetsAndBalances?: {
        [key: string]: components['schemas']['BudgetAndBalanceDto'];
      } | null;
      /** Format: date */
      currentBudgetPeriod?: string;
      /** Format: int32 */
      budgetCycleDay?: number;
      currentBudgetAndBalance?: components['schemas']['BudgetAndBalanceDto'];
      /** Format: double */
      futureMonthMaxBudget?: number;
      fixed?: components['schemas']['FixedCampaignDetailsDto'];
      sponsorship?: components['schemas']['SponsorshipCampaignDetailsDto'];
      ppl?: components['schemas']['PplCampaignDetailsDto'];
      /** Format: date-time */
      dateCreated?: string;
      /** Format: date-time */
      dateUpdated?: string;
      /** Format: date-time */
      dateBalanceLastUpdated?: string;
      createdBy?: string | null;
      updatedBy?: string | null;
    };
    CampaignDtoPageResult: {
      /** @description Gets the collection of entities for this page. */
      items?: components['schemas']['CampaignDto'][] | null;
      /** Format: int32 */
      skip?: number | null;
      /** Format: int32 */
      top?: number | null;
      /**
       * Format: int32
       * @description Gets the total count of items in the result set
       */
      count?: number;
    };
    /** @description Boilerplate bulk handling message for Cathex.Campaigns.Contracts.PubSub.CampaignRefreshBulkRequestMessage. */
    CampaignRefreshBulkResponseMessage: {
      correlationId?: string | null;
      originator?: string | null;
      statusCode?: components['schemas']['PubSubResponseStatusCode'];
      /** Format: int32 */
      count?: number;
    };
    CampaignStatisticSummaryDto: {
      /**
       * @description Aggregation level of this summary
       * @enum {string}
       */
      summaryLevel?:
        | 'Last7Days'
        | 'Last30Days'
        | 'Last90Days'
        | 'BudgetPeriod'
        | 'LastBudgetPeriod'
        | 'Lifetime';
      /**
       * Format: int32
       * @description Total days in this summary period
       */
      days?: number;
      /**
       * Format: date
       * @description Earliest day in this summary period
       */
      startDate?: string;
      /**
       * Format: date
       * @description Last day (inclusive) in this summary period
       */
      endDate?: string;
      /**
       * Format: date
       * @description If the summary is by budget period, this is the budget period summarised
       */
      budgetPeriod?: string | null;
      /**
       * Format: double
       * @description Total balance for this summary period
       */
      balance?: number;
      counts?: components['schemas']['CampaignCountsDto'];
    };
    /** @description A Cathex.Campaigns.Contracts.Dtos.CategoryDto represents either a Trade Category or a Trade Sub-Category. Sub-Categories
     *     should be indicated by setting the Cathex.Campaigns.Contracts.Dtos.CategoryDto.ParentCategoryId value. */
    CategoryDto: {
      /**
       * Format: int32
       * @description The unique identifier for the category or sub-category
       */
      categoryId: number;
      /**
       * Format: int32
       * @description The sub-category's parent category (`0` is not a valid category in this API)
       */
      parentCategoryId?: number | null;
      /** @description Should the trade show in search results for this category.
       *     Can only be set for the parent category, will be `true` for any child categories and if null. */
      isSearchable?: boolean | null;
    };
    /**
     * @description General size of the requesting company, in number of employees
     * @enum {string}
     */
    CompanySizeDto: 'One' | 'TwoToFive' | 'FiveToTen' | 'TenOrMore';
    /** @description Represents a Campaign Confirmation Request */
    ConfirmCampaignRequest: {
      /** @description Flag for confirming the campaign, defaults to true */
      isConfirmed?: boolean | null;
    };
    ConvertQuoteRequest: {
      companyId: string;
      /**
       * Format: double
       * @description If the Quote is for a PPL Campaign, this must be a budget that has been forecasted.
       *
       *     This is represented as a decimal, like all currency, but should actually be a whole number.
       *
       *     Budgets will be rounded up to the nearest increment of 10.
       */
      selectedBudget?: number | null;
      selectedDiscountOffer?: components['schemas']['DiscountOfferDto'];
      /**
       * Format: int32
       * @description This trade's budget cycle day (the day of the month their invoice/budget period starts)
       */
      budgetCycleDay: number;
      /** @description If an active campaign already exists in this quote's Category, de-activate it. Otherwise, throw a validation error. */
      deactivateExistingCampaign?: boolean | null;
    };
    /** @description Represents all of the parameters needed to create a Campaign */
    CreateCampaignRequest: {
      /**
       * @description The type of a campaign (Cathex.Campaigns.Contracts.Dtos.Enums.CampaignTypeDto.Ppl is the only supported now)
       * @enum {string}
       */
      type:
        | 'Ppl'
        | 'Alsp'
        | 'Fixed'
        | 'Sponsored'
        | 'MdpSponsoredSearch'
        | 'MdpSponsoredDisplay';
      biddingStrategyType: components['schemas']['BiddingStrategyTypeDto'];
      category: components['schemas']['CategoryDto'];
      /** @description One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.Category) */
      subCategories: components['schemas']['CategoryDto'][];
      /**
       * Format: double
       * @description The maximum budget for the budget month of Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.BudgetPeriod
       */
      maxBudget: number;
      /**
       * Format: date
       * @description The first of the month that this campaign should start.
       *     Must be a date on the first of this month or first of next month.
       */
      budgetPeriod: string;
      /** @description Geographies for this campaign. */
      geographies: components['schemas']['GeographyDto'][];
      /** @description Rates for each Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.SubCategories and for the Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.Category level
       *     when the Cathex.Campaigns.Contracts.Messages.Campaign.CreateCampaignRequest.BiddingStrategyType is Cathex.Campaigns.Contracts.Dtos.Enums.BiddingStrategyTypeDto.Manual. */
      rateBids?: components['schemas']['RateBidDto'][] | null;
      /** @description A campaign can be created in an inactive state if, for example, the budget is not yet set. Defaults to Active. */
      isActive?: boolean | null;
    };
    CreateCampaignResponse: {
      campaignId: string;
      companyId: string;
    };
    CreateLeadDisputeBatchItem: {
      leadId: string;
      leadDisputeType: components['schemas']['LeadDisputeTypeDto'];
      disputeReason?: string | null;
    };
    CreateLeadDisputeBatchRequest: {
      items: components['schemas']['CreateLeadDisputeBatchItem'][];
    };
    CreateLeadDisputeBatchResponse: {
      companyId?: string | null;
      /** Format: int32 */
      count?: number;
    };
    CreateRegionalRecommendationsRequest: {
      /** @description The geographies covered by this request */
      geographies: components['schemas']['GeographyDto'][];
      category: components['schemas']['CategoryDto'];
      /** @description One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Messages.Regions.CreateRegionalRecommendationsRequest.Category) */
      subCategories: components['schemas']['CategoryDto'][];
    };
    CreateQuoteRequest: {
      /** @description The trade's Company ID, if already known */
      companyId?: string | null;
      source: components['schemas']['QuoteRequestSourceDto'];
      /** @description The geographies covered by this request */
      geographies: components['schemas']['GeographyDto'][];
      category: components['schemas']['CategoryDto'];
      /** @description One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Messages.QuoteRequests.CreateQuoteRequest.Category) */
      subCategories: components['schemas']['CategoryDto'][];
      companySize?: components['schemas']['CompanySizeDto'];
      /** @description Any query/cookie tracking information used with this request */
      utmParameters?: {
        [key: string]: string;
      } | null;
      /** @description If this is a follow-up quote request (e.g., a minor variation of a previous one from the same session), indicate
       *     it here. This is only used for tracking and grouping purposes. */
      previousQuoteRequestId?: string | null;
      /** @description Influence Product Catalog's business rules by setting an experiment group */
      experimentGroup?: string | null;
      quoteRequestType?: components['schemas']['QuoteRequestTypeDto'];
      /** @description If this is an upgrade or a modification, this is the ID of the campaign that is being upgraded */
      sourceCampaignId?: string | null;
      /** @description An arbitrary reference ID from the Cathex.Campaigns.Contracts.Messages.QuoteRequests.CreateQuoteRequest.Source of this quote request */
      sourceReferenceId?: string | null;
    };
    DiscountDto: {
      /** Format: double */
      fixed?: number | null;
      /** Format: double */
      percentage?: number | null;
      /** Format: int32 */
      numberOfMonths?: number;
      /** Format: date */
      lastDiscountedBudgetPeriod?: string;
    };
    /** @description Discount an agent can provide. Initially this is just for fixed but theoretically could be for PPL eventually. */
    DiscountOfferDto: {
      /**
       * Format: double
       * @description If specified, a set amount that can be discounted each month. Either this or Cathex.Campaigns.Contracts.Dtos.QuoteRequests.DiscountOfferDto.Percentage will be set.
       */
      fixedAmount?: number | null;
      /**
       * Format: double
       * @description If specified, a percentage that can be discounted each month. Either this or Cathex.Campaigns.Contracts.Dtos.QuoteRequests.DiscountOfferDto.FixedAmount will be set.
       */
      percentage?: number | null;
      /**
       * Format: int32
       * @description Length of the discount commitment (e.g., 3 months)
       */
      numberOfMonths?: number;
    };
    FixedCampaignDetailsDto: {
      /** Format: double */
      price?: number;
      /** Format: int32 */
      monthsCommitted?: number;
      /** Format: date */
      commitmentStartDate?: string;
      /** Format: date */
      commitmentRenewalDate?: string;
      paymentCadence?: components['schemas']['PaymentCadenceDto'];
      discount?: components['schemas']['DiscountDto'];
      leadCommitment?: components['schemas']['LeadCommitmentDto'];
      expectedLeadCount?: components['schemas']['LeadCommitmentDto'];
    };
    FixedQuoteDetailsDto: {
      /**
       * Format: double
       * @description Monthly price of this fixed plan
       */
      pricePerMonth?: number;
      /**
       * Format: int32
       * @description Length of this commitment period (e.g., 12 months)
       */
      commitmentPeriodMonths?: number;
    };
    /** @description A geography identifies some known territory */
    GeographyDto: {
      /** @description An alphanumeric value that maps to a real location (e.g., `SO4`) */
      value: string;
      type: components['schemas']['GeographyTypeDto'];
      /** @description The name of a location */
      name?: string | null;
      centroid?: components['schemas']['GeoPointDto'];
    };
    /** @description Represents a geographical point with latitude and longitude coordinates. */
    GeoPointDto: {
      /**
       * Format: double
       * @description The latitude of the location, represented as a double. Ranges from -90 to 90.
       */
      latitude?: number;
      /**
       * Format: double
       * @description The longitude of the location, represented as a double. Ranges from -180 to 180.
       */
      longitude?: number;
    };
    /** @enum {string} */
    GeographyDemandDto: 'Low' | 'Middle' | 'High';
    /** @enum {string} */
    GeographyTypeDto:
      | 'PostcodeArea'
      | 'PostcodeDistrict'
      | 'PostcodeSector'
      | 'DirectoryArea';
    GeographyInsightDto: {
      geography?: components['schemas']['GeographyDto'];
      /**
       * Format: int32
       * @description The average household income for the given geography
       */
      averageHouseholdIncome?: number;
      /**
       * Format: int32
       * @description The number of households for the given geography
       */
      households?: number;
      /**
       * Format: int32
       * @description The number of leads available for the given geography
       */
      leadsAvailable?: number;
      /**
       * Format: int32
       * @description The number of leads consumed for the given geography
       */
      leadsConsumed?: number;
      /**
       * Format: int32
       * @description The population for the given geography
       */
      population?: number;
      /**
       * Format: int32
       * @description The number of trades for the given geography
       */
      tradeCount?: number;
    };
    GeographyInsightDtoPageResult: {
      /** @description Gets the collection of entities for this page. */
      items?: components['schemas']['GeographyInsightDto'][] | null;
      /** Format: int32 */
      skip?: number | null;
      /** Format: int32 */
      top?: number | null;
      /**
       * Format: int32
       * @description Gets the total count of items in the result set
       */
      count?: number;
    };
    GetAdjustmentResponse: {
      adjustment?: components['schemas']['AdjustmentDto'];
    };
    /** @enum {string} */
    LeadChannelDto:
      | 'Call'
      | 'Click'
      | 'Directory'
      | 'Message'
      | 'RequestAQuote';
    /** @description The forecasted commitment when the campaign was created. Depending on campaign type, this may be yearly
     *     or monthly. */
    LeadCommitmentDto: {
      /** Format: int32 */
      min?: number;
      /** Format: int32 */
      max?: number;
      monthlyDistribution?: {
        [key: string]: number;
      } | null;
    };
    LeadDisputeDto: {
      leadId?: string | null;
      /** Format: date-time */
      dateCreated?: string;
      leadDisputeType?: components['schemas']['LeadDisputeTypeDto'];
      disputeReason?: string | null;
      /** Format: double */
      price?: number;
      /** Format: double */
      adjustedPrice?: number;
      leadDisputeStatus?: components['schemas']['LeadDisputeStatusDto'];
      /** Format: int32 */
      adjustedCategoryId?: number | null;
      /** Format: int32 */
      adjustedSubCategoryId?: number | null;
      disputedLead?: components['schemas']['LeadDto'];
      /** Format: date */
      budgetPeriodAdjusted?: string | null;
    };
    LeadDisputeDtoPageResult: {
      /** @description Gets the collection of entities for this page. */
      items?: components['schemas']['LeadDisputeDto'][] | null;
      /** Format: int32 */
      skip?: number | null;
      /** Format: int32 */
      top?: number | null;
      /**
       * Format: int32
       * @description Gets the total count of items in the result set
       */
      count?: number;
    };
    /** @enum {string} */
    LeadDisputeStatusDto: 'Pending' | 'Approved' | 'Rejected' | 'Adjusted';
    /** @enum {string} */
    LeadDisputeTypeDto:
      | 'Spam'
      | 'OutOfArea'
      | 'WrongCategory'
      | 'ContactDetails'
      | 'Duplicate'
      | 'Other';
    LeadDto: {
      leadId?: string | null;
      /** @enum {string} */
      leadType?: 'Lead' | 'Click';
      readonly isDisputed?: boolean;
      /** Format: date-time */
      dateGenerated?: string;
      channel?: components['schemas']['LeadChannelDto'];
      /** Format: int32 */
      categoryId?: number | null;
      /** Format: int32 */
      subCategoryId?: number | null;
      campaignId?: string | null;
      disputeStatus?: components['schemas']['LeadDisputeStatusDto'];
    };
    LeadDtoPageResult: {
      /** @description Gets the collection of entities for this page. */
      items?: components['schemas']['LeadDto'][] | null;
      /** Format: int32 */
      skip?: number | null;
      /** Format: int32 */
      top?: number | null;
      /**
       * Format: int32
       * @description Gets the total count of items in the result set
       */
      count?: number;
    };
    /** @enum {string} */
    OfferingTypeDto:
      | 'NewProduct'
      | 'Upgrade'
      | 'Downgrade'
      | 'Amendment'
      | 'BudgetRecalculation'
      | 'Renewal'
      | 'RenewalAmendment';
    /** @description Parent Category for use with metadata endpoints; these represent CategoryDocuments from Trade Experience */
    ParentCategoryDto: {
      /**
       * Format: int32
       * @description Unique identifier for this parent category
       */
      parentCategoryId?: number;
      /** @description The name of this parent category */
      name?: string | null;
      /** @description The sub categories of this parent category */
      subCategories?: components['schemas']['SubCategoryDto'][] | null;
    };
    /** @enum {string} */
    PaymentCadenceDto: 'Monthly' | 'Quarterly' | 'Annually';
    /** @description A Postcode Area is the largest postcode-based geography in the UK. It's the first letter(s) of a Postcode.
     *
     *     See <a href="https://github.com/cat-home-experts/gcp-trade-experience-contracts/blob/main/source/Cathex.Gcp.TradeExperience.Contracts/Documents/PostcodeAreaDocument.cs">PostcodeAreaDocument.cs</a> */
    PostcodeAreaDto: {
      /** @description Postcode Area */
      postcodeArea?: string | null;
      /** @description The most identifiable or largest town or city in this Postcode Area */
      postcodeAreaName?: string | null;
      /** @description Parent Region */
      region?: string | null;
      /** @description Parent Region Name */
      regionName?: string | null;
      /** @description One or more Cathex.Campaigns.Contracts.Dtos.Internal.PostcodeDistrictDto that make up this Postcode Area */
      postcodeDistricts?: components['schemas']['PostcodeDistrictDto'][] | null;
    };
    /** @description A Postcode District is the second largest postcode-based geography. It's the first half of a Postcode (the "outcode").
     *
     *     See <a href="https://github.com/cat-home-experts/gcp-trade-experience-contracts/blob/main/source/Cathex.Gcp.TradeExperience.Contracts/Documents/PostcodeDistrictDocument.cs">PostcodeDistrictDocument.cs</a> */
    PostcodeDistrictDto: {
      /** @description Postcode Outcode */
      postcodeDistrict?: string | null;
      /**
       * Format: int32
       * @description This provides a stable value to use for ordering Postcode Districts including ones that end with letters.
       */
      sortOrder?: number;
      /** @description Parent Postcode Area */
      postcodeArea?: string | null;
      /** @description Parent Postcode Area Name */
      postcodeAreaName?: string | null;
      /** @description Parent Postcode Area's Region */
      region?: string | null;
      /** @description Parent Postcode Area's Region Name */
      regionName?: string | null;
      /** @description Postcode District's name */
      postcodeDistrictName?: string | null;
      postcodeDistrictCentroid?: components['schemas']['GeoPointDto'];
    };
    PplCampaignDetailsDto: {
      biddingStrategyType?: components['schemas']['BiddingStrategyTypeDto'];
      isDirectoryOptOut?: boolean;
      discount?: components['schemas']['DiscountDto'];
      leadCommitment?: components['schemas']['LeadCommitmentDto'];
    };
    PplQuoteDetailsDto: {
      /**
       * Format: double
       * @description If a trade has selected this quote, this is the budget they picked. It must have a corresponding
       *     lead forecast for this budget amount.
       *
       *     This is represented as a decimal, like all currency, but should actually be a whole number.
       *
       *     Budgets will be rounded up to the nearest increment of 10.
       */
      selectedBudget?: number | null;
      /**
       * Format: double
       * @description The budget a UI might want to start with
       *
       *     This is represented as a decimal, like all currency, but should actually be a whole number.
       *
       *     Budgets will be rounded up to the nearest increment of 10.
       */
      recommendedBudget?: number;
      /**
       * Format: double
       * @description Max budget to allow them to select. e.g., Default to 2 x largest bundle.
       *
       *     This is represented as a decimal, like all currency, but should actually be a whole number.
       *
       *     Budgets will be rounded up to the nearest increment of 10.
       */
      maxBudget?: number;
      /**
       * Format: double
       * @description Min budget to allow them to select. e.g., £100
       *
       *     This is represented as a decimal, like all currency, but should actually be a whole number.
       *
       *     Budgets will be rounded up to the nearest increment of 10.
       */
      minBudget?: number;
      /**
       * Format: double
       * @description Estimated maximum average lead price
       */
      maxAverageLeadPrice?: number;
      /**
       * Format: double
       * @description Estimated minimum average lead price
       */
      minAverageLeadPrice?: number;
    };
    ProblemDetails: {
      type?: string | null;
      title?: string | null;
      /** Format: int32 */
      status?: number | null;
      detail?: string | null;
      instance?: string | null;
    } & {
      [key: string]: unknown;
    };
    /** @enum {string} */
    PubSubResponseStatusCode:
      | 'OK'
      | 'BadRequest'
      | 'Unauthenticated'
      | 'PermissionDenied'
      | 'NotFound'
      | 'AlreadyExists'
      | 'ResourceExhausted'
      | 'Cancelled'
      | 'InternalServerError'
      | 'BadGateway'
      | 'Unavailable'
      | 'DeadlineExceeded';
    QuoteDto: {
      /** @description Unique ID of this quote */
      quoteId?: string | null;
      quoteType?: components['schemas']['QuoteTypeDto'];
      offeringType?: components['schemas']['OfferingTypeDto'];
      leadForecast?: components['schemas']['QuoteLeadForecastDto'];
      leadForecastUpgradeDelta?: components['schemas']['QuoteLeadForecastDto'];
      fixedDetails?: components['schemas']['FixedQuoteDetailsDto'];
      pplDetails?: components['schemas']['PplQuoteDetailsDto'];
      /** @description Display label for this quote (e.g., Lite, Standard, Pro) */
      tierLabel?: string | null;
      /**
       * Format: int32
       * @description Field indicating a relative order against all quotes in a Quote Set
       */
      order?: number;
      /** @description Indicates that this quote, among quotes in a Quote Set, is the recommended one. Only one quote in a Quote Set
       *     will be recommended. */
      isRecommended?: boolean;
      /** @description Indicates that this quote is a special offer */
      isSpecialOffer?: boolean;
      /** @description Discounts that an agent can offer to a trade. Initially only against a bundle campaign, and probably hard-coded. */
      agentDiscounts?: components['schemas']['DiscountOfferDto'][] | null;
      /** @description True if the trade has already selected this quote */
      isSelected?: boolean;
    };
    /** @description Min and Max leads forecasted for the following month. For a PPL campaign, the budget used is the
     *     selected (if already selected), most recently re-forecasted, or recommended budget. */
    QuoteLeadForecastDto: {
      /**
       * Format: int32
       * @description Predicted fewest number of leads per month
       */
      min?: number;
      /**
       * Format: int32
       * @description Predicted most number of leads per month
       */
      max?: number;
      /** @description Map of Budget Period to multiplier that represents seasonality */
      monthlyDistribution?: {
        [key: string]: number;
      } | null;
      /**
       * Format: double
       * @description The budget amount used for this forecast, if it is a PPL quote
       *
       *     This is represented as a decimal, like all currency, but should actually be a whole number.
       *
       *     Budgets will be rounded up to the nearest increment of 10.
       */
      selectedBudget?: number | null;
    };
    /** @description Set of forecasts for a quote */
    QuoteLeadForecastsDto: {
      forecasts?: components['schemas']['QuoteLeadForecastDto'][] | null;
    };
    QuoteLeadReforecastDto: {
      /**
       * Format: double
       * @description The budget used for this forecast
       *
       *     This is represented as a decimal, like all currency, but should actually be a whole number.
       *
       *     Budgets will be rounded up to the nearest increment of 10.
       */
      selectedBudget?: number;
      /**
       * Format: int32
       * @description Predicted fewest number of leads per month
       */
      min?: number;
      /**
       * Format: int32
       * @description Predicted most number of leads per month
       */
      max?: number;
      /** @description Map of Budget Period to multiplier that represents seasonality */
      monthlyDistribution?: {
        [key: string]: number;
      } | null;
    };
    /** @enum {string} */
    QuoteRequestBehaviorDto: 'OfferCampaignQuotes' | 'CallSupport' | 'Alsp';
    /** @description A Quote Request is a recommend offering to a trade along with the original inputs of the request */
    QuoteRequestDto: {
      /** @description Unique ID of this Quote Request */
      quoteRequestId?: string | null;
      behavior?: components['schemas']['QuoteRequestBehaviorDto'];
      /**
       * Format: date-time
       * @description Date this quote request was created/requested
       */
      dateCreated?: string;
      /** @description Refreshing a quote request creates a new one, but hangs on to the old one. Can also store previous
       *     quote request IDs if, for example, an agent updates the parameters of a quote. */
      previousQuoteRequestId?: string | null;
      /** @description If this Quote Request is superseded by a new one (specified as Cathex.Campaigns.Contracts.Dtos.QuoteRequests.QuoteRequestDto.PreviousQuoteRequestId in a
       *     "Create" or "Refresh" action), this is the newer ID */
      supersedingQuoteRequestId?: string | null;
      /** @description The trade's Company ID, if already known */
      companyId?: string | null;
      status?: components['schemas']['QuoteRequestStatusDto'];
      /** @description Indicates which quote is already selected */
      selectedQuoteId?: string | null;
      summary?: components['schemas']['QuoteSetSummaryDto'];
      /** @description Individual quotes (options) that respond to this quote request. May be empty if the Cathex.Campaigns.Contracts.Dtos.QuoteRequests.QuoteRequestDto.Behavior does
       *     not support quotes. */
      quoteSet?: components['schemas']['QuoteDto'][] | null;
      /**
       * Format: date-time
       * @description Expiration date of the quotes in this quote request.
       */
      validTo?: string;
      source?: components['schemas']['QuoteRequestSourceDto'];
      /** @description The geographies originally included in the request */
      geographies?: components['schemas']['GeographyDto'][] | null;
      category?: components['schemas']['CategoryDto'];
      /** @description One or more Sub Categories (whose parent must be Cathex.Campaigns.Contracts.Dtos.QuoteRequests.QuoteRequestDto.Category) */
      subCategories?: components['schemas']['CategoryDto'][] | null;
      companySize?: components['schemas']['CompanySizeDto'];
      /** @description In the case of an upgrade (or a modification), this is the Campaign that is being upgraded */
      sourceCampaignId?: string | null;
    };
    /**
     * @description The source of this quote request
     * @enum {string}
     */
    QuoteRequestSourceDto:
      | 'Dsu'
      | 'Gsf'
      | 'TradeApp'
      | 'Migration'
      | 'AlspRenewal'
      | 'AlspRenewalAmendment'
      | 'CampaignAdmin'
      | 'Unattended';
    /** @enum {string} */
    QuoteRequestStatusDto: 'Active' | 'Expired' | 'Converted';
    /** @enum {string} */
    QuoteRequestTypeDto:
      | 'Acquisition'
      | 'Renewal'
      | 'RenewalAmendment'
      | 'Upgrade'
      | 'AdditionalProduct'
      | 'Amendment'
      | 'Lifecycle';
    /** @enum {string} */
    QuoteSetSummaryAmendmentDto:
      | 'NotApplicable'
      | 'AvailableLeadCommitmentChanged'
      | 'AvailableLeadCommitmentUnchanged'
      | 'Unavailable'
      | 'NotEnoughLeads';
    /** @enum {string} */
    QuoteSetSummaryBudgetRecalculationDto: 'NotApplicable' | 'Recommended';
    QuoteSetSummaryDto: {
      amendment: components['schemas']['QuoteSetSummaryAmendmentDto'];
      budgetRecalculation: components['schemas']['QuoteSetSummaryBudgetRecalculationDto'];
      newProduct: components['schemas']['QuoteSetSummaryNewProductDto'];
      ppl: components['schemas']['QuoteSetSummaryPplDto'];
      upgrade: components['schemas']['QuoteSetSummaryUpgradeDto'];
    };
    /** @enum {string} */
    QuoteSetSummaryNewProductDto: 'NotApplicable' | 'Available' | 'Unavailable';
    /** @enum {string} */
    QuoteSetSummaryPplDto: 'NotApplicable' | 'Available' | 'Unavailable';
    /** @enum {string} */
    QuoteSetSummaryUpgradeDto:
      | 'NotApplicable'
      | 'Available'
      | 'Unavailable'
      | 'OffTarget'
      | 'NotEnoughLeads'
      | 'NotEnoughTimeRemaining';
    /** @enum {string} */
    QuoteTypeDto: 'Fixed' | 'Ppl';
    /** @description The agreed upon rates by channel for the associated campaign's category (or sub-categories).
     *     There will always be at least one agreed rate per campaign at the category level and at least
     *     one at the sub-category level (because campaigns have at least one sub-category).
     *
     *     When a new rate is agreed to in a given sub-category, it can be stored without having to also agree
     *     to new rates in the other sub-categories. The previously agreed rates will continue to apply. */
    RateBidDto: {
      /**
       * Format: double
       * @description Agreed price of a lead for calls to phone numbers listed in search results
       */
      call: number;
      /**
       * Format: double
       * @description Agreed price of a click delivered to clients' websites from search results
       */
      click: number;
      /**
       * Format: double
       * @description Agreed price of a lead for calls made to phone numbers listed in printed directories
       */
      directory: number;
      /**
       * Format: double
       * @description Agreed price of a lead for direct messages to a client
       */
      message: number;
      /**
       * Format: double
       * @description Agreed price of a lead where our Request-a-Quote option has picked this client
       */
      requestAQuote: number;
      /**
       * Format: int32
       * @description The Category this Rate applies to
       */
      categoryId: number;
      /**
       * Format: int32
       * @description The sub-category the rate applies to. This can only be null if it's the one rate at the Category level.
       */
      subCategoryId?: number | null;
      /**
       * Format: date-time
       * @description Date this rate agreement was made with the client. Defaults to now.
       */
      dateAgreed?: string | null;
    };
    ReforecastQuoteRequest: {
      /**
       * Format: double
       * @description Selected budget. Must be between Min and Max budget of the PPL quote
       *
       *     This is represented as a decimal, like all currency, but should actually be a whole number.
       *
       *     Budgets will be rounded up to the nearest increment of 10.
       */
      selectedBudget: number;
    };
    RefreshQuoteRequest: Record<string, never>;
    /** @description A Region is a higher level division of the UK than Postcode Area. It is loosely based on the former Government
     *     Office Regions (GOR) in England with pseudo regions for the other countries in the UK. Examples include East Midlands,
     *     Wales (non-GOR region), Greater London (non-GOR region but Checkatrade convention), etc. It has many Cathex.Campaigns.Contracts.Dtos.Internal.PostcodeAreaDto.
     *
     *     At present, Campaigns cannot be assigned Regions.
     *
     *     See <a href="https://github.com/cat-home-experts/gcp-trade-experience-contracts/blob/main/source/Cathex.Gcp.TradeExperience.Contracts/Documents/RegionDocument.cs">RegionDocument.cs</a> */
    RegionDto: {
      /** @description Slugified version of Cathex.Campaigns.Contracts.Dtos.Internal.RegionDto.RegionName to unique identify the region */
      region?: string | null;
      /** @description Name of this Region */
      regionName?: string | null;
      /** @description One or more Cathex.Campaigns.Contracts.Dtos.Internal.PostcodeAreaDto that make up this Region */
      postcodeAreas?: components['schemas']['PostcodeAreaDto'][] | null;
    };
    RegionDtoPageResult: {
      /** @description Gets the collection of entities for this page. */
      items?: components['schemas']['RegionDto'][] | null;
      /** Format: int32 */
      skip?: number | null;
      /** Format: int32 */
      top?: number | null;
      /**
       * Format: int32
       * @description Gets the total count of items in the result set
       */
      count?: number;
    };
    SelectQuoteRequest: {
      quoteId: string;
      /**
       * Format: double
       * @description If the Quote is for a PPL Campaign, this must be a budget that has been forecasted.
       *
       *     This is represented as a decimal, like all currency, but should actually be a whole number.
       *
       *     Budgets will be rounded up to the nearest increment of 10.
       */
      selectedBudget?: number | null;
    };
    SponsorshipCampaignDetailsDto: {
      /** @description True if this sponsored listing campaign is enabled in Search */
      isSearchSponsored?: boolean;
      /** @description True if this sponsored listing campaign is enabled in Request a Quote */
      isRequestAQuoteSponsored?: boolean;
      /**
       * Format: double
       * @description Price multiplier used for Search leads, if search sponsored
       */
      searchRateMultiplier?: number | null;
      /**
       * Format: double
       * @description Price multiplier used for Request a Quote leads, if RaQ sponsored
       */
      requestAQuoteRateMultiplier?: number | null;
    };
    /** @description Sub-Category for use with metadata endpoints; these represent CategoryDocuments' Sub-Category from Trade Experience */
    SubCategoryDto: {
      /**
       * Format: int32
       * @description Sub-category ID
       */
      categoryId?: number;
      /** @description The name of the sub-category */
      name?: string | null;
      /** @description Whether this sub-category is selected by default when creating a new campaign */
      isSelectedByDefault?: boolean;
    };
    /** @description Updates the paused date for a campaign */
    UpdateAvailabilityRequest: {
      /**
       * Format: date
       * @description Date this campaign should be paused until, exclusive of this date. (If this value is set,
       *     it is set to the date that the campaign should be understood to be "unpaused.")
       *
       *     Clients can pause their campaigns for a limited period of time, and we can pause a campaign until any date in the future.
       */
      pausedUntil?: string | null;
    };
    UpdateBiddingStrategyRequest: {
      biddingStrategyType?: components['schemas']['BiddingStrategyTypeDto'];
    };
    UpdateMaxSpendRequest: {
      /** Format: date */
      budgetPeriod: string;
      /** @description If true and the following month's budget isn't explicitly set, then we need to take this month's budget and then
       *     clone it for next month. If this is false, it's just their way of saying "here's the new max spend starting
       *     in this period and applying into the future until said otherwise." */
      isOneTimeTopUp?: boolean | null;
      /**
       * Format: double
       * @description Maximum amount the Trade is willing to pay for a campaign in a given budget period regardless of
       *     minimum commitment budget boosts
       */
      maxSpend: number;
    };
    UpdateGeographiesRequest: {
      geographies?: components['schemas']['GeographyDto'][] | null;
    };
    UpdateLeadDisputeRequest: {
      /** Format: double */
      adjustedLeadPrice?: number | null;
      leadDisputeStatus?: components['schemas']['LeadDisputeStatusDto'];
      /** Format: int32 */
      adjustedCategoryId?: number | null;
      /** Format: int32 */
      adjustedSubCategoryId?: number | null;
      comments?: string | null;
    };
    /** @description Updates the parent category of a campaign */
    UpdateParentCategoryRequest: {
      category: components['schemas']['CategoryDto'];
    };
    UpdateRateBidsRequest: {
      rateBids?: components['schemas']['RateBidDto'][] | null;
    };
    /** @description Updates the active, directory opt-out and RaQ opt-out flags for a campaign */
    UpdateStatusRequest: {
      isActive?: boolean | null;
      isRequestAQuoteOptOut?: boolean | null;
      isDirectoryOptOut?: boolean | null;
    };
    UpdateSubCategoriesRequest: {
      category?: components['schemas']['CategoryDto'];
      subCategories?: components['schemas']['CategoryDto'][] | null;
      updateType?: components['schemas']['CampaignAttributeUpdateTypeDto'];
    };
    ValidationProblemDetails: {
      type?: string | null;
      title?: string | null;
      /** Format: int32 */
      status?: number | null;
      detail?: string | null;
      instance?: string | null;
      readonly errors?: {
        [key: string]: string[];
      } | null;
    } & {
      [key: string]: unknown;
    };
    /** @description Fetch a Quote for a Campaign Upgrade */
    CreateUpgradeOptionsRequest: {
      source: components['schemas']['QuoteRequestSourceDto'];
      amendment?: components['schemas']['CampaignAmendmentDto'];
    };
    /** @description Upgrade a campaign based on a new quote request */
    UpgradeCampaignRequest: {
      /** @description An Upgrade typed Quote Request */
      quoteRequestId: string | null;
      /** @description A Quote in Cathex.Campaigns.Contracts.Messages.Campaign.UpgradeCampaignRequest.QuoteRequestId's Quote Set */
      quoteId: string | null;
    };
    UpgradeCampaignResponse: {
      upgradeId: string;
      campaignId: string;
      companyId: string;
    };
    PriceRangesDtoPageResult: {
      items?: components['schemas']['PriceRangeItem'][];
    };
    PriceRangeItem: {
      categoryId?: number;
      subCategoryId?: number;
      call?: components['schemas']['PriceDetail'];
      click?: components['schemas']['PriceDetail'];
      directory?: components['schemas']['PriceDetail'];
      message?: components['schemas']['PriceDetail'];
      requestAQuote?: components['schemas']['PriceDetail'];
      /** Format: float */
      averageRate?: number;
    };
    PriceDetail: {
      /** Format: float */
      maxPrice?: number;
      /** Format: float */
      minPrice?: number;
    };
    PriceRangesRequest: Record<string, never>;
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
  GetAdjustment: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An Adjustment's ID */
        adjustmentId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['GetAdjustmentResponse'];
          'application/json': components['schemas']['GetAdjustmentResponse'];
          'text/json': components['schemas']['GetAdjustmentResponse'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  GetAdjustments: {
    parameters: {
      query?: {
        /** @description Limit the number of results in this page. Defaults to returning "all" and there is no max value. */
        top?: number;
        /** @description Skip this number of adjustments before returning results. */
        skip?: number;
      };
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['AdjustmentDtoPageResult'];
          'application/json': components['schemas']['AdjustmentDtoPageResult'];
          'text/json': components['schemas']['AdjustmentDtoPageResult'];
        };
      };
    };
  };
  RecalculateCampaignBudgets: {
    parameters: {
      query?: {
        correlationId?: string;
        limit?: number;
        chunkSize?: number;
        chunkInterval?: number;
        /** @description for a specific company */
        companyId?: string;
        budgetCycleDays?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['CampaignBulkResponseMessage'];
          'application/json': components['schemas']['CampaignBulkResponseMessage'];
          'text/json': components['schemas']['CampaignBulkResponseMessage'];
        };
      };
    };
  };
  RefreshCampaigns: {
    parameters: {
      query?: {
        /** @description A correlation ID to use for tracking this request */
        correlationId?: string;
        /** @description When specified, limit the total number of Trades to update (not the number of campaigns) */
        limit?: number;
        chunkSize?: number;
        chunkInterval?: number;
        /** @description Request a refresh for a specific Trade */
        companyId?: string;
        /** @description The type of refresh to perform. Can be 'Balance' or 'Definition' (default). */
        refreshType?: 'Definition' | 'Balance';
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['CampaignRefreshBulkResponseMessage'];
          'application/json': components['schemas']['CampaignRefreshBulkResponseMessage'];
          'text/json': components['schemas']['CampaignRefreshBulkResponseMessage'];
        };
      };
    };
  };
  RecalculateCampaigns: {
    parameters: {
      query?: {
        /** @description A correlation ID to use for tracking this request */
        correlationId?: string;
        /** @description When specified, limit the total number of Trades to update (not the number of campaigns) */
        limit?: number;
        /** @description Ignored */
        chunkSize?: number;
        /** @description Ignored */
        chunkInterval?: number;
        /** @description Request a recalculation for a specific Trade */
        companyId?: string;
        /** @description Budget period to recalculate. Defaults to current budget period. */
        budgetPeriod?: string;
        /** @description The number of months +/- relative to budgetPeriod to recalculate. */
        budgetPeriodMonthDelta?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['CampaignRefreshBulkResponseMessage'];
          'application/json': components['schemas']['CampaignRefreshBulkResponseMessage'];
          'text/json': components['schemas']['CampaignRefreshBulkResponseMessage'];
        };
      };
    };
  };
  GetCampaigns: {
    parameters: {
      query?: {
        /** @description Number of records to skip, defaults to 0 */
        offset?: number;
        /** @description Number of records to return, defaults to 50 */
        pageSize?: number;
        campaignType?:
          | 'Ppl'
          | 'Alsp'
          | 'Fixed'
          | 'Sponsored'
          | 'MdpSponsoredSearch'
          | 'MdpSponsoredDisplay';
        includeInactive?: boolean;
        categoryId?: number;
        /**
         * @description Sort expression. Allowed sort fields are: dateUpdated, isActive, pausedUntil
         * @example dateUpdated:asc
         */
        sort?: string[];
      };
      header?: never;
      path: {
        companyId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['CampaignDtoPageResult'];
          'application/json': components['schemas']['CampaignDtoPageResult'];
          'text/json': components['schemas']['CampaignDtoPageResult'];
        };
      };
    };
  };
  CreateCampaign: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['CreateCampaignRequest'];
        'text/json': components['schemas']['CreateCampaignRequest'];
        'application/*+json': components['schemas']['CreateCampaignRequest'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['CreateCampaignResponse'];
          'application/json': components['schemas']['CreateCampaignResponse'];
          'text/json': components['schemas']['CreateCampaignResponse'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  GetCampaign: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        companyId: string;
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['CampaignDto'];
          'application/json': components['schemas']['CampaignDto'];
          'text/json': components['schemas']['CampaignDto'];
        };
      };
    };
  };
  GetCampaignStatistics: {
    parameters: {
      query?: {
        summaryLevel?:
          | 'Last7Days'
          | 'Last30Days'
          | 'Last90Days'
          | 'BudgetPeriod'
          | 'LastBudgetPeriod'
          | 'Lifetime';
      };
      header?: never;
      path: {
        companyId: string;
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['CampaignStatisticSummaryDto'];
          'application/json': components['schemas']['CampaignStatisticSummaryDto'];
          'text/json': components['schemas']['CampaignStatisticSummaryDto'];
        };
      };
    };
  };
  GetPriceRanges: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        companyId: string;
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['PriceRangesRequest'];
        'text/json': components['schemas']['PriceRangesRequest'];
        'application/*+json': components['schemas']['PriceRangesRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['PriceRangesDtoPageResult'];
          'application/json': components['schemas']['PriceRangesDtoPageResult'];
          'text/json': components['schemas']['PriceRangesDtoPageResult'];
        };
      };
    };
  };
  ConfirmCampaign: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['ConfirmCampaignRequest'];
        'text/json': components['schemas']['ConfirmCampaignRequest'];
        'application/*+json': components['schemas']['ConfirmCampaignRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  UpdateSubCategories: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpdateSubCategoriesRequest'];
        'text/json': components['schemas']['UpdateSubCategoriesRequest'];
        'application/*+json': components['schemas']['UpdateSubCategoriesRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  UpdateMaxSpend: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpdateMaxSpendRequest'];
        'text/json': components['schemas']['UpdateMaxSpendRequest'];
        'application/*+json': components['schemas']['UpdateMaxSpendRequest'];
      };
    };
    responses: {
      /** @description No Content */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  UpdateRateBids: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpdateRateBidsRequest'];
        'text/json': components['schemas']['UpdateRateBidsRequest'];
        'application/*+json': components['schemas']['UpdateRateBidsRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  UpdateGeographies: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpdateGeographiesRequest'];
        'text/json': components['schemas']['UpdateGeographiesRequest'];
        'application/*+json': components['schemas']['UpdateGeographiesRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  UpdateAvailability: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpdateAvailabilityRequest'];
        'text/json': components['schemas']['UpdateAvailabilityRequest'];
        'application/*+json': components['schemas']['UpdateAvailabilityRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  UpdateStatus: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpdateStatusRequest'];
        'text/json': components['schemas']['UpdateStatusRequest'];
        'application/*+json': components['schemas']['UpdateStatusRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  UpdateParentCategory: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpdateParentCategoryRequest'];
        'text/json': components['schemas']['UpdateParentCategoryRequest'];
        'application/*+json': components['schemas']['UpdateParentCategoryRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  UpdateBiddingStrategy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpdateBiddingStrategyRequest'];
        'text/json': components['schemas']['UpdateBiddingStrategyRequest'];
        'application/*+json': components['schemas']['UpdateBiddingStrategyRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  GetCategoryById: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A parent category ID */
        parentCategoryId: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ParentCategoryDto'];
          'application/json': components['schemas']['ParentCategoryDto'];
          'text/json': components['schemas']['ParentCategoryDto'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  TradeAppFirebaseToken: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': string;
          'application/json': string;
          'text/json': string;
        };
      };
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  GetLeadDispute: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        companyId: string;
        leadDisputeId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['LeadDisputeDto'];
          'application/json': components['schemas']['LeadDisputeDto'];
          'text/json': components['schemas']['LeadDisputeDto'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  UpdateLeadDispute: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        companyId: string;
        leadDisputeId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpdateLeadDisputeRequest'];
        'text/json': components['schemas']['UpdateLeadDisputeRequest'];
        'application/*+json': components['schemas']['UpdateLeadDisputeRequest'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  GetLeadDisputes: {
    parameters: {
      query?: {
        leadDisputeStatusAsEnum?: string;
        adjustmentIdMaybe?: string;
        somePagination?: string;
      };
      header?: never;
      path: {
        companyId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['LeadDisputeDtoPageResult'];
          'application/json': components['schemas']['LeadDisputeDtoPageResult'];
          'text/json': components['schemas']['LeadDisputeDtoPageResult'];
        };
      };
    };
  };
  CreateLeadDisputes: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        companyId: string;
        leadId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['CreateLeadDisputeBatchRequest'];
        'text/json': components['schemas']['CreateLeadDisputeBatchRequest'];
        'application/*+json': components['schemas']['CreateLeadDisputeBatchRequest'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['CreateLeadDisputeBatchResponse'];
          'application/json': components['schemas']['CreateLeadDisputeBatchResponse'];
          'text/json': components['schemas']['CreateLeadDisputeBatchResponse'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  GetLeadById: {
    parameters: {
      query?: {
        leadType?: 'Lead' | 'Click';
      };
      header?: never;
      path: {
        companyId: string;
        leadId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['LeadDto'];
          'application/json': components['schemas']['LeadDto'];
          'text/json': components['schemas']['LeadDto'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  GetLeads: {
    parameters: {
      query?: {
        /** @description Number of records to skip, defaults to 0 */
        offset?: number;
        /** @description Number of records to return, defaults to 50 */
        pageSize?: number;
        budgetPeriod?: string;
        dateGeneratedFrom?: string;
        dateGeneratedTo?: string;
        campaignId?: string;
        /**
         * @description Sort expression. Allowed sort fields are: dateGenerated
         * @example dateGenerated:asc
         */
        sort?: string[];
      };
      header?: never;
      path: {
        companyId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['LeadDtoPageResult'];
          'application/json': components['schemas']['LeadDtoPageResult'];
          'text/json': components['schemas']['LeadDtoPageResult'];
        };
      };
    };
  };
  ExportLeads: {
    parameters: {
      query?: {
        budgetPeriod?: string;
        campaignId?: string;
      };
      header?: never;
      path: {
        companyId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': string;
          'text/csv': string;
          'text/tab-separated-values': string;
        };
      };
      /** @description Client Error */
      415: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': components['schemas']['ProblemDetails'];
          'text/csv': components['schemas']['ProblemDetails'];
          'text/tab-separated-values': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  CreateQuoteRequest: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['CreateQuoteRequest'];
        'text/json': components['schemas']['CreateQuoteRequest'];
        'application/*+json': components['schemas']['CreateQuoteRequest'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['QuoteRequestDto'];
          'application/json': components['schemas']['QuoteRequestDto'];
          'text/json': components['schemas']['QuoteRequestDto'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  RefreshRequestForQuote: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        quoteRequestId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['RefreshQuoteRequest'];
        'text/json': components['schemas']['RefreshQuoteRequest'];
        'application/*+json': components['schemas']['RefreshQuoteRequest'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['QuoteRequestDto'];
          'application/json': components['schemas']['QuoteRequestDto'];
          'text/json': components['schemas']['QuoteRequestDto'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  SelectQuote: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        quoteRequestId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['SelectQuoteRequest'];
        'text/json': components['schemas']['SelectQuoteRequest'];
        'application/*+json': components['schemas']['SelectQuoteRequest'];
      };
    };
    responses: {
      /** @description No Content */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  GetQuoteRequest: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        quoteRequestId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['QuoteRequestDto'];
          'application/json': components['schemas']['QuoteRequestDto'];
          'text/json': components['schemas']['QuoteRequestDto'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  GetQuoteLeadForecasts: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        quoteRequestId: string;
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['QuoteLeadForecastsDto'];
          'application/json': components['schemas']['QuoteLeadForecastsDto'];
          'text/json': components['schemas']['QuoteLeadForecastsDto'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  ConvertQuote: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        quoteRequestId: string;
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['ConvertQuoteRequest'];
        'text/json': components['schemas']['ConvertQuoteRequest'];
        'application/*+json': components['schemas']['ConvertQuoteRequest'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['CampaignDto'];
          'application/json': components['schemas']['CampaignDto'];
          'text/json': components['schemas']['CampaignDto'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
      /** @description Conflict */
      409: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  ReforecastQuote: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        quoteRequestId: string;
        quoteId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['ReforecastQuoteRequest'];
        'text/json': components['schemas']['ReforecastQuoteRequest'];
        'application/*+json': components['schemas']['ReforecastQuoteRequest'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['QuoteLeadReforecastDto'];
          'application/json': components['schemas']['QuoteLeadReforecastDto'];
          'text/json': components['schemas']['QuoteLeadReforecastDto'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  GetRegions: {
    parameters: {
      query?: {
        /** @description Limit the number of results in this page. Defaults to returning "all" and there is no max value. */
        top?: number;
        /** @description Skip this number of regions before returning results. */
        skip?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['RegionDtoPageResult'];
          'application/json': components['schemas']['RegionDtoPageResult'];
          'text/json': components['schemas']['RegionDtoPageResult'];
        };
      };
    };
  };
  CreateRegionalRecommendations: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['CreateRegionalRecommendationsRequest'];
        'text/json': components['schemas']['CreateRegionalRecommendationsRequest'];
        'application/*+json': components['schemas']['CreateRegionalRecommendationsRequest'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  CreateUpgradeOptions: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Fixed Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['CreateUpgradeOptionsRequest'];
        'text/json': components['schemas']['CreateUpgradeOptionsRequest'];
        'application/*+json': components['schemas']['CreateUpgradeOptionsRequest'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['QuoteRequestDto'];
          'application/json': components['schemas']['QuoteRequestDto'];
          'text/json': components['schemas']['QuoteRequestDto'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  UpgradeCampaign: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A Trade's Company ID */
        companyId: string;
        /** @description An existing Campaign */
        campaignId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['UpgradeCampaignRequest'];
        'text/json': components['schemas']['UpgradeCampaignRequest'];
        'application/*+json': components['schemas']['UpgradeCampaignRequest'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['UpgradeCampaignResponse'];
          'application/json': components['schemas']['UpgradeCampaignResponse'];
          'text/json': components['schemas']['UpgradeCampaignResponse'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
}
