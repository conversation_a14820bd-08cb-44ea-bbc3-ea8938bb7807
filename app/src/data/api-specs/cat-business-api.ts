/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/trade-jobs/matches': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Get all jobs matched to the authenticated trade */
    get: {
      parameters: {
        query?: {
          /** @description Page number for pagination */
          page?: number;
          /** @description Number of items per page */
          size?: number;
          /** @description Field to sort by */
          sortBy?: 'price' | 'date';
          /** @description Sort direction */
          sortDirection?: 'ASC' | 'DESC';
          /** @description Comma-separated list of job statuses to filter by */
          statuses?: string;
        };
        header?: never;
        path?: never;
        cookie?: never;
      };
      requestBody?: never;
      responses: {
        /** @description List of matched jobs with pagination info */
        200: {
          headers: {
            [name: string]: unknown;
          };
          content: {
            'application/json': {
              data?: {
                /** Format: uuid */
                id?: string;
                /** @enum {string} */
                status?: 'PENDING' | 'ACCEPTED' | 'REJECTED';
                job?: {
                  /** Format: uuid */
                  id?: string;
                  /** @enum {string} */
                  status?:
                    | 'REQUESTED'
                    | 'SCHEDULED'
                    | 'IN_PROGRESS'
                    | 'COMPLETED'
                    | 'EXPIRED'
                    | 'CANCELED'
                    | 'PENDING_COMPLETION_APPROVAL'
                    | 'NEEDS_ATTENTION'
                    | 'PENDING_PAYMENT';
                  price?: number;
                  description?: string;
                  companyName?: string;
                  location?: {
                    postcode?: string;
                    address?: string;
                  };
                  dates?: {
                    /** Format: uuid */
                    id?: string;
                    /** Format: date-time */
                    date?: string;
                    timeSlot?: string;
                  }[];
                };
              }[];
              pagination?: {
                /** @description Current page number */
                page?: number;
                /** @description Number of items per page */
                size?: number;
                /** @description Total number of items */
                total?: number;
                /** @description Total number of pages */
                totalPages?: number;
              };
            };
          };
        };
        /** @description Invalid pagination, sorting, or status parameters */
        400: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Unauthorized - Authentication required */
        401: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Server error */
        500: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
      };
    };
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/trade-jobs/matches/{jobId}/accept': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Accept a job that was matched to the trade */
    post: {
      parameters: {
        query?: never;
        header?: never;
        path: {
          /** @description Job ID */
          jobId: string;
        };
        cookie?: never;
      };
      requestBody: {
        content: {
          'application/json': {
            /**
             * Format: uuid
             * @description ID of the selected time slot from job.dates
             */
            selectedDateId: string;
          };
        };
      };
      responses: {
        /** @description Job accepted successfully */
        200: {
          headers: {
            [name: string]: unknown;
          };
          content: {
            'application/json': components['schemas']['TradeJobMatch'];
          };
        };
        /** @description Job ID or selected time slot is required */
        400: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Unauthorized - Authentication required */
        401: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Job not found */
        404: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Job is no longer available or already accepted */
        409: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Server error */
        500: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
      };
    };
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/trade-jobs/matches/{jobId}/reject': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Reject a job with optional price feedback */
    post: {
      parameters: {
        query?: never;
        header?: never;
        path: {
          /** @description Job ID */
          jobId: string;
        };
        cookie?: never;
      };
      requestBody: {
        content: {
          'application/json': {
            /** @description Reason for rejecting the job */
            reason: string;
            /**
             * Format: float
             * @description Optional suggested price for the job
             */
            suggestedPrice?: number;
          };
        };
      };
      responses: {
        /** @description Job rejected successfully */
        200: {
          headers: {
            [name: string]: unknown;
          };
          content: {
            'application/json': components['schemas']['TradeJobMatch'];
          };
        };
        /** @description Job ID and reason are required */
        400: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Unauthorized - Authentication required */
        401: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Job match not found or already processed */
        404: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Server error */
        500: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
      };
    };
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/trade-jobs/{jobId}/complete': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Mark a job as complete (only available to assigned trade) */
    post: {
      parameters: {
        query?: never;
        header?: never;
        path: {
          /** @description Job ID */
          jobId: string;
        };
        cookie?: never;
      };
      requestBody?: {
        content: {
          'multipart/form-data': {
            /** @description Completion photos (max 5 files, 10MB each) */
            files?: string[];
          };
        };
      };
      responses: {
        /** @description Job marked as complete successfully */
        200: {
          headers: {
            [name: string]: unknown;
          };
          content: {
            'application/json': components['schemas']['Job'];
          };
        };
        /** @description Job ID is required */
        400: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Unauthorized - Authentication required */
        401: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Only assigned trade can mark job as complete */
        403: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Job not found */
        404: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Job must be in progress to mark as complete */
        409: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Files too large */
        413: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Server error */
        500: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
      };
    };
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/trade-jobs/matches/{jobId}/cancel': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Cancel a job with a reason (only available to assigned trade) */
    post: {
      parameters: {
        query?: never;
        header?: never;
        path: {
          /** @description Job ID */
          jobId: string;
        };
        cookie?: never;
      };
      requestBody: {
        content: {
          'application/json': {
            /** @description Reason for canceling the job */
            reason: string;
          };
        };
      };
      responses: {
        /** @description Job canceled successfully */
        200: {
          headers: {
            [name: string]: unknown;
          };
          content: {
            'application/json': components['schemas']['Job'];
          };
        };
        /** @description Job ID and cancellation reason are required */
        400: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Unauthorized - Authentication required */
        401: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Only assigned trade can cancel this job */
        403: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Job not found */
        404: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Job can only be canceled when scheduled or in progress */
        409: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Server error */
        500: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
      };
    };
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/trade-jobs/matches/{jobId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Get detailed information for a specific job */
    get: {
      parameters: {
        query?: never;
        header?: never;
        path: {
          /** @description Job ID */
          jobId: string;
        };
        cookie?: never;
      };
      requestBody?: never;
      responses: {
        /** @description Detailed job information including location, dates, documents, and issues */
        200: {
          headers: {
            [name: string]: unknown;
          };
          content: {
            'application/json': {
              job?: components['schemas']['Job'];
              location?: {
                postcode?: string;
                address?: string;
              };
              dates?: {
                /** Format: uuid */
                id?: string;
                /** Format: date-time */
                date?: string;
                timeSlot?: string;
              }[];
              match?: {
                /** @enum {string} */
                status?: 'PENDING' | 'ACCEPTED' | 'REJECTED';
                /** Format: uuid */
                selectedDateId?: string;
                rejectionReason?: string;
              };
              issues?: {
                /** Format: uuid */
                id?: string;
                type?: string;
                description?: string;
                /** Format: date-time */
                reportedAt?: string;
                reportedBy?: string;
                status?: string;
              }[];
              documents?: {
                /** Format: uuid */
                id?: string;
                /** @enum {string} */
                type?:
                  | 'JOB_PHOTO'
                  | 'JOB_COMPLETION_PHOTO'
                  | 'JOB_ISSUE_REPORT_PHOTO'
                  | 'OTHER';
                url?: string;
                description?: string;
                /** Format: date-time */
                uploadedAt?: string;
              }[];
            };
          };
        };
        /** @description Job ID is required */
        400: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Unauthorized - Authentication required */
        401: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Job not found or no match found for this trade */
        404: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
        /** @description Server error */
        500: {
          headers: {
            [name: string]: unknown;
          };
          content?: never;
        };
      };
    };
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: {
    /**
     * @description Type of document associated with a job
     * @enum {string}
     */
    JobDocumentType:
      | 'JOB_PHOTO'
      | 'JOB_COMPLETION_PHOTO'
      | 'JOB_ISSUE_REPORT_PHOTO'
      | 'INVOICE'
      | 'OTHER';
    /**
     * @description Available time slots for jobs (MORNING: 09:00AM, AFTERNOON: 12:00PM, EVENING: 05:00PM)
     * @enum {string}
     */
    TimeSlot: 'MORNING' | 'AFTERNOON' | 'EVENING';
    /**
     * @description Current status of the job
     * @enum {string}
     */
    JobStatus:
      | 'PENDING_PAYMENT'
      | 'REQUESTED'
      | 'SCHEDULED'
      | 'EXPIRED'
      | 'IN_PROGRESS'
      | 'PENDING_COMPLETION_APPROVAL'
      | 'COMPLETED'
      | 'CANCELED'
      | 'NEEDS_ATTENTION';
    /**
     * @description The urgency level of the job
     * @enum {string}
     */
    JobUrgency: 'Standard' | 'Flexible' | 'Urgent';
    /**
     * @description Type of property where the job will be performed
     * @enum {string}
     */
    PropertyType:
      | 'House'
      | 'Apartment/Flat'
      | 'Office'
      | 'Retail'
      | 'Industrial/Warehouse'
      | 'Hotel/Restaurant'
      | 'School/University'
      | 'Hospital/Clinic'
      | 'Other';
    JobDate: {
      /**
       * Format: uuid
       * @description Unique identifier for the job date
       */
      id: string;
      /**
       * Format: uuid
       * @description ID of the associated job
       */
      jobId: string;
      /**
       * Format: date-time
       * @description The date and time for the job
       */
      date: string;
      /** @description Priority of this date (1-3) */
      priority: number;
      /** @description The time slot for this job date */
      timeSlot: components['schemas']['TimeSlot'];
    };
    Job: {
      /**
       * Format: uuid
       * @description Unique identifier for the job
       */
      id: string;
      /**
       * Format: uuid
       * @description ID of the company that created the job
       */
      companyId: string;
      /**
       * Format: uuid
       * @description ID of the user (trade) assigned to the job
       */
      userId?: string | null;
      /** @description Name of the job category */
      categoryName: string;
      /** @description ID of the job category */
      categoryId: number;
      /** @description ID of the parent category */
      categoryParentId: number;
      /** @description Estimated hours to complete the job */
      estimatedHours: number;
      /** @description Job title */
      title: string;
      /** @description Detailed description of the job */
      description: string;
      /** @description Materials needed for the job */
      equipmentNeeded?: string | null;
      jobUrgency: components['schemas']['JobUrgency'];
      /**
       * Format: float
       * @description Price of the job
       */
      price: number;
      /**
       * @description Current status of the job
       * @enum {string}
       */
      status: components['schemas']['JobStatus'];
      /**
       * Format: date-time
       * @description When the job request expires (24 hours before earliest job time)
       */
      expiresAt: string;
      cancellation?: components['schemas']['JobCancellation'];
      /** @description Digital signature for job completion */
      completionSignature?: string | null;
      /**
       * Format: date-time
       * @description When the job completion was approved
       */
      completionApprovedAt?: string | null;
      /** @description Number of trades matched to this job */
      matchedTradesCount: number;
      dates?: components['schemas']['JobDate'][];
      location?: components['schemas']['JobLocation'];
      issueReports?: components['schemas']['JobIssueReport'][];
      history?: components['schemas']['JobHistory'][];
      edits?: components['schemas']['JobEdit'][];
      documents?: components['schemas']['JobDocument'][];
      tradeMatches?: components['schemas']['TradeJobMatch'][];
      /**
       * Format: date-time
       * @description When the job was created
       */
      createdAt: string;
      /**
       * Format: date-time
       * @description When the job was last updated
       */
      updatedAt: string;
      company?: components['schemas']['Company'];
      user?: components['schemas']['User'];
    };
    JobLocation: {
      /**
       * Format: uuid
       * @description Location ID
       */
      id?: string;
      /**
       * Format: uuid
       * @description Associated job ID
       */
      jobId?: string;
      /** @description Unique key for the address */
      addressKey?: string;
      /** @description Full address */
      address?: string;
      /** @description Postcode */
      postcode?: string;
      /** @description Name of the onsite contact person */
      onsiteContactName?: string;
      /** @description Phone number of the onsite contact */
      onsiteContactPhone?: string;
      /**
       * Format: email
       * @description Email of the onsite contact
       */
      onsiteContactEmail?: string;
      /** @description Name of the business contact person */
      businessContactName?: string;
      /** @description Phone number of the business contact */
      businessContactPhone?: string;
      /**
       * Format: email
       * @description Email of the business contact
       */
      businessContactEmail?: string;
      /** @description Whether parking is available */
      parkingAvailable?: boolean;
      /** @description Details about parking restrictions if any */
      parkingRestrictions?: string | null;
      propertyType?: components['schemas']['PropertyType'];
      /** @description Whether customer will be present */
      customerPresent?: boolean;
      /** @description Site preparation details if any */
      sitePreparation?: string | null;
    };
    JobDocument: {
      /**
       * Format: uuid
       * @description Unique identifier for the document
       */
      id: string;
      /**
       * Format: uuid
       * @description ID of the associated job
       */
      jobId: string;
      /**
       * Format: uri
       * @description Path to access the file (returns signed URL when retrieved)
       */
      filePath: string;
      /** @description Original name of the file */
      fileName: string;
      /** @description MIME type of the file */
      fileType: string;
      /** @description Size of the file in bytes */
      fileSize: number;
      /** @description Optional description of the file */
      description?: string | null;
      /**
       * @description Type of the document
       * @default OTHER
       */
      documentType: components['schemas']['JobDocumentType'];
      /**
       * Format: date-time
       * @description When the file was uploaded
       */
      uploadedAt: string;
      /**
       * Format: uuid
       * @description ID of the user who uploaded the file
       */
      uploadedById?: string | null;
    };
    JobIssueReport: {
      /**
       * Format: uuid
       * @description Unique identifier for the issue report
       */
      id: string;
      /**
       * Format: uuid
       * @description ID of the job this issue is reported for
       */
      jobId: string;
      /** @description Type of issue being reported */
      issueType: string;
      /** @description Detailed description of the issue */
      description: string;
      /**
       * Format: date-time
       * @description When the issue was reported
       */
      reportedAt: string;
      /**
       * Format: uuid
       * @description ID of the user who reported the issue
       */
      reportedBy: string;
    };
    TradeJobMatch: {
      /**
       * Format: uuid
       * @description Unique identifier for the match
       */
      id: string;
      /**
       * Format: uuid
       * @description ID of the job
       */
      jobId: string;
      /**
       * Format: uuid
       * @description ID of the trade
       */
      tradeId: string;
      /**
       * @description Status of the match
       * @default PENDING
       * @enum {string}
       */
      status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
      /** @description Reason for rejection if status is REJECTED */
      rejectionReason?: string | null;
      /**
       * Format: uuid
       * @description ID of the selected job date
       */
      selectedDateId?: string | null;
      /**
       * @description Whether the one day reminder has been sent
       * @default false
       */
      oneDayReminderSent: boolean;
      /**
       * @description Whether the two hour reminder has been sent
       * @default false
       */
      twoHourReminderSent: boolean;
      /** @description Selected date and time slot */
      selectedDate?: components['schemas']['JobDate'];
      /** @description Associated job details */
      job?: components['schemas']['Job'];
      /**
       * Format: date-time
       * @description When the match was created
       */
      createdAt: string;
      /**
       * Format: date-time
       * @description When the match was last updated
       */
      updatedAt: string;
      /**
       * Format: float
       * @description Suggested price if provided during rejection
       */
      suggestedPrice?: number | null;
    };
    /**
     * @description Source of a job history change
     * @enum {string}
     */
    ChangeSource: 'SYSTEM' | 'USER' | 'TRADE';
    JobHistory: {
      /**
       * Format: uuid
       * @description Unique identifier for the history entry
       */
      id: string;
      /**
       * Format: uuid
       * @description ID of the associated job
       */
      jobId: string;
      status: components['schemas']['JobStatus'];
      /**
       * Format: date-time
       * @description When the change occurred
       */
      changedAt: string;
      /** @default SYSTEM */
      changeSource: components['schemas']['ChangeSource'];
      /**
       * Format: uuid
       * @description ID of who made the change (can be userId, tradeId, or null for system)
       */
      referenceId?: string | null;
      /** @description Name of who made the change (e.g. "John Doe", "Trade Company Name", "System") */
      changedByName?: string | null;
      /** @description Detailed description of what happened */
      changeDetails?: string | null;
    };
    JobEdit: {
      /**
       * Format: uuid
       * @description Unique identifier for the edit
       */
      id: string;
      /**
       * Format: uuid
       * @description ID of the associated job
       */
      jobId: string;
      /** @description Name of the field that was edited */
      fieldName: string;
      /** @description Previous value of the field */
      oldValue: string;
      /** @description New value of the field */
      newValue: string;
      /**
       * Format: date-time
       * @description When the edit was made
       */
      editedAt: string;
      /**
       * Format: uuid
       * @description ID of the user who made the edit
       */
      createdById?: string | null;
    };
    /**
     * @description Type of business cancellation
     * @enum {string}
     */
    BusinessCancellationType:
      | 'MORE_THAN_24H'
      | 'WITHIN_24H'
      | 'WITHIN_2H'
      | 'NO_ACCESS'
      | 'INACCURATE_DESCRIPTION'
      | 'TRADE_CANCELLATION';
    /**
     * @description Who cancelled the job
     * @enum {string}
     */
    CancelledBy: 'BUSINESS' | 'TRADE' | 'SYSTEM';
    JobCancellation: {
      /**
       * Format: uuid
       * @description Unique identifier for the cancellation
       */
      id: string;
      /**
       * Format: uuid
       * @description ID of the cancelled job
       */
      jobId: string;
      /** @description User-provided cancellation type */
      userCancellationType: string;
      businessCancellationType: components['schemas']['BusinessCancellationType'];
      /** @description Reason for cancellation */
      reason: string;
      cancelledBy: components['schemas']['CancelledBy'];
      /** @description Amount in minor units (pence). This is the amount refunded to the business, including VAT. */
      businessRefund: number;
      /** @description Amount in minor units (pence). This is what the trade receives after deducting Checkatrade fee, including VAT. */
      tradeCompensation: number;
      /** @description Amount in minor units (pence). Our service fee calculated on pre-VAT amount and then VAT added. */
      checkatradeFee: number;
      /** @description Amount in minor units (pence). Total fee charged for cancellation (trade_compensation + checkatrade_fee), including VAT. */
      totalCancellationFee: number;
      /**
       * Format: decimal
       * @description The effective service fee percentage, calculated on pre-VAT amounts.
       */
      effectiveRate: number;
      /**
       * Format: uuid
       * @description ID of the user who cancelled the job
       */
      cancelledByUserId?: string | null;
      /** @description If cancelled by a trade, this will store the trade's company ID */
      tradeCompanyId?: string | null;
      /**
       * Format: date-time
       * @description When the job was cancelled
       */
      cancelledAt: string;
      /**
       * Format: date-time
       * @description If the job had an accepted match at the time of cancellation, this will store the scheduled date
       */
      scheduledDate?: string | null;
      /**
       * @description Whether the job was eligible for rematch after cancellation (only applicable for trade cancellations)
       * @default false
       */
      eligibleForRematch: boolean;
    };
    Company: {
      /**
       * Format: uuid
       * @description Unique identifier for the company
       */
      id?: string;
      /** @description Company name */
      name?: string;
      /** @description Size of the company */
      size?: string;
      /** @description Type of business */
      businessType?: string;
      /**
       * @description Company status
       * @enum {string}
       */
      status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
      /**
       * Format: date-time
       * @description When the company was created
       */
      createdAt?: string;
      /**
       * Format: date-time
       * @description When the company was last updated
       */
      updatedAt?: string;
    };
    User: {
      /**
       * Format: uuid
       * @description Unique identifier for the user
       */
      id?: string;
      /**
       * Format: email
       * @description User's email address
       */
      email?: string;
      /** @description User's first name */
      firstName?: string;
      /** @description User's last name */
      lastName?: string;
      /**
       * Format: uuid
       * @description ID of the associated company
       */
      companyId?: string;
      /** @description User's roles */
      roles?: string[];
      /**
       * @description User's status
       * @enum {string}
       */
      status?: 'ACTIVE' | 'INACTIVE';
      /**
       * Format: date-time
       * @description When the user was created
       */
      createdAt?: string;
      /**
       * Format: date-time
       * @description When the user was last updated
       */
      updatedAt?: string;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export type operations = Record<string, never>;
