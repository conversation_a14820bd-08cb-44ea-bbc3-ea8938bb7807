/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/api/v1/accreditationlibrary/{accreditationId}/accreditation': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Updates or inserts into the Firestore accreditation library for a given accreditation */
    post: operations['UpsertAccreditation'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/api/v1/company/{companyId}/credit': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Retrieves the given company current maximum credit and credit safe limit */
    get: operations['GetTradeCredit'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/api/v1/company/{companyId}/member-promise': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Retrieves the given company's current status of a member promise which helps a member
     *     to take actions to improve the performance of their membership.
     * @description Description
     *     -----------
     *     _ Retrieves the given company's current status of member promise, as configured by
     *     Checkatrade._
     *     _The source of the data is Salesforce._
     *     _The results are not cached._
     */
    get: operations['GetMemberPromise'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/api/v1/vettinghandoff': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Updates or inserts into the Firestore company accreditations collection. */
    post: operations['UpsertAccreditations'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/api/v1/contact/{userId}/preferences': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Provides a list of Marketing Preferences for a given UserId
     * @description Description
     *     -----------
     *     _This endpoint will return a list of Marketing Preferences for an individual user's GUID_
     *     _The source of the data is Salesforce_
     *     _No strict ordering has been applied_
     *     _The results are not cached_
     *     [click for Mobile Page](https://checkatrade.atlassian.net/wiki/spaces/MPT/overview?homepageId=3286892548)
     *     [click for Architectural
     *     Diagrams](https://checkatrade.atlassian.net/wiki/spaces/MPT/pages/3337716085/TE+Salesforce+GCP+Architecture)
     */
    get: operations['GetPreferences'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * Updates the Marketing Preferences preferences for a given user
     * @description Description
     *     -----------
     *     _This endpoint will update the Marketing Preferences for an individual user's GUID_
     *     _The source of the data is Salesforce_
     *     [click for Mobile Page](https://checkatrade.atlassian.net/wiki/spaces/MPT/overview?homepageId=3286892548)
     *     [click for Architectural
     *     Diagrams](https://checkatrade.atlassian.net/wiki/spaces/MPT/pages/3337716085/TE+Salesforce+GCP+Architecture)
     */
    patch: operations['PatchPreferences'];
    trace?: never;
  };
  '/api/v1/leads/secure-contacts-logs/{companyId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Gets paginated secure contact logs for the given companyId
     * @description     Description
     *         -----------
     *         _Gets the call and sms logs from secure contacts for the given companyId._
     *     	_Only users with a valid token that grants access to the provided companyId will get results._
     *     	_The source of the data is Secure Contacts API._
     *         _The results are not cached._
     */
    get: operations['GetSecureContactsLogsForCompany'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: {
    Accreditation: {
      /** Format: int32 */
      Accred_ID?: number;
      /** Format: int32 */
      Company_ID?: number;
      name?: string | null;
      /** Format: int32 */
      Accreditation_Type?: number;
      /** Format: date-time */
      Expiry_Date?: string | null;
      Registration_Number?: string | null;
      SFAccred_Status?: string | null;
      /** Format: int32 */
      Accred_Status?: number;
      Accred_Note?: string | null;
      Accred_Proof?: string | null;
      Visible_On_Profile?: boolean;
      /** Format: date-time */
      Approved_Dt?: string | null;
      /** Format: date-time */
      Modify_Dt?: string | null;
      /** Format: int32 */
      Modify_by?: number | null;
      /** Format: int32 */
      Status_ind?: number | null;
    };
    AccreditationLibraryDto: {
      correlationId?: string | null;
      originator?: string | null;
      /** Format: int32 */
      accreditationId?: number;
      name?: string | null;
      description?: string | null;
      logoFilename?: string | null;
      url?: string | null;
      urlText?: string | null;
      visibleOnProfile?: boolean;
      requiresApproval?: boolean;
      canExpire?: boolean;
      isDeleted?: boolean;
    };
    /**
     * Format: string
     * @enum {string}
     */
    CommunicationMethod: 'CALL' | 'SMS';
    CompanyAccreditationHandoffMessage: {
      accreditations?: components['schemas']['Accreditation'][] | null;
      correlationId?: string | null;
      originator?: string | null;
    };
    ContactPreferencesBundleDto: {
      group?: string | null;
      preferences?: {
        [key: string]: boolean;
      } | null;
    };
    ContactPreferencesDto: {
      group?: string | null;
      preferences?: components['schemas']['PreferencesDto'];
    };
    ContactPreferencesMessageDto: {
      'contact-preferences'?:
        | components['schemas']['ContactPreferencesBundleDto'][]
        | null;
    };
    /**
     * Format: string
     * @enum {string}
     */
    InboundSortableFields: 'contactTime';
    MemberPromiseReadDto: {
      status?: boolean;
      errorMessage?: string | null;
    };
    PreferencesDto: {
      email?: boolean;
      phone?: boolean;
      post?: boolean;
      sms?: boolean;
    };
    ProblemDetails: {
      type?: string | null;
      title?: string | null;
      /** Format: int32 */
      status?: number | null;
      detail?: string | null;
      instance?: string | null;
    };
    SalesforceCreditDto: {
      /** Format: double */
      maximumCredit?: number | null;
      /** Format: double */
      creditSafeLimit?: number | null;
      errorMessage?: string | null;
    };
    SecureContactLogDto: {
      secureContactId?: string | null;
      rowKey?: string | null;
      communicationMethod?: components['schemas']['CommunicationMethod'];
      fromPhoneNumber?: string | null;
      /** Format: date-time */
      contactTime?: string;
      wasSuccessfulCall?: boolean;
      /** Format: int32 */
      durationSeconds?: number | null;
    };
    /**
     * Format: string
     * @enum {string}
     */
    SortDirection: 'ASCENDING' | 'DESCENDING';
    ValidationProblemDetails: {
      type?: string | null;
      title?: string | null;
      /** Format: int32 */
      status?: number | null;
      detail?: string | null;
      instance?: string | null;
      readonly errors?: {
        [key: string]: string[];
      } | null;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
  UpsertAccreditation: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description Accreditation Id */
        accreditationId: number;
      };
      cookie?: never;
    };
    /** @description Accreditation library Dto */
    requestBody?: {
      content: {
        'application/json': components['schemas']['AccreditationLibraryDto'];
        'text/json': components['schemas']['AccreditationLibraryDto'];
        'application/*+json': components['schemas']['AccreditationLibraryDto'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  GetTradeCredit: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        companyId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['SalesforceCreditDto'];
          'application/json': components['schemas']['SalesforceCreditDto'];
          'text/json': components['schemas']['SalesforceCreditDto'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  GetMemberPromise: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        companyId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['MemberPromiseReadDto'];
          'application/json': components['schemas']['MemberPromiseReadDto'];
          'text/json': components['schemas']['MemberPromiseReadDto'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  UpsertAccreditations: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['CompanyAccreditationHandoffMessage'];
        'text/json': components['schemas']['CompanyAccreditationHandoffMessage'];
        'application/*+json': components['schemas']['CompanyAccreditationHandoffMessage'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
    };
  };
  GetPreferences: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        userId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ContactPreferencesDto'][];
          'application/json': components['schemas']['ContactPreferencesDto'][];
          'text/json': components['schemas']['ContactPreferencesDto'][];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  PatchPreferences: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        userId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['ContactPreferencesMessageDto'];
        'text/json': components['schemas']['ContactPreferencesMessageDto'];
        'application/*+json': components['schemas']['ContactPreferencesMessageDto'];
      };
    };
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description No Content */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
  GetSecureContactsLogsForCompany: {
    parameters: {
      query?: {
        Page?: number;
        Limit?: number;
        FromDate?: string;
        ToDate?: string;
        SortBy?: components['schemas']['InboundSortableFields'];
        SortDirection?: components['schemas']['SortDirection'];
        IncludeUnsuccessful?: boolean;
      };
      header?: never;
      path: {
        companyId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['SecureContactLogDto'][];
          'application/json': components['schemas']['SecureContactLogDto'][];
          'text/json': components['schemas']['SecureContactLogDto'][];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ValidationProblemDetails'];
          'application/json': components['schemas']['ValidationProblemDetails'];
          'text/json': components['schemas']['ValidationProblemDetails'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'text/plain': components['schemas']['ProblemDetails'];
          'application/json': components['schemas']['ProblemDetails'];
          'text/json': components['schemas']['ProblemDetails'];
        };
      };
    };
  };
}
