import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { paths, components } from 'src/data/api-specs/member-data-api';
import { createApiClient } from 'src/data/apiClient';
import {
  CreditResponseSchema,
  MarketingPreferencesSchema,
  MemberPromiseResponseSchema,
  SecureContactLogsResponseSchema,
  type CreditResponseType,
  type MarketingPreferencesType,
  type MemberPromiseResponseType,
} from 'src/data/schemas/api/member-data';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { getFirebaseAuthToken } from 'src/services/firebase/auth';

class MemberDataApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: config.memberDataApiBaseUrl,
      getBearerToken: (forceRefresh) => getFirebaseAuthToken(forceRefresh),
    });

  public async getTradeCredit(
    companyId: number,
  ): Promise<AxiosResponse<CreditResponseType>> {
    const response = await this.client.get(
      '/api/v1/company/{companyId}/credit',
      {
        params: { path: { companyId: companyId.toString() } },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(CreditResponseSchema, response.data),
    };
  }

  public async getMemberPromise(
    companyId: number,
  ): Promise<AxiosResponse<MemberPromiseResponseType>> {
    const response = await this.client.get(
      '/api/v1/company/{companyId}/member-promise',
      {
        params: { path: { companyId: companyId.toString() } },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        MemberPromiseResponseSchema,
        response.data,
      ),
    };
  }

  public async getMarketingPreferences(
    userId: string,
  ): Promise<AxiosResponse<MarketingPreferencesType>> {
    const response = await this.client.get(
      '/api/v1/contact/{userId}/preferences',
      { params: { path: { userId } } },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        MarketingPreferencesSchema,
        response.data,
      ),
    };
  }

  public async updateMarketingPreferences(
    userId: string,
    requestBody: MarketingPreferencesType,
  ) {
    return this.client.patch('/api/v1/contact/{userId}/preferences', {
      params: { path: { userId } },
      body: {
        'contact-preferences':
          requestBody as components['schemas']['ContactPreferencesBundleDto'][],
      },
    });
  }

  public async getSecureContacts({
    companyId,
    pageParam,
    limit,
  }: {
    companyId: string;
    pageParam: number;
    limit?: number;
  }) {
    const response = await this.client.get(
      '/api/v1/leads/secure-contacts-logs/{companyId}',
      {
        params: {
          path: { companyId },
          query: { Page: pageParam, Limit: limit || 10 },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        SecureContactLogsResponseSchema,
        response.data,
      ),
    };
  }
}

export const memberDataApi = new MemberDataApi();
