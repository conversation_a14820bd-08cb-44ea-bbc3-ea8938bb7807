import { config } from 'src/config';
import { getFirebaseAuthToken } from 'src/services/firebase/auth';
import { BrazeExternalIdResponseSchema } from 'src/data/schemas/api/communication/BrazeExternalIdResponse';
import { paths } from 'src/data/api-specs/communication-api';
import { AxiosError } from 'axios';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { createApiClient } from '../../apiClient';

class CommunicationApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: config.communicationsApiBaseUrl,
      getBearerToken: (forceRefresh) => getFirebaseAuthToken(forceRefresh),
    });

  public async getBrazeExternalId() {
    type ExpectedResponse = { external_id?: string };

    try {
      const response = await this.client.get(
        '/api/trade/user/get-external-id',
        {},
      );

      return {
        ...response,
        data: parseSchemaWithErrorCapture(
          BrazeExternalIdResponseSchema,
          response.data,
        ),
      };
    } catch (error) {
      const axiosError = error as AxiosError<ExpectedResponse>;
      if (!axiosError.response || axiosError.response.status !== 404) {
        throw error;
      }

      return {
        ...axiosError.response,
        data: {},
      };
    }
  }
}

export const communicationApi = new CommunicationApi();
