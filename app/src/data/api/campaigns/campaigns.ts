import { AxiosResponse } from 'axios';
import { format } from 'date-fns';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import {
  CreateCampaignApiSchema,
  type CreateCampaignRequestBody,
  type UpdateGeographiesBodyType,
  type UpdateStatusApiRequest,
  type UpdateSubCategoriesBodyType,
  UpdateAvailabilityApiSchema,
  UpdateGeographiesApiSchema,
  UpdateStatusApiSchema,
  UpdateSubcategoriesApiSchema,
  UpdateParentCategoryBodyType,
  UpdateParentCategoryApiSchema,
  CampaignDtoSchema,
  type CampaignDtoType,
  CampaignDtoPageResultType,
  CampaignDtoPageResult,
  QuoteRequestSource,
  GetUpgradeOptionsBodyType,
  UpdateMaxSpendApiSchema,
  type UpdateMaxSpendApiRequestBody,
  ConvertQuoteRequestBody,
  ConvertQuoteRequestBodySchema,
  CreateQuoteRequestRequestApiSchema,
  CreateQuoteRequestRequestBody,
  ReforecastQuoteRequestBody,
  ReforecastQuoteRequestBodySchema,
  ReforecastQuoteResponse,
  ReforecastQuoteResponseSchema,
  UpgradeCampaignRequestBodyType,
  UpgradeCampaignRequestBodySchema,
  UpgradeCampaignResponseType,
  RateBidsType,
} from 'src/data/schemas/api/campaigns';
import type { paths } from 'src/data/api-specs/campaigns-api';
import { getFirebaseAuthToken } from 'src/services/firebase/auth';
import {
  LeadDto,
  LeadDtoPageResult,
  LeadType,
} from 'src/data/schemas/api/campaigns/LeadDtoPageResult';
import {
  DisputeLeadApiSchema,
  DisputeLeadBodyType,
} from 'src/data/schemas/api/campaigns/CreateLeadDisputeBatchRequest';
import { CreateLeadDisputeBatchResponseSchema } from 'src/data/schemas/api/campaigns/CreateLeadDisputeBatchResponse';
import {
  QuoteRequestResponse,
  quoteRequestResponseSchema,
} from 'src/data/schemas/api/campaigns/QuoteRequestResponse';
import {
  PriceRangesDtoPageResult,
  PriceRangesDtoPageResultType,
} from 'src/data/schemas/api/campaigns/PriceRangesDtoPageResult';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { recommendedLocationsResponseSchema } from 'src/data/schemas/api/campaigns/RecommendedLocationsResponse';
import {
  GetParentCategoryDtoSchema,
  GetParentCategoryDtoType,
} from 'src/data/schemas/api/campaigns/GetParentCategoryDto';
import { RecommendedLocationsRequestBodyType } from 'src/data/schemas/api/campaigns/RecommendedLocationsRequest';
import {
  CampaignStatisticsSummaryDtoSchema,
  CampaignStatisticsSummaryLevel,
} from 'src/data/schemas/api/campaigns/CampaignStatisticsSummaryDto';

class CampaignsApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: config.campaignsBaseUrl,
      getBearerToken: (forceRefresh) => getFirebaseAuthToken(forceRefresh),
    });

  public async createCampaign(
    companyId: number,
    body: CreateCampaignRequestBody,
  ) {
    const requestBody = parseSchemaWithErrorCapture(
      CreateCampaignApiSchema,
      body,
    );
    return this.client.post('/v1/trades/{companyId}/campaigns', {
      params: {
        path: {
          companyId: companyId.toString(),
        },
      },
      body: requestBody,
    });
  }

  public async getCampaign(
    companyId: number,
    campaignId: string,
  ): Promise<AxiosResponse<CampaignDtoType>> {
    const response = await this.client.get(
      '/v1/trades/{companyId}/campaigns/{campaignId}',
      {
        params: { path: { companyId: companyId.toString(), campaignId } },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(CampaignDtoSchema, response.data),
    };
  }

  public async getCampaigns(
    companyId: number,
    options?: {
      includeInactive: boolean;
      pageSize: number;
      offset?: number;
      sort?: string[];
    },
  ): Promise<AxiosResponse<CampaignDtoPageResultType>> {
    const response = await this.client.get('/v1/trades/{companyId}/campaigns', {
      params: {
        path: { companyId: companyId.toString() },
        query: {
          offset: options?.offset,
          includeInactive: options?.includeInactive,
          pageSize: options?.pageSize,
          sort: options?.sort,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(CampaignDtoPageResult, response.data),
    };
  }

  public async updateCampaignAvailability(
    companyId: number,
    campaignId: string,
    pausedUntil?: Date | null,
  ) {
    const formattedDate = pausedUntil
      ? format(pausedUntil, 'yyyy-MM-dd')
      : undefined;

    const requestBody = parseSchemaWithErrorCapture(
      UpdateAvailabilityApiSchema,
      {
        pausedUntil: formattedDate,
      },
    );

    return this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/updateAvailability',
      {
        params: {
          path: {
            companyId: companyId.toString(),
            campaignId,
          },
        },
        body: requestBody,
      },
    );
  }

  public async updateCampaignStatus(
    companyId: number,
    campaignId: string,
    body: UpdateStatusApiRequest,
  ) {
    const requestBody = parseSchemaWithErrorCapture(
      UpdateStatusApiSchema,
      body,
    );
    return this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/updateStatus',
      {
        params: {
          path: {
            companyId: companyId.toString(),
            campaignId,
          },
        },
        body: requestBody,
      },
    );
  }

  public async updateSubCategories(
    companyId: number,
    campaignId: string,
    body: UpdateSubCategoriesBodyType,
  ) {
    const requestBody = parseSchemaWithErrorCapture(
      UpdateSubcategoriesApiSchema,
      body,
    );
    return this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/updateSubCategories',
      {
        params: {
          path: {
            companyId: companyId.toString(),
            campaignId,
          },
        },
        body: requestBody,
      },
    );
  }

  public async getParentCategory(
    parentCategoryId: number,
  ): Promise<AxiosResponse<GetParentCategoryDtoType>> {
    const response = await this.client.get(
      '/v1/categories/{parentCategoryId}',
      {
        params: { path: { parentCategoryId } },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        GetParentCategoryDtoSchema,
        response.data,
      ),
    };
  }

  public async updateParentCategory(
    companyId: number,
    campaignId: string,
    body: UpdateParentCategoryBodyType,
  ) {
    const requestBody = parseSchemaWithErrorCapture(
      UpdateParentCategoryApiSchema,
      body,
    );
    return this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/updateParentCategory',
      {
        params: {
          path: {
            companyId: companyId.toString(),
            campaignId,
          },
        },
        body: requestBody,
      },
    );
  }

  public async updateMaxSpend(
    companyId: number,
    campaignId: string,
    body: UpdateMaxSpendApiRequestBody,
  ) {
    const requestBody = parseSchemaWithErrorCapture(
      UpdateMaxSpendApiSchema,
      body,
    );
    return this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/updateMaxSpend',
      {
        params: {
          path: {
            companyId: companyId.toString(),
            campaignId,
          },
        },
        body: requestBody,
      },
    );
  }

  public async updateGeographies(
    companyId: number,
    campaignId: string,
    body: UpdateGeographiesBodyType,
  ) {
    const requestBody = parseSchemaWithErrorCapture(
      UpdateGeographiesApiSchema,
      body,
    );
    return this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/updateGeographies',
      {
        params: {
          path: {
            companyId: companyId.toString(),
            campaignId,
          },
        },
        body: requestBody,
      },
    );
  }

  public async updateRateBids(
    companyId: number,
    campaignId: string,
    body: RateBidsType[],
  ) {
    return this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/updateRateBids',
      {
        params: {
          path: {
            companyId: companyId.toString(),
            campaignId,
          },
        },
        body: { rateBids: body },
      },
    );
  }

  public async getCampaignStatisticsSummary(
    companyId: number,
    campaignId: string,
    options: {
      summaryLevel: CampaignStatisticsSummaryLevel;
    },
  ) {
    const response = await this.client.get(
      '/v1/trades/{companyId}/campaigns/{campaignId}/statistics',
      {
        params: {
          path: { companyId: companyId.toString(), campaignId },
          query: { summaryLevel: options.summaryLevel },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        CampaignStatisticsSummaryDtoSchema,
        response.data,
      ),
    };
  }

  public async fetchLeads(
    companyId: number,
    campaignId: string,
    options: {
      offset?: number;
      pageSize?: number;
      budgetPeriod?: Date;
      dateGeneratedTo?: string | undefined;
      dateGeneratedFrom?: string | undefined;
      sort?: string;
    },
  ) {
    const response = await this.client.get('/v1/trades/{companyId}/leads', {
      params: {
        path: {
          companyId: companyId.toString(),
        },
        query: {
          campaignId: campaignId.toString(),
          offset: options.offset,
          pageSize: options.pageSize,
          budgetPeriod: options.budgetPeriod?.toString(),
          dateGeneratedTo: options.dateGeneratedTo,
          dateGeneratedFrom: options.dateGeneratedFrom,
          sort: [`dateGenerated:${options.sort ?? 'asc'}`],
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(LeadDtoPageResult, response.data),
    };
  }

  public async fetchLeadDetails(
    companyId: string,
    leadId: string,
    options?: {
      leadType: LeadType;
    },
  ) {
    const response = await this.client.get(
      '/v1/trades/{companyId}/leads/{leadId}',
      {
        params: {
          path: {
            companyId: companyId,
            leadId: leadId,
          },
          query: {
            leadType: options?.leadType,
          },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(LeadDto, response.data),
    };
  }

  public async disputeLead(
    companyId: string,
    leadId: string,
    body: DisputeLeadBodyType,
  ) {
    const requestBody = parseSchemaWithErrorCapture(DisputeLeadApiSchema, body);
    const response = await this.client.post(
      '/v1/trades/{companyId}/leadDisputes/batch',
      {
        params: {
          path: {
            companyId: companyId,
            leadId: leadId,
          },
        },
        body: requestBody,
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        CreateLeadDisputeBatchResponseSchema,
        response.data,
      ),
    };
  }

  public async getUpgradeOptions(
    companyId: number,
    campaignId: string,
    amendment?: GetUpgradeOptionsBodyType,
  ) {
    const response = await this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/upgradeOptions',
      {
        params: {
          path: {
            companyId: companyId.toString(),
            campaignId,
          },
        },
        body: {
          source: QuoteRequestSource.TradeApp,
          amendment: amendment,
        },
      },
    );

    return {
      ...response,
      data: quoteRequestResponseSchema.parse(response.data),
    };
  }

  public async getQuoteRequest(
    quoteRequestId: string,
  ): Promise<AxiosResponse<QuoteRequestResponse>> {
    const response = await this.client.get(
      '/v1/quoteRequests/{quoteRequestId}',
      {
        params: {
          path: {
            quoteRequestId,
          },
        },
      },
    );

    return {
      ...response,
      data: quoteRequestResponseSchema.parse(response.data),
    };
  }

  public async createQuoteRequest(
    body: CreateQuoteRequestRequestBody,
  ): Promise<AxiosResponse<QuoteRequestResponse>> {
    const requestBody = parseSchemaWithErrorCapture(
      CreateQuoteRequestRequestApiSchema,
      body,
    );
    const response = await this.client.post('/v1/quoteRequests', {
      body: requestBody,
    });

    return {
      ...response,
      data: quoteRequestResponseSchema.parse(response.data),
    };
  }

  public async reforecastQuoteRequest(
    quoteRequestId: string,
    quoteId: string,
    body: ReforecastQuoteRequestBody,
  ): Promise<AxiosResponse<ReforecastQuoteResponse>> {
    const requestBody = parseSchemaWithErrorCapture(
      ReforecastQuoteRequestBodySchema,
      body,
    );

    const response = await this.client.post(
      '/v1/quoteRequests/{quoteRequestId}/quotes/{quoteId}/reforecast',
      {
        params: {
          path: {
            quoteRequestId,
            quoteId,
          },
        },
        body: requestBody,
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        ReforecastQuoteResponseSchema,
        response.data,
      ),
    };
  }

  public async convertQuoteRequest(
    quoteRequestId: string,
    quoteId: string,
    body: ConvertQuoteRequestBody,
  ): Promise<AxiosResponse<CampaignDtoType>> {
    const requestBody = parseSchemaWithErrorCapture(
      ConvertQuoteRequestBodySchema,
      body,
    );

    const response = await this.client.post(
      '/v1/quoteRequests/{quoteRequestId}/quotes/{quoteId}/convert',
      {
        params: {
          path: {
            quoteRequestId,
            quoteId,
          },
        },
        body: requestBody,
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(CampaignDtoSchema, response.data),
    };
  }

  public async upgradeCampaign(
    companyId: number,
    campaignId: string,
    requestBody: UpgradeCampaignRequestBodyType,
  ): Promise<AxiosResponse<UpgradeCampaignResponseType>> {
    const body = parseSchemaWithErrorCapture(
      UpgradeCampaignRequestBodySchema,
      requestBody,
    );
    return this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/upgrade',
      {
        params: {
          path: {
            companyId: companyId.toString(),
            campaignId,
          },
        },
        body,
      },
    );
  }

  public async getCampaignPriceRanges(
    companyId: number,
    campaignId: string,
  ): Promise<AxiosResponse<PriceRangesDtoPageResultType>> {
    const response = await this.client.post(
      '/v1/trades/{companyId}/campaigns/{campaignId}/priceRanges',
      {
        params: { path: { companyId: companyId.toString(), campaignId } },
        body: {},
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        PriceRangesDtoPageResult,
        response.data,
      ),
    };
  }

  public async getRecommendedLocations(
    requestBody: RecommendedLocationsRequestBodyType,
  ) {
    const response = await this.client.post('/v1/regions/recommendations', {
      body: requestBody,
    });

    return {
      ...response,
      data: recommendedLocationsResponseSchema.parse(response.data),
    };
  }
}

export const campaignsApi = new CampaignsApi();
