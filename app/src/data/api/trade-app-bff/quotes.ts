import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { getAccessToken } from 'src/auth/utils/authSession';
import type { Quote } from 'src/screens/QuotesAndInvoices/quotes.types';

import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { GetQuoteTokenResponseSchema } from 'src/data/schemas/api/trade-app-bff/quotes';
import type { GetQuoteTokenResponseType } from 'src/data/schemas/api/trade-app-bff/quotes';
import { transformQuoteToCreateQuoteRequest } from './transformQuoteToCreateQuoteRequest';

interface IGetQuotesByOpportunityId {
  opportunityId: string;
  limit: number;
  lastVisible?: string;
  companyId: string;
}

class QuotesTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async createQuote(
    quoteDetails: Quote,
    companyId: number,
  ): Promise<AxiosResponse<{ quoteId: string }>> {
    const quoteData = transformQuoteToCreateQuoteRequest(quoteDetails);
    return this.client.post('/quotes', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
      body: quoteData,
    });
  }

  public async getQuoteToken(
    quoteId: string,
    companyId: number,
  ): Promise<AxiosResponse<GetQuoteTokenResponseType>> {
    const response = await this.client.post('/quotes/{quoteId}/token', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { quoteId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        GetQuoteTokenResponseSchema,
        response.data,
      ),
    };
  }

  public async shareDraftQuote(
    quoteId: string,
    companyId: number,
  ): Promise<AxiosResponse> {
    const response = await this.client.post('/quotes/{quoteId}/draft', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { quoteId },
      },
    });

    return response;
  }

  public async deleteQuote(
    quoteId: string,
    companyId: number,
  ): Promise<AxiosResponse> {
    return this.client.delete('/quotes/{quoteId}', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { quoteId },
      },
    });
  }

  public async shareQuote(
    quoteId: string,
    companyId: number,
  ): Promise<AxiosResponse> {
    const response = await this.client.post('/quotes/{quoteId}/share', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { quoteId },
      },
    });

    return response;
  }
  public async getQuotesByOpportunityId({
    opportunityId,
    limit,
    lastVisible,
    companyId,
  }: IGetQuotesByOpportunityId): Promise<AxiosResponse> {
    const response = await this.client.get(
      '/quotes/opportunity/{opportunityId}',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
          path: { opportunityId },
          query: { limit, lastVisible },
        },
      },
    );

    return response;
  }
}

export const quotes = new QuotesTradeBffApi();
