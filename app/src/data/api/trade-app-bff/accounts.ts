import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { AccountsResponse } from 'src/data/schemas/api/trade-app-bff/accounts';
import { getAccessToken } from 'src/auth/utils/authSession';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';

class AccountsTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getAccounts(): Promise<AxiosResponse<AccountsResponse>> {
    const response = await this.client.get('/accounts', {});
    return {
      ...response,
      data: parseSchemaWithErrorCapture(AccountsResponse, response.data),
    };
  }

  public async blockCustomer(
    jobId: string,
    consumerId: string,
    companyId: number,
  ): Promise<AxiosResponse> {
    return this.client.post('/consumers/{consumerId}/report', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { consumerId },
      },
      body: { jobId },
    });
  }
}

export const accounts = new AccountsTradeBffApi();
