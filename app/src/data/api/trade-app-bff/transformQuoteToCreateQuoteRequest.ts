import {
  CreateQuoteRequestType,
  QuoteRequestSchema,
} from 'src/data/schemas/api/trade-app-bff/quotes/CreateQuoteRequest';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { Quote } from 'src/screens/QuotesAndInvoices/quotes.types';

export const transformQuoteToCreateQuoteRequest = (
  input: Quote,
): CreateQuoteRequestType => {
  return parseSchemaWithErrorCapture(QuoteRequestSchema, {
    ...input,
    consumerDetails: {
      ...input.homeownerDetails,
    },
  });
};
