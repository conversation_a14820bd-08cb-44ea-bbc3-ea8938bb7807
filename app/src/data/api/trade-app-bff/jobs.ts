import { AxiosResponse } from 'axios';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { config } from 'src/config';
import { getAccessToken } from 'src/auth/utils/authSession';
import {
  GetJobRejectReasons,
  getJobRejectReasonsSchema,
  GetJobsRequestType,
  GetJobsResponseType,
  GetJobResponseType,
  getJobResponseSchema,
  getJobsResponseSchema,
  GetJobCancelReasons,
  getJobCancelReasonsSchema,
  RejectOrCancelJobReason,
  GetUnreadJobsCountResponseType,
  getUnreadJobsCountResponse,
  GetPropertyFactsResponseType,
  getPropertyFactsSchema,
} from 'src/data/schemas/api/capi/jobs';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import {
  JobAppointmentsRequestType,
  jobAppointmentsResponseSchema,
  JobAppointmentsResponseType,
} from 'src/data/schemas/api/capi/appointments';
import {
  GetReviewRequestsRequest,
  getReviewRequestsSchema,
} from 'src/data/schemas/api/capi/review-requests';

class JobsTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getJob(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse<GetJobResponseType>> {
    const response = await this.client.get('/jobs/{jobId}', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getJobResponseSchema, response.data),
    };
  }

  public async getJobs({
    tab,
    page = 1,
    size = 10,
    companyId,
  }: GetJobsRequestType): Promise<AxiosResponse<GetJobsResponseType>> {
    const response = await this.client.get('/jobs', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        query: { tab, page, size },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getJobsResponseSchema, response.data),
    };
  }

  public async cancelJob(
    jobId: string,
    companyId: number,
    cancellationReason?: RejectOrCancelJobReason,
  ): Promise<AxiosResponse<GetJobResponseType>> {
    const response = await this.client.post('/jobs/{jobId}/cancel', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
      body: { reason: cancellationReason },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getJobResponseSchema, response.data),
    };
  }

  public async getJobAppointments(
    jobId: string,
    { page, size, active }: JobAppointmentsRequestType,
    companyId: number,
  ): Promise<AxiosResponse<JobAppointmentsResponseType>> {
    const response = await this.client.get('/jobs/{jobId}/appointments', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: {
          jobId,
        },
        query: {
          page,
          size,
          active,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        jobAppointmentsResponseSchema,
        response.data,
      ),
    };
  }

  public async getPropertyFactsForJob(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse<GetPropertyFactsResponseType>> {
    const response = await this.client.get('/jobs/{jobId}/property-facts', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getPropertyFactsSchema, response.data),
    };
  }

  public async getJobRejectionReasons(
    companyId: number,
  ): Promise<AxiosResponse<GetJobRejectReasons>> {
    const response = await this.client.get('/job/reject-reasons', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getJobRejectReasonsSchema,
        response.data,
      ),
    };
  }

  public async getJobCancellationReasons(
    companyId: number,
  ): Promise<AxiosResponse<GetJobCancelReasons>> {
    const response = await this.client.get('/job/cancel-reasons', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getJobCancelReasonsSchema,
        response.data,
      ),
    };
  }

  public async acceptJob(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse<GetJobResponseType>> {
    const response = await this.client.post('/jobs/{jobId}/accept', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getJobResponseSchema, response.data),
    };
  }

  public async getUnreadJobsCount(
    companyId: number,
  ): Promise<AxiosResponse<GetUnreadJobsCountResponseType>> {
    const response = await this.client.get('/jobs/count-unread', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getUnreadJobsCountResponse,
        response.data,
      ),
    };
  }

  public async markJobAsRead(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse<GetJobResponseType>> {
    const response = await this.client.post('/jobs/{jobId}/mark-as-read', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getJobResponseSchema, response.data),
    };
  }

  public async rejectJob(
    jobId: string,
    companyId: number,
    rejectReason?: RejectOrCancelJobReason,
  ): Promise<AxiosResponse<GetJobResponseType>> {
    const response = await this.client.post('/jobs/{jobId}/reject', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
      body: { reason: rejectReason },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getJobResponseSchema, response.data),
    };
  }

  public async completeJob(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse<GetJobResponseType>> {
    const response = await this.client.post('/jobs/{jobId}/complete', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getJobResponseSchema, response.data),
    };
  }

  public async bookJob(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse<GetJobResponseType>> {
    const response = await this.client.post('/jobs/{jobId}/book', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getJobResponseSchema, response.data),
    };
  }

  public async updateJobNote(
    jobId: string,
    note: string,
    companyId: number,
  ): Promise<AxiosResponse<GetJobResponseType>> {
    const response = await this.client.put('/jobs/{jobId}/note', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
      body: { note },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getJobResponseSchema, response.data),
    };
  }

  public async createReviewRequest(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse> {
    return this.client.post('/jobs/{jobId}/review/requests', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });
  }

  public async getReviewRequests(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse<GetReviewRequestsRequest>> {
    const response = await this.client.get('/jobs/{jobId}/review/requests', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getReviewRequestsSchema, response.data),
    };
  }

  public async requestAddress(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse> {
    const response = await this.client.post('/jobs/{jobId}/request-address', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });

    return response;
  }
}

export const jobs = new JobsTradeBffApi();
