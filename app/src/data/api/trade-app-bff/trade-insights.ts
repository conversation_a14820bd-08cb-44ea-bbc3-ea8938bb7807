import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { getAccessToken } from 'src/auth/utils/authSession';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { TradeInsightsResponse } from 'src/data/schemas/api/trade-app-bff/trade-insights/tradeInsightsResponse';

class TradeInsightsTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getTradeInsights(
    companyId: number,
  ): Promise<AxiosResponse<TradeInsightsResponse>> {
    const response = await this.client.get('/trade-insights', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(TradeInsightsResponse, response.data),
    };
  }
}

export const tradeInsights = new TradeInsightsTradeBffApi();
