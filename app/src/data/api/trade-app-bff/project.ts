import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { getAccessToken } from 'src/auth/utils/authSession';
import {
  PostProjectBodyType,
  ProjectCompletedJobsRequestType,
  ProjectCompletedListResponse,
  projectCompletedListResponseSchema,
  projectListResponseSchema,
  projectSchemaResponse,
  ProjectSchemaResponseType,
} from 'src/data/schemas/api/trade-app-bff/project/Project';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { PaginatedResponse } from 'src/data/schemas/common/paginatedResponse';

class FeaturedProjectBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getAll(
    companyId: number,
  ): Promise<AxiosResponse<PaginatedResponse<ProjectSchemaResponseType>>> {
    const response = await this.client.get('/projects', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        projectListResponseSchema,
        response.data,
      ),
    };
  }

  public async get(
    companyId: number,
    projectId: string,
  ): Promise<AxiosResponse<ProjectSchemaResponseType>> {
    const response = await this.client.get('/project/{projectId}', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: {
          projectId,
        },
      },
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(projectSchemaResponse, response.data),
    };
  }

  public async create(
    body: PostProjectBodyType,
    companyId: number,
  ): Promise<AxiosResponse<ProjectSchemaResponseType>> {
    const response = await this.client.post('/project', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
      body,
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(projectSchemaResponse, response.data),
    };
  }

  public async delete(
    projectId: string,
    companyId: number,
  ): Promise<AxiosResponse<void>> {
    return this.client.delete('/project/{projectId}', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: {
          projectId,
        },
      },
    });
  }

  public async update(
    projectId: string,
    body: Omit<PostProjectBodyType, 'companyId'>,
    companyId: number,
  ): Promise<AxiosResponse<ProjectSchemaResponseType>> {
    const response = await this.client.patch('/project/{projectId}', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: {
          projectId,
        },
      },
      body,
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(projectSchemaResponse, response.data),
    };
  }

  public async getCompletedJobs(
    companyId: number,
    { size }: ProjectCompletedJobsRequestType,
  ): Promise<AxiosResponse<ProjectCompletedListResponse>> {
    const response = await this.client.get('/projects/completed-jobs', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        query: {
          size,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        projectCompletedListResponseSchema,
        response.data,
      ),
    };
  }
}

export const project = new FeaturedProjectBffApi();
