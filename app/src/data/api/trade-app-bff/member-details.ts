import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { getAccessToken } from 'src/auth/utils/authSession';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { MemberDetailsResponse } from 'src/data/schemas/api/trade-app-bff/member-details';

class MemberDetailsTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getMemberDetails(
    companyId: number,
  ): Promise<AxiosResponse<MemberDetailsResponse>> {
    const response = await this.client.get('/member-details', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(MemberDetailsResponse, response.data),
    };
  }
}

export const memberDetails = new MemberDetailsTradeBffApi();
