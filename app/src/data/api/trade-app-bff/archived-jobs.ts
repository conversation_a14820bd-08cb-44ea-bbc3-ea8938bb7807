import { AxiosResponse } from 'axios';
import { createApiClient } from 'src/data/apiClient';
import {
  getArchivedJobResponseSchema,
  GetArchivedJobResponseType,
  GetArchivedJobsRequestType,
  getArchivedJobsResponseSchema,
  GetArchivedJobsResponseType,
} from 'src/data/schemas/api/capi/archived-jobs';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { config } from 'src/config';
import { getAccessToken } from 'src/auth/utils/authSession';

class ArchivedJobsTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getArchivedJob(
    jobId: string,
    companyId: number,
  ): Promise<AxiosResponse<GetArchivedJobResponseType>> {
    const response = await this.client.get('/archived-jobs/{jobId}', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { jobId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getArchivedJobResponseSchema,
        response.data,
      ),
    };
  }

  public async getArchivedJobs(
    query: GetArchivedJobsRequestType,
    companyId: number,
  ): Promise<AxiosResponse<GetArchivedJobsResponseType>> {
    const response = await this.client.get('/archived-jobs', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        query,
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getArchivedJobsResponseSchema,
        response.data,
      ),
    };
  }
}

export const archivedJobs = new ArchivedJobsTradeBffApi();
