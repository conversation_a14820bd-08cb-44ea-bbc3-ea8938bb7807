import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { getAccessToken } from 'src/auth/utils/authSession';
import {
  ExternalMetricsResponse,
  ManualReviewRequestListParams,
  ManualReviewRequestListResponse,
  ManualReviewRequestReminderParams,
  ManualReviewRequestResponse,
  ManualReviewRequestSchema,
  type MetricsQueryParams,
  PostManualReviewRequestParams,
  ReviewItemSchema,
  type ReviewReplyRequest,
  ReviewReplyResponse,
  type ReviewReportParams,
  type ReviewsPublicListParams,
  ReviewsResponse,
  type ReviewV2Type,
} from 'src/data/schemas/api/trade-app-bff/reviews';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import {
  JobReviewRequestListParams,
  JobReviewRequestResponseSchema,
  JobReviewRequestsResponse,
} from 'src/data/schemas/api/trade-app-bff/reviews/JobReviewRequests';
import { ReviewResponse } from 'src/data/schemas/api/trade-app-bff/reviews/ReviewResponse';
import {
  GetReviewsSummaryParams,
  ReviewsSummaryResponse,
  ReviewsSummarySchema,
} from 'src/data/schemas/api/trade-app-bff/reviews/ReviewsSummary';

class ReviewsTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getReview(
    companyId: number,
    reviewId: string,
  ): Promise<AxiosResponse<ReviewResponse>> {
    const response = await this.client.get('/reviews/{reviewId}', {
      params: {
        header: { 'x-trade-company-id': companyId },
        path: {
          reviewId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(ReviewResponse, response.data),
    };
  }

  public async getReviews({
    companyId,
    page = 1,
    size = 10,
    orderDesc = 'createdAt',
  }: ReviewsPublicListParams): Promise<AxiosResponse<ReviewsResponse>> {
    const response = await this.client.get('/reviews', {
      params: {
        query: { page, size, orderDesc },
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(ReviewsResponse, response.data),
    };
  }
  public async getReviewMetrics({
    companyId,
  }: MetricsQueryParams): Promise<AxiosResponse<ExternalMetricsResponse>> {
    const response = await this.client.get('/reviews/metrics', {
      params: { header: { 'x-trade-company-id': companyId } },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(ExternalMetricsResponse, response.data),
    };
  }

  public async reviewReport(
    { companyId, reason, description }: ReviewReportParams,
    reviewId: string,
  ): Promise<AxiosResponse<ReviewV2Type>> {
    const response = await this.client.post('/reviews/{reviewId}/report', {
      params: {
        path: { reviewId },
        header: { 'x-trade-company-id': companyId },
      },
      body: { reason, description },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(ReviewItemSchema, response.data),
    };
  }

  public async reviewReply({
    companyId,
    reviewId,
    reply,
  }: ReviewReplyRequest): Promise<AxiosResponse<ReviewReplyResponse>> {
    const response = await this.client.post('/reviews/{reviewId}/reply', {
      params: {
        path: { reviewId },
        header: { 'x-trade-company-id': companyId },
      },
      body: { reply },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(ReviewReplyResponse, response.data),
    };
  }

  public async getManualReviewRequests({
    companyId,
    size = 10,
    lastReviewRequestId,
  }: ManualReviewRequestListParams): Promise<
    AxiosResponse<ManualReviewRequestListResponse>
  > {
    const response = await this.client.get('/manual-review-requests', {
      params: {
        query: { size, lastReviewRequestId },
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        ManualReviewRequestListResponse,
        response.data,
      ),
    };
  }

  public async postManualReviewRequest({
    companyId,
    ...body
  }: PostManualReviewRequestParams): Promise<
    AxiosResponse<ManualReviewRequestResponse>
  > {
    const response = await this.client.post('/manual-review-requests', {
      params: { header: { 'x-trade-company-id': companyId } },
      body: body,
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        ManualReviewRequestSchema,
        response.data,
      ),
    };
  }

  public async postManualReviewRequestReminder({
    companyId,
    id,
  }: ManualReviewRequestReminderParams): Promise<
    AxiosResponse<ManualReviewRequestResponse>
  > {
    const response = await this.client.post(
      '/manual-review-requests/{id}/reminder',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
          path: {
            id,
          },
        },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        ManualReviewRequestSchema,
        response.data,
      ),
    };
  }

  public async getJobReviewRequestsList({
    companyId,
    page = 1,
    size = 10,
  }: JobReviewRequestListParams): Promise<
    AxiosResponse<JobReviewRequestsResponse>
  > {
    const response = await this.client.get('/reviews/job-requests', {
      params: {
        query: { page, size },
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        JobReviewRequestResponseSchema,
        response.data,
      ),
    };
  }

  public async getReviewsSummary({
    companyId,
  }: GetReviewsSummaryParams): Promise<AxiosResponse<ReviewsSummaryResponse>> {
    const response = await this.client.get(
      '/reviews/summary/{companyId}/{summaryType}',
      {
        params: {
          header: {
            'x-trade-company-id': companyId,
          },
          path: {
            companyId,
            summaryType: 'trade',
          },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(ReviewsSummarySchema, response.data),
    };
  }
}

export const reviews = new ReviewsTradeBffApi();
