import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import {
  AppointmentRequestType,
  AppointmentResponseType,
  AppointmentTypesResponseType,
  PatchAppointmentRequestType,
  appointmentResponseSchema,
  appointmentTypesResponseSchema,
} from 'src/data/schemas/api/capi/appointments';
import { getAccessToken } from 'src/auth/utils/authSession';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';

class AppointmentsTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getAppointment(
    appointmentId: string,
    companyId: number,
  ): Promise<AxiosResponse<AppointmentResponseType>> {
    const response = await this.client.get('/appointments/{appointmentId}', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { appointmentId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        appointmentResponseSchema,
        response.data,
      ),
    };
  }

  public async getAppointmentTypes(
    companyId: number,
  ): Promise<AxiosResponse<AppointmentTypesResponseType>> {
    const response = await this.client.get('/appointment/types', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        appointmentTypesResponseSchema,
        response.data,
      ),
    };
  }

  public async createAppointment(
    options: AppointmentRequestType,
    companyId: number,
  ): Promise<AxiosResponse<AppointmentResponseType>> {
    const response = await this.client.post('/appointments', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
      body: options,
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        appointmentResponseSchema,
        response.data,
      ),
    };
  }

  public async updateAppointment(
    appointmentId: string,
    options: PatchAppointmentRequestType,
    companyId: number,
  ): Promise<AxiosResponse<AppointmentResponseType>> {
    const response = await this.client.patch('/appointments/{appointmentId}', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { appointmentId },
      },
      body: options,
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        appointmentResponseSchema,
        response.data,
      ),
    };
  }

  public async rescheduleAppointment(
    appointmentId: string,
    options: AppointmentRequestType,
    companyId: number,
  ): Promise<AxiosResponse<AppointmentResponseType>> {
    const response = await this.client.post(
      '/appointments/{appointmentId}/reschedule',
      {
        params: {
          header: {
            'x-trade-company-id': companyId,
          },
          path: { appointmentId },
        },
        body: options,
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        appointmentResponseSchema,
        response.data,
      ),
    };
  }

  public async cancelAppointment(
    appointmentId: string,
    companyId: number,
  ): Promise<AxiosResponse<AppointmentResponseType>> {
    const response = await this.client.post(
      '/appointments/{appointmentId}/cancel',
      {
        params: {
          header: {
            'x-trade-company-id': companyId,
          },
          path: { appointmentId },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        appointmentResponseSchema,
        response.data,
      ),
    };
  }
}

export const appointments = new AppointmentsTradeBffApi();
