import { AxiosResponse } from 'axios';
import { getAccessToken } from 'src/auth/utils/authSession';
import { config } from 'src/config';
import { paths } from 'src/data/api-specs/trade-app-bff';
import { createApiClient } from 'src/data/apiClient';
import {
  AddTeamPersonRequestType,
  addTeamPersonResponseSchema,
  AddTeamPersonResponseType,
  GetTeamInviteRequestType,
  getTeamInviteResponseSchema,
  GetTeamInviteResponseType,
  getTeamIsFullMemberOrNationalAccountResponseSchema,
  GetTeamIsFullMemberOrNationalAccountResponseType,
  GetTeamRequestType,
  getTeamResponseSchema,
  GetTeamResponseType,
  InviteSubcontractorRequestType,
  inviteSubcontractorResponseSchema,
  InviteSubcontractorResponseType,
  removeSubcontractor<PERSON>esponseSchema,
  RemoveSubcontractorResponseType,
  UpdateTeamInviteRequestType,
  updateTeamInviteResponseSchema,
  UpdateTeamInviteResponseType,
  UpdateTeamPersonRequestType,
  updateTeamPersonResponseSchema,
  UpdateTeamPersonResponseType,
} from 'src/data/schemas/api/trade-app-bff/team';
import { GetTeamCountersRequestType } from 'src/data/schemas/api/trade-app-bff/team/TeamCountersRequest';
import {
  getTeamCountersResponseSchema,
  GetTeamCountersResponseType,
} from 'src/data/schemas/api/trade-app-bff/team/TeamCountersResponse';
import {
  EmployeeConsentEmailRequestType,
  employeeConsentEmailResponseSchema,
  EmployeeConsentEmailResponseType,
} from 'src/data/schemas/api/trade-app-bff/team/TeamEmployeeConsentEmail';
import { GetTeamInvitesRequestType } from 'src/data/schemas/api/trade-app-bff/team/TeamInvitesRequest';
import {
  getSubcontractorTeamInvitesResponseSchema,
  GetSubcontractorTeamInvitesResponseType,
  getTeamInvitesResponseSchema,
  GetTeamInvitesResponseType,
} from 'src/data/schemas/api/trade-app-bff/team/TeamInvitesResponse';
import { GetTeamPersonRequestType } from 'src/data/schemas/api/trade-app-bff/team/TeamPersonRequest';
import {
  getTeamPersonResponseSchema,
  GetTeamPersonResponseType,
} from 'src/data/schemas/api/trade-app-bff/team/TeamPersonResponse';
import {
  AcceptVettingConsentRequestType,
  acceptVettingConsentResponseSchema,
  AcceptVettingConsentResponseType,
  GetTeamPersonsVettingDetailsRequestType,
  getTeamPersonsVettingDetailsResponseSchema,
  GetTeamPersonsVettingDetailsResponseType,
} from 'src/data/schemas/api/trade-app-bff/team/TeamPersonsVettingDetails';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { LIST_PER_PAGE_SIZE } from 'src/screens/MyTeam/constants';

class TeamTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getTeam({
    contractingType,
    searchTerm,
    page = 1,
    pageSize = 10,
    companyId,
  }: GetTeamRequestType): Promise<AxiosResponse<GetTeamResponseType>> {
    const searchTermEncoded = searchTerm
      ? encodeURIComponent(searchTerm.replaceAll(' ', '&'))
      : undefined;

    const response = await this.client.get('/team/persons', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        query: {
          contractingType,
          searchTerm: searchTermEncoded,
          page,
          pageSize,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(getTeamResponseSchema, response.data),
    };
  }

  public async getTeamPerson({
    personId,
    companyId,
  }: GetTeamPersonRequestType): Promise<
    AxiosResponse<GetTeamPersonResponseType>
  > {
    const response = await this.client.get('/team/{personId}', {
      params: {
        path: { personId },
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getTeamPersonResponseSchema,
        response.data,
      ),
    };
  }

  public async addTeamPerson(
    request: AddTeamPersonRequestType,
  ): Promise<AxiosResponse<AddTeamPersonResponseType>> {
    const response = await this.client.post('/team', {
      params: {
        header: {
          'x-trade-company-id': request.companyId,
        },
      },
      body: {
        ...request,
        dateOfBirth: request.dateOfBirth.toISOString(),
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        addTeamPersonResponseSchema,
        response.data,
      ),
    };
  }

  public async updateTeamPerson(
    request: UpdateTeamPersonRequestType,
    personId: string,
  ): Promise<AxiosResponse<UpdateTeamPersonResponseType>> {
    const response = await this.client.put('/team/{personId}/persons', {
      params: {
        header: {
          'x-trade-company-id': request.companyId,
        },
        path: { personId },
      },
      body: {
        ...request,
        dateOfBirth: request.dateOfBirth.toISOString(),
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        updateTeamPersonResponseSchema,
        response.data,
      ),
    };
  }

  public async getInvites({
    page = 1,
    pageSize = LIST_PER_PAGE_SIZE,
    companyId,
  }: GetTeamInvitesRequestType): Promise<
    AxiosResponse<GetSubcontractorTeamInvitesResponseType>
  > {
    const response = await this.client.get('/team/invites', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        query: {
          page,
          pageSize,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getSubcontractorTeamInvitesResponseSchema,
        response.data,
      ),
    };
  }

  public async getInvite({
    inviteId,
    companyId,
  }: GetTeamInviteRequestType): Promise<
    AxiosResponse<GetTeamInviteResponseType>
  > {
    const response = await this.client.get('/team/invite/{id}', {
      params: {
        path: {
          id: inviteId,
        },
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getTeamInviteResponseSchema,
        response.data,
      ),
    };
  }

  public async getTeamCounters({
    companyId,
  }: GetTeamCountersRequestType): Promise<
    AxiosResponse<GetTeamCountersResponseType>
  > {
    const response = await this.client.get('/team/counters', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getTeamCountersResponseSchema,
        response.data,
      ),
    };
  }

  public async inviteSubcontractor(
    request: InviteSubcontractorRequestType,
    companyId: number,
  ): Promise<AxiosResponse<InviteSubcontractorResponseType>> {
    const response = await this.client.post('/team/invites', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
      body: request,
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        inviteSubcontractorResponseSchema,
        response.data,
      ),
    };
  }

  public async getPendingInvites({
    page = 1,
    pageSize = LIST_PER_PAGE_SIZE,
    companyId,
    relationshipStatus,
  }: GetTeamInvitesRequestType): Promise<
    AxiosResponse<GetTeamInvitesResponseType>
  > {
    const response = await this.client.get('/team/pending-invites', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        query: {
          page,
          pageSize,
          relationshipStatus,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getTeamInvitesResponseSchema,
        response.data,
      ),
    };
  }

  public async updateTeamInvite(
    inviteId: string,
    companyId: number,
    request: UpdateTeamInviteRequestType,
  ): Promise<AxiosResponse<UpdateTeamInviteResponseType>> {
    const response = await this.client.put('/team/invite/{id}', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { id: inviteId },
      },
      body: request,
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        updateTeamInviteResponseSchema,
        response.data,
      ),
    };
  }

  public async getTeamPersonsVettingDetails({
    email,
    companyId,
  }: GetTeamPersonsVettingDetailsRequestType): Promise<
    AxiosResponse<GetTeamPersonsVettingDetailsResponseType>
  > {
    const response = await this.client.get('/team/persons/vetting-details', {
      params: {
        query: {
          email,
        },
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getTeamPersonsVettingDetailsResponseSchema,
        response.data,
      ),
    };
  }

  public async acceptVettingConsent(
    request: AcceptVettingConsentRequestType,
    companyId: number,
    vettingId: string,
  ): Promise<AxiosResponse<AcceptVettingConsentResponseType>> {
    const response = await this.client.put('/team/{personId}/vetting', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { personId: vettingId },
      },
      body: request,
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        acceptVettingConsentResponseSchema,
        response.data,
      ),
    };
  }

  public async sendEmployeeConsentEmail(
    request: EmployeeConsentEmailRequestType,
    companyId: number,
    personId: string,
  ): Promise<AxiosResponse<EmployeeConsentEmailResponseType>> {
    const response = await this.client.post('/team/{personId}/consent-email', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
        path: { personId },
      },
      body: request,
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        employeeConsentEmailResponseSchema,
        response.data,
      ),
    };
  }

  public async removeSubcontractor(
    subcontractorId: string,
    companyId: number,
    inviteId: string,
  ): Promise<AxiosResponse<RemoveSubcontractorResponseType>> {
    const response = await this.client.delete(
      '/team/subcontractor/{subcontractorId}',
      {
        params: {
          header: {
            'x-trade-company-id': companyId,
          },
          path: { subcontractorId },
          query: { inviteId },
        },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        removeSubcontractorResponseSchema,
        response.data,
      ),
    };
  }

  public async getIsFullMemberOrNationalAccount(
    companyId: number,
  ): Promise<AxiosResponse<GetTeamIsFullMemberOrNationalAccountResponseType>> {
    const response = await this.client.get(
      '/team/is-full-member-or-national-account',
      {
        params: {
          header: {
            'x-trade-company-id': companyId,
          },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        getTeamIsFullMemberOrNationalAccountResponseSchema,
        response.data,
      ),
    };
  }
}

export const team = new TeamTradeBffApi();
