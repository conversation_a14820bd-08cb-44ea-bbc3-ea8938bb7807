import { getAccessToken } from 'src/auth/utils/authSession';
import { config } from 'src/config';
import { paths } from 'src/data/api-specs/trade-app-bff';
import { createApiClient } from 'src/data/apiClient';
import { v7 as UUIDv7 } from 'uuid';

import { PostCompanyNameBodyType } from 'src/data/schemas/api/trade-app-bff/self-service/CompanyName';
import { PostMemberEmailBodyType } from 'src/data/schemas/api/trade-app-bff/self-service/MemberEmail';
import { SelfServiceParamsType } from 'src/data/schemas/api/trade-app-bff/self-service/Validation';
import { PostMemberPhoneBodyType } from 'src/data/schemas/api/trade-app-bff/self-service/MemberPhone';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { PostMemberAddressBodyType } from 'src/data/schemas/api/trade-app-bff/self-service/MemberAddress';
import {
  DeletePersonBodyType,
  UpdatePersonBodyType,
} from 'src/data/schemas/api/trade-app-bff/self-service/Person';

class SelfServiceTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public updateCompanyName = async (
    request: PostCompanyNameBodyType,
    params: SelfServiceParamsType,
  ): Promise<void> => {
    const companyId = params.companyId;
    const correlationId = UUIDv7();

    logEvent(EVENT_TYPE.MY_DETAILS_COMPANY_NAME_SUBMIT_REQUEST, {
      correlationId,
      companyId,
    });

    await this.client.post('/self-service/company-name', {
      params: {
        header: {
          'x-trade-company-id': companyId,
          'x-correlation-id': correlationId,
        },
      },
      body: {
        ...request,
      },
    });
  };

  public updateMemberEmail = async (
    request: PostMemberEmailBodyType,
    params: SelfServiceParamsType,
  ): Promise<void> => {
    const companyId = params.companyId;
    const correlationId = UUIDv7();

    logEvent(EVENT_TYPE.MY_DETAILS_ACCOUNT_EMAIL_SUBMIT_REQUEST, {
      correlationId,
      companyId,
    });

    await this.client.post('/self-service/member-email', {
      params: {
        header: {
          'x-trade-company-id': companyId,
          'x-correlation-id': correlationId,
        },
      },
      body: {
        ...request,
      },
    });
  };

  public updateMemberPhone = async (
    request: PostMemberPhoneBodyType,
    params: SelfServiceParamsType,
  ): Promise<void> => {
    const companyId = params.companyId;
    const correlationId = UUIDv7();

    logEvent(EVENT_TYPE.MY_DETAILS_ACCOUNT_PHONE_SUBMIT_REQUEST, {
      correlationId,
      companyId,
    });

    await this.client.post('/self-service/member-phone', {
      params: {
        header: {
          'x-trade-company-id': companyId,
          'x-correlation-id': correlationId,
        },
      },
      body: {
        ...request,
      },
    });
  };

  public updateMemberTradingAddress = async (
    request: PostMemberAddressBodyType,
    params: SelfServiceParamsType,
  ): Promise<void> => {
    const companyId = params.companyId;
    const correlationId = UUIDv7();

    logEvent(EVENT_TYPE.MY_DETAILS_ACCOUNT_TRADING_ADDRESS_SUBMIT_REQUEST, {
      correlationId,
      companyId,
    });

    await this.client.post('/self-service/member-trading-address', {
      params: {
        header: {
          'x-trade-company-id': companyId,
          'x-correlation-id': correlationId,
        },
      },
      body: {
        ...request,
      },
    });
  };

  public updateMemberAdminAddress = async (
    request: PostMemberAddressBodyType,
    params: SelfServiceParamsType,
  ): Promise<void> => {
    const companyId = params.companyId;
    const correlationId = UUIDv7();

    logEvent(EVENT_TYPE.MY_DETAILS_ACCOUNT_ADMIN_ADDRESS_SUBMIT_REQUEST, {
      correlationId,
      companyId,
    });

    await this.client.post('/self-service/member-admin-address', {
      params: {
        header: {
          'x-trade-company-id': companyId,
          'x-correlation-id': correlationId,
        },
      },
      body: {
        ...request,
      },
    });
  };

  public updatePerson = async (
    request: UpdatePersonBodyType,
    params: SelfServiceParamsType,
  ): Promise<void> => {
    const companyId = params.companyId;
    const correlationId = UUIDv7();

    logEvent(EVENT_TYPE.MY_DETAILS_UPDATE_CONTACT_SUBMIT_REQUEST, {
      correlationId,
      companyId,
    });

    await this.client.post('/self-service/update-person', {
      params: {
        header: {
          'x-trade-company-id': companyId,
          'x-correlation-id': correlationId,
        },
      },
      body: {
        ...request,
      },
    });
  };

  public deletePerson = async (
    request: DeletePersonBodyType,
    params: SelfServiceParamsType,
  ): Promise<void> => {
    const companyId = params.companyId;
    const correlationId = UUIDv7();

    logEvent(EVENT_TYPE.MY_DETAILS_DELETE_CONTACT_SUBMIT_REQUEST, {
      correlationId,
      companyId,
    });

    await this.client.post('/self-service/delete-person', {
      params: {
        header: {
          'x-trade-company-id': companyId,
          'x-correlation-id': correlationId,
        },
      },
      body: {
        ...request,
      },
    });
  };
}

export const selfServiceApi = new SelfServiceTradeBffApi();
