import { AxiosResponse } from 'axios';
import { getAccessToken } from 'src/auth/utils/authSession';
import { config } from 'src/config';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { createApiClient } from 'src/data/apiClient';
import {
  revettingStatusSchema,
  RevettingStatusSchema,
} from 'src/data/schemas/api/trade-app-bff/revetting';
import {
  revettingUrlSchema,
  RevettingUrlSchema,
} from 'src/data/schemas/api/trade-app-bff/revetting/RevettingUrl';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';

class RevettingTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getRevettingStatus({
    companyId,
  }: {
    companyId: number;
  }): Promise<AxiosResponse<RevettingStatusSchema>> {
    const response = await this.client.get('/revetting/status', {
      params: {
        header: { 'x-trade-company-id': companyId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(revettingStatusSchema, response.data),
    };
  }

  public async getRevettingUrl({
    companyId,
  }: {
    companyId: number;
  }): Promise<AxiosResponse<RevettingUrlSchema>> {
    const response = await this.client.get('/revetting/url', {
      params: {
        header: { 'x-trade-company-id': companyId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(revettingUrlSchema, response.data),
    };
  }
}

export const revetting = new RevettingTradeBffApi();
