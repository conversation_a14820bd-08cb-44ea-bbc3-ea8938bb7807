import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { getAccessToken } from 'src/auth/utils/authSession';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { MemberInfoResponse } from 'src/data/schemas/api/trade-app-bff/member-info';

class MemberInfoTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getMemberInfo(
    companyId: number,
  ): Promise<AxiosResponse<MemberInfoResponse>> {
    const response = await this.client.get('/member-info', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(MemberInfoResponse, response.data),
    };
  }
}

export const memberInfo = new MemberInfoTradeBffApi();
