import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { getAccessToken } from 'src/auth/utils/authSession';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import { ReferralCampaign } from 'src/data/schemas/api/trade-app-bff/referral/ReferralCampaign';

class ReferralTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getReferralCampaign({
    companyId,
    campaignId,
  }: {
    companyId: number;
    campaignId: number;
  }): Promise<AxiosResponse<ReferralCampaign>> {
    const response = await this.client.get('/referral', {
      params: {
        header: { 'x-trade-company-id': companyId },
        query: { campaignId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(ReferralCampaign, response.data),
    };
  }
}

export const referral = new ReferralTradeBffApi();
