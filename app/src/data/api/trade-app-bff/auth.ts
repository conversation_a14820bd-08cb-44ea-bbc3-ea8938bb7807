import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { getAccessToken } from 'src/auth/utils/authSession';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import {
  ChatTokenResponse,
  ChatTokenResponseType,
} from 'src/data/schemas/api/capi/chat/ChatTokenResponse';
import {
  CustomTokenResponseSchema,
  CustomTokenResponseType,
} from 'src/data/schemas/api/trade-app-bff/auth/CustomTokenResponse';

class AuthTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async getChatToken(
    companyId: number,
  ): Promise<AxiosResponse<ChatTokenResponseType>> {
    const response = await this.client.post('/auth/chat', {
      params: {
        header: {
          'x-trade-company-id': companyId,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(ChatTokenResponse, response.data),
    };
  }

  public async getCustomFirebaseToken(
    companyId: number,
    accessToken: string,
  ): Promise<AxiosResponse<CustomTokenResponseType>> {
    const response = await this.client.get('/auth/createCustomFirebaseToken', {
      params: {
        header: {
          'x-trade-company-id': companyId,
          Authorization: `Bearer ${accessToken}`,
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        CustomTokenResponseSchema,
        response.data,
      ),
    };
  }
}

export const auth = new AuthTradeBffApi();
