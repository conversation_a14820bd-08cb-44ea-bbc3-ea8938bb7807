import { AxiosResponse } from 'axios';
import { getAccessToken } from 'src/auth/utils/authSession';
import { config } from 'src/config';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { createApiClient } from 'src/data/apiClient';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import {
  BookableServiceCreateResponse,
  bookableServiceCreateResponseSchema,
  bookableServiceDetailSchema,
  BookableServiceDetailType,
  BookableServiceSchemaResponse,
  bookableServicesResponseSchema,
  BookingServiceStatusType,
  CreateBookableServiceSchema,
} from 'src/data/schemas/api/capi/service-catalog/service';

interface GetServicesConfig {
  pageNumber: number;
  pageSize: number;
  companyId: number;
}

class ServiceCatalogTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  /**
   * Gets all services for a trade
   *
   * @param pageNumber
   * @param pageSize
   * @param filter
   */
  public getServices = async ({
    pageNumber,
    pageSize,
    companyId,
  }: GetServicesConfig): Promise<
    AxiosResponse<BookableServiceSchemaResponse>
  > => {
    const response = await this.client.get('/service-catalog/services', {
      params: {
        header: { 'x-trade-company-id': companyId },
        query: { pageSize, pageNumber },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        bookableServicesResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Retrieves a trade service and associated detail
   *
   * @param companyId
   * @param serviceId
   * @param versionId
   */
  public getServiceVersionDetail = async (
    companyId: number,
    serviceId: string,
    versionId: string,
  ): Promise<AxiosResponse<BookableServiceDetailType>> => {
    const response = await this.client.get(
      '/service-catalog/service/{serviceId}/version/{versionId}',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
          path: {
            serviceId,
            versionId,
          },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        bookableServiceDetailSchema,
        response.data,
      ),
    };
  };

  /**
   * Creates a service
   */
  public postService = async (
    companyId: number,
    service: CreateBookableServiceSchema,
  ): Promise<AxiosResponse<BookableServiceCreateResponse>> => {
    const response = await this.client.post('/service-catalog/service', {
      body: service,
      params: {
        header: { 'x-trade-company-id': companyId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        bookableServiceCreateResponseSchema,
        response.data,
      ),
    };
  };

  public patchServiceVersionStatus = async (
    companyId: number,
    serviceId: string,
    versionId: string,
    status: BookingServiceStatusType,
  ): Promise<AxiosResponse<Record<string, never>>> => {
    return this.client.patch(
      '/service-catalog/service/{serviceId}/version/{versionId}/status',
      {
        body: { status },
        params: {
          header: { 'x-trade-company-id': companyId },
          path: { serviceId, versionId },
        },
      },
    );
  };

  public postServiceVersion = async (
    companyId: number,
    serviceId: string,
    version: CreateBookableServiceSchema,
  ): Promise<AxiosResponse<BookableServiceCreateResponse>> => {
    const response = await this.client.post(
      '/service-catalog/service/{serviceId}/version',
      {
        body: version,
        params: {
          header: { 'x-trade-company-id': companyId },
          path: { serviceId },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        bookableServiceCreateResponseSchema,
        response.data,
      ),
    };
  };
}

export const serviceCatalog = new ServiceCatalogTradeBffApi();
