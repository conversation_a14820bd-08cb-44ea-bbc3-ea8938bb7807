import { AxiosResponse } from 'axios';
import { getAccessToken } from 'src/auth/utils/authSession';
import { config } from 'src/config';
import { paths } from 'src/data/api-specs/trade-app-bff';
import { createApiClient } from 'src/data/apiClient';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import {
  PersonAccreditationPathType,
  PersonAccreditationsParamsType,
  PersonAccreditationsPathType,
  PostPersonAccreditationBodyType,
} from 'src/data/schemas/api/trade-app-bff/person/PersonAccreditationRequest';
import {
  AccreditationResponseType,
  accreditationResponseSchema,
  accreditationsResponseSchema,
} from 'src/data/schemas/api/trade-app-bff/person/PersonAccreditationResponse';

class PersonTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  public async addPersonAccreditation(
    request: PostPersonAccreditationBodyType,
    params: PersonAccreditationsParamsType,
    path: PersonAccreditationsPathType,
  ): Promise<AxiosResponse<AccreditationResponseType>> {
    const response = await this.client.post(
      '/person/{personId}/accreditations',
      {
        params: {
          header: {
            'x-trade-company-id': params.companyId,
          },
          path: { personId: path.personId },
        },
        body: {
          ...request,
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        accreditationResponseSchema,
        response.data,
      ),
    };
  }

  public async getPersonAccreditations(
    params: PersonAccreditationsParamsType,
    path: PersonAccreditationsPathType,
  ): Promise<AxiosResponse<AccreditationResponseType[]>> {
    const response = await this.client.get(
      '/person/{personId}/accreditations',
      {
        params: {
          header: {
            'x-trade-company-id': params.companyId,
          },
          path: { personId: path.personId },
        },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        accreditationsResponseSchema,
        response.data,
      ),
    };
  }

  public async getPersonAccreditation(
    params: PersonAccreditationsParamsType,
    path: PersonAccreditationPathType,
  ): Promise<AxiosResponse<AccreditationResponseType>> {
    const response = await this.client.get(
      '/person/{personId}/accreditations/{accreditationId}',
      {
        params: {
          header: {
            'x-trade-company-id': params.companyId,
          },
          path: {
            personId: path.personId,
            accreditationId: path.accreditationId,
          },
        },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        accreditationResponseSchema,
        response.data,
      ),
    };
  }

  public async deletePersonAccreditation(
    params: PersonAccreditationsParamsType,
    path: PersonAccreditationPathType,
  ): Promise<AxiosResponse<void>> {
    return this.client.delete('/person/{personId}/accreditations/{id}', {
      params: {
        header: {
          'x-trade-company-id': params.companyId,
        },
        path: {
          personId: path.personId,
          id: path.accreditationId,
        },
      },
    });
  }
}

export const personApi = new PersonTradeBffApi();
