import { AxiosHeaders, AxiosResponse } from 'axios';
import { getAccessToken } from 'src/auth/utils/authSession';
import { config } from 'src/config';
import { IS_WEB } from 'src/constants';
import type { paths } from 'src/data/api-specs/trade-app-bff';
import { createApiClient } from 'src/data/apiClient';
import {
  ActivityType,
  GetActivitiesResponse,
  GetActivitiesResponseSchema,
} from 'src/data/schemas/api/capi/payments/activities';
import {
  BalanceResponseSchema,
  GetBalanceResponse,
} from 'src/data/schemas/api/capi/payments/balance';
import {
  CreateOnboardingResponse,
  CreateOnboardingResponseSchema,
  GetBalanceAccountIdsResponse,
  GetBalanceAccountIdsResponseSchema,
  GetExternalTaxInformationResponse,
  GetExternalTaxInformationResponseSchema,
  GetOnboardingResponseSchema,
  GetOnboardingStatusResponse,
  GetTaxInformationResponse,
  GetTaxInformationResponseSchema,
  TaxInformation,
} from 'src/data/schemas/api/capi/payments/onboarding';
import {
  CancelPaymentRequest,
  PaymentPayByPhonePaymentMethodsResponse,
  PaymentPayByPhonePaymentMethodsResponseSchema,
  PaymentPayByPhonePaymentRequest,
  PaymentPayByPhonePaymentRequestResponse,
  PaymentPayByPhonePaymentRequestResponseSchema,
  PaymentRequest,
  PaymentRequestDetailResponse,
  PaymentRequestDetailResponseSchema,
  PaymentRequestNoJobResponseSchema,
  PaymentRequestOffPlatformJobRequest,
  PaymentRequestOffPlatformJobResponse,
  PaymentRequestResponse,
  PaymentRequestResponseSchema,
  PaymentRequestsQuoteMetricsResponse,
  PaymentRequestsQuoteMetricsResponseSchema,
  PaymentRequestsResponseSchema,
  PaymentRequestsSchemaResponse,
} from 'src/data/schemas/api/capi/payments/payment-request';
import {
  GetSplitPaymentResponse,
  SplitPaymentResponseSchema,
} from 'src/data/schemas/api/capi/payments/split-payments';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';

interface GetActivitiesConfig {
  pageNumber: number;
  pageSize: number;
  filter: ActivityType | null;
  companyId: number;
}

class PaymentsTradeBffApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.capiUrl}/v2/trade-app`,
      getBearerToken: getAccessToken,
    });

  /**
   * Start onboarding process, which fetches data from Salesforce, creates
   * all necessary entities in Adyen, and returns a hosted onboarding URL
   * to redirect to in the app.
   *
   * @param companyId
   * @param primaryCategoryId
   */
  public onboard = async (
    companyId: number,
    primaryCategoryId: number,
  ): Promise<AxiosResponse<CreateOnboardingResponse>> => {
    const response = await this.client.post('/payments/onboard', {
      // @ts-expect-error - type needs updating - companyId not present in body
      body: { companyId, primaryCategoryId },
      params: {
        header: { 'x-trade-company-id': companyId },
        query: { clientType: IS_WEB ? 'WEB' : 'MOBILE' },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        CreateOnboardingResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Gets the authenticated users onboarding status
   */
  public getOnboardingStatus = async (
    companyId: number,
  ): Promise<AxiosResponse<GetOnboardingStatusResponse>> => {
    const response = await this.client.get('/payments/onboarding-information', {
      params: {
        header: { 'x-trade-company-id': companyId },
      },
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        GetOnboardingResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Gets external tax information from payments
   */
  public getExternalTaxInformation = async (
    companyId: number,
  ): Promise<AxiosResponse<GetExternalTaxInformationResponse>> => {
    const response = await this.client.get(
      '/payments/external-tax-information',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
        },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        GetExternalTaxInformationResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Record tax information for HMRC reporting purposes
   *
   * @param body
   */
  public taxInformation = async (
    body: TaxInformation,
    companyId: number,
  ): Promise<AxiosResponse> => {
    return this.client.post('/payments/tax-information', {
      params: {
        header: { 'x-trade-company-id': companyId },
      },
      body,
    });
  };

  /**
   * Get CSV file of activities for a company
   */
  public getActivitiesStatementCsvReport = async (
    companyId: string,
    fromDate: Date,
    toDate: Date,
  ): Promise<AxiosResponse> => {
    return this.client.get('/payments/report/activities-statement', {
      params: {
        header: {
          'x-trade-company-id': companyId,
          accept: 'text/csv',
        },
        query: {
          fromDate: fromDate.toISOString(),
          toDate: toDate.toISOString(),
        },
      },
    });
  };

  /**
   * Get Account balances for Company
   */
  public getAccountBalance = async (
    companyId: number,
  ): Promise<AxiosResponse<GetBalanceResponse>> => {
    const response = await this.client.get('/payments/balance', {
      params: {
        header: { 'x-trade-company-id': companyId },
      },
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(BalanceResponseSchema, response.data),
    };
  };

  /**
   * Gets the payment service charge and total for the provided amount in pennies
   *
   * @param penceAmount
   */
  public getSplitPayment = async (
    penceAmount: number,
    companyId: number,
  ): Promise<AxiosResponse<GetSplitPaymentResponse>> => {
    const response = await this.client.get(
      '/payments/split-payment/{penceAmount}',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
          path: {
            penceAmount,
          },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        SplitPaymentResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Gets a log of payment related activity
   *
   * @param pageNumber
   * @param pageSize
   * @param filter
   */
  public getActivities = async ({
    pageNumber,
    pageSize,
    filter,
    companyId,
  }: GetActivitiesConfig): Promise<AxiosResponse<GetActivitiesResponse>> => {
    const response = await this.client.get('/payments/activities', {
      params: {
        header: { 'x-trade-company-id': companyId },
        query: { pageSize, pageNumber, status: filter ?? undefined },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        GetActivitiesResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Gets the authenticated company's tax information from Content-API
   *
   * @param pageNumber
   */
  public getTaxInformation = async (
    companyId: number,
  ): Promise<AxiosResponse<GetTaxInformationResponse>> => {
    const response = await this.client.get('/payments/tax-information', {
      params: {
        header: { 'x-trade-company-id': companyId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        GetTaxInformationResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Gets the authenticated company's balance account ID along with the merchant balance ID
   *
   */
  public getBalanceAccountIds = async (
    companyId: number,
  ): Promise<AxiosResponse<GetBalanceAccountIdsResponse>> => {
    const response = await this.client.get('/payments/balance-accounts', {
      params: {
        header: { 'x-trade-company-id': companyId },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        GetBalanceAccountIdsResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Retrieves a payment request and associated payment detail
   *
   * @param paymentLinkId
   */
  public getPaymentRequestDetail = async (
    paymentLinkId: string,
    companyId: number,
  ): Promise<AxiosResponse<PaymentRequestDetailResponse>> => {
    const response = await this.client.get(
      '/payments/payment-request/{paymentLinkId}',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
          path: {
            paymentLinkId,
          },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        PaymentRequestDetailResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Gets payment requests by opportunityID
   *
   * @param opportunityId
   * @param pageNumber
   * @param pageSize
   * @param orderBy
   * @param orderDirection
   */
  public getPaymentRequestsByOpportunityId = async ({
    opportunityId,
    pageNumber,
    companyId,
    pageSize,
    orderBy,
    orderDirection,
  }: {
    opportunityId: string;
    pageNumber: number;
    companyId: number;
    pageSize: number;
    orderBy?: string | undefined;
    orderDirection?: 'ASC' | 'DESC' | undefined;
  }): Promise<AxiosResponse<PaymentRequestsSchemaResponse>> => {
    const response = await this.client.get(
      '/payments/payment-requests/opportunity/{opportunityId}',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
          path: {
            opportunityId,
          },
          query: { pageNumber, pageSize, orderBy, orderDirection },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        PaymentRequestsResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Gets payment requests by quoteId
   *
   * @param quoteId
   * @param pageNumber
   * @param pageSize
   * @param orderBy
   * @param orderDirection
   */
  public getPaymentRequestsByQuoteId = async ({
    quoteId,
    pageNumber,
    companyId,
    pageSize,
    orderBy,
    orderDirection,
  }: {
    quoteId: string;
    pageNumber: number;
    companyId: number;
    pageSize: number;
    orderBy?: string | undefined;
    orderDirection?: 'ASC' | 'DESC' | undefined;
  }): Promise<AxiosResponse<PaymentRequestsSchemaResponse>> => {
    const response = await this.client.get(
      '/payments/payment-requests/quote/{quoteId}',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
          path: {
            quoteId,
          },
          query: { pageNumber, pageSize, orderBy, orderDirection },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        PaymentRequestsResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Creates a new payment request and sends a stream message with the payment link once successfully created
   *
   * @param body
   */
  public paymentRequest = async ({
    channelId,
    paymentRequest,
    companyId,
  }: PaymentRequest): Promise<AxiosResponse<PaymentRequestResponse>> => {
    const response = await this.client.post('/payments/payment-request', {
      params: {
        header: { 'x-trade-company-id': companyId },
      },
      body: {
        paymentRequest: {
          ...paymentRequest,
          opportunityId: channelId,
          dueDate: paymentRequest.dueDate.toISOString(),
        },
      },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        PaymentRequestResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Cancels a payment request
   *
   * @param paymentLinkId
   * @param reason
   */
  public cancelPaymentRequest = async ({
    reason,
    paymentLinkId,
    companyId,
  }: CancelPaymentRequest): Promise<AxiosResponse> => {
    const response = await this.client.patch(
      '/payments/payment-request/{paymentLinkId}/cancel',
      {
        body: { reason },
        params: {
          header: { 'x-trade-company-id': companyId },
          path: {
            paymentLinkId,
          },
        },
      },
    );

    return response;
  };

  /**
   * Creates a new payment request for an off platform job
   * @param request
   */
  public paymentRequestOffPlatformJob = async (
    request: PaymentRequestOffPlatformJobRequest,
  ): Promise<AxiosResponse<PaymentRequestOffPlatformJobResponse>> => {
    const header = new AxiosHeaders();
    header.set('x-trade-company-id', request.paymentRequest.companyId);

    const response = await this.client.post(
      '/payments/payment-request/off-platform-job',
      {
        params: { header },
        body: {
          ...request,
          paymentRequest: {
            ...request.paymentRequest,
            dueDate: request.paymentRequest.dueDate.toISOString(),
          },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        PaymentRequestNoJobResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Retrieves the metrics for payment requests on a quote
   *
   * @param quoteId
   */
  public getPaymentRequestsQuoteMetrics = async (
    quoteId: string,
    companyId: number,
  ): Promise<AxiosResponse<PaymentRequestsQuoteMetricsResponse>> => {
    const response = await this.client.get(
      '/payments/payment-requests/quote/{quoteId}/metrics',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
          path: {
            quoteId,
          },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        PaymentRequestsQuoteMetricsResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Gets available payment methods for the Pay by Phone payment method
   *
   * @param companyId
   */
  public getPayByPhonePaymentMethods = async (
    companyId: number,
  ): Promise<AxiosResponse<PaymentPayByPhonePaymentMethodsResponse>> => {
    const response = await this.client.get(
      '/payments/pay-by-phone/payment-methods',
      {
        params: {
          header: { 'x-trade-company-id': companyId },
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        PaymentPayByPhonePaymentMethodsResponseSchema,
        response.data,
      ),
    };
  };

  /**
   * Creates a new pay by phone payment request
   * @param request
   */
  public payByPhonePaymentRequest = async (
    request: PaymentPayByPhonePaymentRequest,
  ): Promise<AxiosResponse<PaymentPayByPhonePaymentRequestResponse>> => {
    const header = new AxiosHeaders();
    header.set('x-trade-company-id', request.paymentRequest.companyId);

    const response = await this.client.post(
      '/payments/pay-by-phone/payment-request',
      {
        params: { header },
        body: {
          vendor: 'adyen',
          vendorData: request.vendorData,
          paymentRequest: {
            ...request.paymentRequest,
            dueDate: request.paymentRequest.dueDate.toISOString(),
          },
          consumer: request.consumer,
          job: request.job,
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        PaymentPayByPhonePaymentRequestResponseSchema,
        response.data,
      ),
    };
  };
}

export const payments = new PaymentsTradeBffApi();
