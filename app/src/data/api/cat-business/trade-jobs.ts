import { AxiosResponse } from 'axios';
import { Platform } from 'react-native';
import { getStoredIdToken } from 'src/auth/utils/authStorage';
import { config } from 'src/config';
import { paths } from 'src/data/api-specs/cat-business-api';
import { createApiClient } from 'src/data/apiClient';
import {
  AcceptJobRequest,
  AcceptJobResponse,
  AcceptJobResponseSchema,
  CancelJobRequest,
  CancelJobResponse,
  CancelJobResponseSchema,
  FileWithMetadata,
  JobDetailsResponse,
  JobDetailsResponseSchema,
  MarkJobCompleteResponse,
  MarkJobCompleteResponseSchema,
  MatchedJobsRequest,
  MatchedJobsResponse,
  MatchedJobsResponseSchema,
  RejectJobRequest,
  RejectJobResponse,
  RejectJobResponseSchema,
} from 'src/data/schemas/api/cat-business';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';

class TradeJobsApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: `${config.catForBusinessApiBaseUrl}/api`,
      getBearerToken: getStoredIdToken,
    });

  public async getMatchedJobs(
    companyId: number,
    params?: MatchedJobsRequest,
  ): Promise<AxiosResponse<MatchedJobsResponse>> {
    if (!companyId) {
      throw new Error('No company ID provided');
    }

    const response = await this.client.get(
      '/trade-jobs/matches',
      {
        params: {
          query: params,
        },
      },
      {
        headers: {
          'x-trade-company-id': companyId,
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        MatchedJobsResponseSchema,
        response.data,
      ),
    };
  }

  public async acceptJob(
    companyId: number,
    jobId: string,
    data: AcceptJobRequest,
  ): Promise<AxiosResponse<AcceptJobResponse>> {
    if (!companyId) {
      throw new Error('No company ID provided');
    }

    const response = await this.client.post(
      '/trade-jobs/matches/{jobId}/accept',
      {
        params: {
          path: { jobId },
        },
        body: data,
      },
      {
        headers: {
          'x-trade-company-id': companyId,
        },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(AcceptJobResponseSchema, response.data),
    };
  }

  public async rejectJob(
    companyId: number,
    jobId: string,
    data: RejectJobRequest,
  ): Promise<AxiosResponse<RejectJobResponse>> {
    if (!companyId) {
      throw new Error('No company ID provided');
    }

    const response = await this.client.post(
      '/trade-jobs/matches/{jobId}/reject',
      {
        params: {
          path: { jobId },
        },
        body: data,
      },
      {
        headers: {
          'x-trade-company-id': companyId,
        },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(RejectJobResponseSchema, response.data),
    };
  }

  public async markJobComplete(
    companyId: number,
    jobId: string,
    data: { files: FileWithMetadata[] },
  ): Promise<AxiosResponse<MarkJobCompleteResponse>> {
    if (!companyId) {
      throw new Error('No company ID provided');
    }

    if (!data.files || data.files.length === 0) {
      throw new Error('No files provided');
    }

    const formData = new FormData();

    // Process files sequentially to maintain order
    for (const file of data.files) {
      if (!file.uri) {
        continue;
      }

      if (Platform.OS === 'web') {
        // For web, we need to fetch the file and create a proper File object
        const response = await fetch(file.uri);
        const blob = await response.blob();
        const fileName = file.originalname || `photo-${Date.now()}.jpg`;
        formData.append('files', blob, fileName);
      } else {
        // For iOS, we need to prepend file:// to the URI
        const fileUri =
          Platform.OS === 'ios' && !file.uri.startsWith('file://')
            ? `file://${file.uri}`
            : file.uri;

        // Get the filename from the URI
        const fileName = fileUri.split('/').pop() || `photo-${Date.now()}.jpg`;

        // Create a file object that matches the expected format
        formData.append('files', {
          uri: fileUri,
          type: file.mimetype || 'image/jpeg',
          name: fileName,
        } as unknown as File);
      }
    }

    const response = await this.client.post(
      '/trade-jobs/{jobId}/complete',
      {
        params: {
          path: { jobId },
        },
        // @ts-expect-error - OpenAPI spec incorrectly defines body as undefined for this multipart/form-data endpoint
        body: formData,
      },
      {
        headers: {
          'x-trade-company-id': companyId,
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        MarkJobCompleteResponseSchema,
        response.data,
      ),
    };
  }

  public async cancelJob(
    companyId: number,
    jobId: string,
    data: CancelJobRequest,
  ): Promise<AxiosResponse<CancelJobResponse>> {
    if (!companyId) {
      throw new Error('No company ID provided');
    }

    const response = await this.client.post(
      '/trade-jobs/matches/{jobId}/cancel',
      {
        params: {
          path: { jobId },
        },
        body: data,
      },
      {
        headers: {
          'x-trade-company-id': companyId,
        },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(CancelJobResponseSchema, response.data),
    };
  }

  public async getJobDetails(
    companyId: number,
    jobId: string,
  ): Promise<AxiosResponse<JobDetailsResponse>> {
    if (!companyId) {
      throw new Error('No company ID provided');
    }

    const response = await this.client.get(
      '/trade-jobs/matches/{jobId}',
      {
        params: {
          path: { jobId },
        },
      },
      {
        headers: {
          'x-trade-company-id': companyId,
        },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        JobDetailsResponseSchema,
        response.data,
      ),
    };
  }
}

export const tradeJobsApi = new TradeJobsApi();
