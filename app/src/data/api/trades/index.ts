import { AxiosResponse } from 'axios';
import { config } from 'src/config';
import { createApiClient } from 'src/data/apiClient';
import { getFirebaseAuthToken } from 'src/services/firebase/auth';
import { parseSchemaWithErrorCapture } from 'src/data/schemas/helpers/parseSchemaWithErrorCapture';
import {
  type SendCampaignAccreditationsNeededEmailBodyType,
  type SendCompleteCampaignSetupEmailBodyType,
  type ZuoraAccountResponseType,
  type ZuoraInvoicesResponseType,
  ZuoraAccountResponseSchema,
  ZuoraInvoiceItemsResponseSchema,
  ZuoraInvoiceItemsResponseType,
  ZuoraInvoicesResponseSchema,
  ZuoraRsaSignatureType,
  ZuoraRsaSignatureSchema,
} from 'src/data/schemas/api/trades';
import type { paths } from '../../api-specs/trades-api';

class TradesApi {
  private client: ReturnType<typeof createApiClient<paths>> =
    createApiClient<paths>({
      baseUrl: config.apiBaseUrl,
      getBearerToken: (forceRefresh) => getFirebaseAuthToken(forceRefresh),
    });

  public requestAvailability(companyId: number, tradeId: number) {
    return this.client.get('/api/availability/{companyId}/{tradeId}', {
      params: { path: { companyId, tradeId } },
    });
  }

  public getZuoraPaymentDetails(companyId: number) {
    return this.client.get('/api/zuora/request/{companyId}', {
      params: { path: { companyId } },
    });
  }

  public requestProfileDescription(companyId: number, tradeId: number) {
    return this.client.get('/api/profile-description/{companyId}/{tradeId}', {
      params: { path: { companyId, tradeId } },
    });
  }

  public generateQuoteInvoicePDF(
    companyId: number,
    quoteId: string,
    type: number,
  ) {
    return this.client.post(
      '/api/quotes-and-invoices/generate-pdf/{companyId}/{quoteId}/{type}',
      { params: { path: { companyId, quoteId, type } } },
    );
  }

  public requestCompanyCategories(companyId: number) {
    return this.client.get('/api/company-categories/{companyId}', {
      params: { path: { companyId } },
    });
  }

  public async getZuoraInvoices(
    companyId: number,
  ): Promise<AxiosResponse<ZuoraInvoicesResponseType>> {
    const response = await this.client.get('/api/zuora/request/{companyId}', {
      params: { path: { companyId } },
    });

    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        ZuoraInvoicesResponseSchema,
        response.data,
      ),
    };
  }

  public getZuoraInvoice(
    companyId: number,
    invoiceId: string,
  ): Promise<AxiosResponse<Blob>> {
    return this.client.get(
      '/api/zuora/request/{companyId}/{invoiceId}',
      { params: { path: { companyId, invoiceId } } },
      { responseType: 'blob' },
    );
  }
  public async getZuoraInvoiceItems(
    companyId: number,
    invoiceId: string,
  ): Promise<AxiosResponse<ZuoraInvoiceItemsResponseType>> {
    const response = await this.client.get(
      '/api/zuora/request/{companyId}/{invoiceId}/items',
      { params: { path: { companyId, invoiceId } } },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        ZuoraInvoiceItemsResponseSchema,
        response.data,
      ),
    };
  }

  public async getPaymentInfo(
    companyId: number,
  ): Promise<AxiosResponse<ZuoraAccountResponseType>> {
    const response = await this.client.get('/api/zuora/account/{companyId}', {
      params: { path: { companyId } },
    });
    return {
      ...response,
      data: parseSchemaWithErrorCapture(
        ZuoraAccountResponseSchema,
        response.data,
      ),
    };
  }

  public async getRsaSignature(
    companyId: number,
  ): Promise<AxiosResponse<ZuoraRsaSignatureType>> {
    const response = await this.client.get(
      '/api/zuora/account/signature/{companyId}',
      {
        params: { path: { companyId } },
      },
    );
    return {
      ...response,
      data: parseSchemaWithErrorCapture(ZuoraRsaSignatureSchema, response.data),
    };
  }

  public async handleZuoraPayment(
    companyId: number,
    paymentMethodId: string,
    amount: string,
  ) {
    return this.client.post(
      '/api/zuora/payment/{companyId}/{paymentMethodId}/{amount}',
      {
        params: {
          path: {
            companyId,
            paymentMethodId,
            amount,
          },
        },
      },
    );
  }

  public sendCompleteCampaignSetupEmail(
    companyId: number,
    campaignId: string,
    body: SendCompleteCampaignSetupEmailBodyType,
  ) {
    return this.client.post(
      '/api/campaign/{companyId}/{campaignId}/send-complete-campaign-setup-email',
      {
        params: { path: { companyId, campaignId } },
        body: body,
      },
    );
  }

  public sendCampaignAccreditationsNeededEmail(
    companyId: number,
    campaignId: string,
    body: SendCampaignAccreditationsNeededEmailBodyType,
  ) {
    return this.client.post(
      '/api/campaign/{companyId}/{campaignId}/send-accreditations-needed-email',
      {
        params: { path: { companyId, campaignId } },
        body: body,
      },
    );
  }
}

export const tradesApi = new TradesApi();
