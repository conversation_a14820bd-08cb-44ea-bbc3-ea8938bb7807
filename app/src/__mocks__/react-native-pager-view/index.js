/* eslint-disable @typescript-eslint/explicit-module-boundary-types, react/prop-types */
import React from 'react';
import { View } from 'react-native';

class PagerView extends React.Component {
  viewRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = { index: 0 };
  }

  setPage(selectedPage) {
    this.viewRef.current?.props.onPageSelected(selectedPage);
  }

  setPageWithoutAnimation() {
    // nothing
  }

  setScrollEnabled() {
    // nothing
  }

  render() {
    const {
      children,
      initialPage,
      onPageSelected,
      style,
      scrollEnabled,
      testID,
    } = this.props;
    const { index } = this.state;

    return (
      <View
        ref={this.viewRef}
        testID={testID}
        initialPage={initialPage}
        onPageSelected={(page) => {
          this.setState({
            index: page,
          });

          if (onPageSelected) {
            onPageSelected({ nativeEvent: { position: page } });
          }
        }}
        style={style}
        scrollEnabled={scrollEnabled}
      >
        {children[index]}
      </View>
    );
  }
}

export { PagerView };

export default PagerView;
