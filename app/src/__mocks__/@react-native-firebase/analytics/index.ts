export const lengthError = new Error('eventName too long!');
export const mockLogEvent: (eventName: string) => void = jest.fn(
  (eventName) => {
    if (eventName.length > 40) {
      throw lengthError;
    }
  },
);
export const mockLogScreenView: () => void = jest.fn();
export const mockSetUserId: () => void = jest.fn();
export const mockSetUserProperty: () => void = jest.fn();

const analytics = jest.fn(() => ({
  logEvent: mockLogEvent,
  logScreenView: mockLogScreenView,
  setUserId: mockSetUserId,
  setUserProperty: mockSetUserProperty,
}));

export default analytics;
