/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-function */
import React, { ReactElement, Component } from 'react';
import { View } from 'react-native';

class BottomSheetModal extends Component<
  { children: any },
  { visible: boolean }
> {
  state = { visible: false };

  snapToIndex() {}

  snapToPosition() {}

  expand() {}

  collapse() {}

  close() {}

  forceClose() {}

  present() {
    this.setState({ visible: true });
  }

  dismiss() {
    this.setState({ visible: false });
  }

  render(): ReactElement {
    return this.state.visible ? this.props.children : <React.Fragment />;
  }
}

module.exports = {
  BottomSheetModal,
  BottomSheetView: View,
};
