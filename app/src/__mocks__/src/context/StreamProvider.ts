import { tradeAppBff } from 'src/data/api/trade-app-bff';

const connectUser = jest.fn().mockImplementation(async () => {
  await tradeAppBff.auth.getChatToken(123);
});
const disconnectUser = jest.fn();

export class StreamChat {
  static getInstance = (): StreamChat => new StreamChat();
  on = jest.fn();
  connectUser = connectUser;
  disconnectUser = disconnectUser;
  queryChannels = jest.fn(() => Promise.resolve([]));
}
