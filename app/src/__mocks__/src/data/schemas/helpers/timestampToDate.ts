import { z } from 'zod';
import { TimestampInstance } from 'src/services/firebase/firestoreUtils';
import type { Timestamp } from 'src/services/firebase/firestore.types';

/**
 * See src/data/schemas/helpers/README.md
 * @returns a date transform for Firestore Timestamps
 */
export const timestampToDate = z
  .custom<Date | Timestamp>(
    (data) => data instanceof TimestampInstance || data instanceof Date,
  )
  .transform<Date>((data) => {
    if (data instanceof Date) {
      return data;
    }

    if (data instanceof TimestampInstance) {
      return (data as unknown as Timestamp)!.toDate();
    }

    throw Error('Invalid Date/Timestamp');
  });
