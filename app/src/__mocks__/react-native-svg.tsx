import React, { ReactElement } from 'react';
import { View } from 'react-native';

export type { SvgProps } from 'react-native-svg';

export default (props: Record<string, unknown>): ReactElement => (
  <View {...props} />
);

export const Path = (): ReactElement => <View />;
export const Ellipse = (): ReactElement => <View />;
export const G = (): ReactElement => <View />;
export const Defs = (): ReactElement => <View />;
export const LinearGradient = (): ReactElement => <View />;
export const Stop = (): ReactElement => <View />;
export const ClipPath = (): ReactElement => <View />;
