import React, { ReactElement } from 'react';
import { TouchableOpacity, Text } from 'react-native';

// There is a known issue of menu options not coming up in testing
// gh issues:
// https://github.com/instea/react-native-popup-menu/issues/253
// https://github.com/instea/react-native-popup-menu/issues/243

const mockedOnPress = jest.fn();
const mockedMenuOption = (
  <TouchableOpacity testID="menuOption" onPress={mockedOnPress}>
    <Text>{'Item'}</Text>
  </TouchableOpacity>
);
export const Menu = 'Menu';
export const MenuContext = 'MenuContext';
export const MenuOptions = 'MenuOptions';
export const MenuOption = (): ReactElement => mockedMenuOption;
export const MenuTrigger = 'MenuTrigger';
export const renderers = {
  Popover: jest.fn(),
};
