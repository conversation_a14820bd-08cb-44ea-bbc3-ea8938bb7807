/* eslint-disable import/no-default-export */
import { ConfigPlugin } from 'expo/config-plugins';

type AndroidNetSecurityConfigProps = {
  isAppStoreBuild?: boolean;
  isDevClient?: boolean;
};

/**
 * Config plugin allowing customizing native Android and iOS build properties for managed apps.
 * @param config Expo config for application.
 * @param props Configuration for the build properties plugin.
 */
export declare const withAndroidNetSecurityConfig: ConfigPlugin<AndroidNetSecurityConfigProps>;
export default withAndroidNetSecurityConfig;
