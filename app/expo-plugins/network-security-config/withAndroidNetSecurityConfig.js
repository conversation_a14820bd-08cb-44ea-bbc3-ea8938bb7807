/* eslint-disable @typescript-eslint/no-var-requires */
const { AndroidConfig, withAndroidManifest } = require('@expo/config-plugins');
const { Paths } = require('@expo/config-plugins/build/android');
const path = require('path');
const fs = require('fs');

const fsPromises = fs.promises;

const { getMainApplicationOrThrow } = AndroidConfig.Manifest;

/**
 *
 * @param {Object} config - ExpoConfig
 * @param {String} fileSuffix - debug, internal, prod
 */
const copySecurityConfigFile = async (config, fileSuffix) => {
  const sourceFilePath = path.join(
    __dirname,
    `network_security_config_${fileSuffix}.xml`,
  );
  const destFilePath = path.join(
    await Paths.getResourceFolderAsync(config.modRequest.projectRoot),
    'xml',
    'network_security_config.xml',
  );

  const destDirectory = path.resolve(destFilePath, '..');

  if (!fs.existsSync(destDirectory)) {
    await fsPromises.mkdir(destDirectory);
  }

  await fsPromises.copyFile(sourceFilePath, destFilePath);
};

const getFileSuffix = (isAppStoreBuild, isDevClient) => {
  if (isAppStoreBuild) {
    return 'prod';
  }

  return isDevClient ? 'devclient' : 'internal';
};

/**
 *
 * @param {Object} config - ExpoConfig
 * @param {Object} options - { isAppStoreBuild: boolean }
 * @returns {Object} ExpoConfig
 */
const withAndroidNetSecurityConfig = (
  config,
  { isAppStoreBuild, isDevClient },
) => {
  return withAndroidManifest(config, async (manifestConfig) => {
    // Copy internal/release network_security_config.xml to res/xml
    await copySecurityConfigFile(
      manifestConfig,
      getFileSuffix(isAppStoreBuild, isDevClient),
    );

    // Update AndroidManifest.xml
    const mainApplication = getMainApplicationOrThrow(
      manifestConfig.modResults,
    );
    mainApplication.$['android:networkSecurityConfig'] =
      '@xml/network_security_config';

    return manifestConfig;
  });
};

module.exports = withAndroidNetSecurityConfig;
