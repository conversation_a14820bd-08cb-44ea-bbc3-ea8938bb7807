/* eslint-disable @typescript-eslint/no-var-requires */
const { withDangerousMod } = require('@expo/config-plugins');
const {
  mergeContents,
} = require('@expo/config-plugins/build/utils/generateCode');
const fs = require('fs');
const path = require('path');

async function readFileAsync(filePath) {
  return fs.promises.readFile(filePath, 'utf8');
}

async function saveFileAsync(filePath, content) {
  return fs.promises.writeFile(filePath, content, 'utf8');
}

function disableAdIDSupport(src) {
  return mergeContents({
    tag: 'withRNFirebaseDisableAdId',
    src,
    newSrc: '$RNFirebaseAnalyticsWithoutAdIdSupport = true',
    anchor: /platform :ios/,
    offset: 0,
    comment: '#',
  }).contents;
}

const withRNFirebaseDisableAdId = (config) => {
  return withDangerousMod(config, [
    'ios',
    async (modConfig) => {
      const file = path.join(
        modConfig.modRequest.platformProjectRoot,
        'Podfile',
      );
      const contents = await readFileAsync(file);
      await saveFileAsync(file, disableAdIDSupport(contents));
      return modConfig;
    },
  ]);
};

module.exports = withRNFirebaseDisableAdId;
