/* eslint-disable import/no-default-export */
import { ConfigPlugin } from 'expo/config-plugins';

/**
 * Config plugin allowing customizing native Android and iOS build properties for managed apps.
 * @param config Expo config for application.
 * @param props Configuration for the build properties plugin.
 */
export declare const withRNFirebaseDisableAdId: ConfigPlugin;
export default withRNFirebaseDisableAdId;
