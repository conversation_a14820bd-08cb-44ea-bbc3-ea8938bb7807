/* eslint-disable import/no-default-export */
import { AndroidDefaultBrowserModule } from './AndroidDefaultBrowser.types';

class NoOpDefaultBrowserModule implements AndroidDefaultBrowserModule {
  openURL(_url: string): Promise<void> {
    throw new Error(
      'AndroidDefaultBrowser.openURL is not available on this platform',
    );
  }
}

// This call loads the native module object from the JSI.
export default new NoOpDefaultBrowserModule();
