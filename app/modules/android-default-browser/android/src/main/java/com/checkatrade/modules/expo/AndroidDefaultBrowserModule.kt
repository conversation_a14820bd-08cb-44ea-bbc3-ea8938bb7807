package com.checkatrade.modules.expo

import android.content.Intent
import androidx.core.net.toUri
import expo.modules.kotlin.modules.Module
import expo.modules.kotlin.modules.ModuleDefinition

class AndroidDefaultBrowserModule : Module() {
  // Each module class must implement the definition function. The definition consists of components
  // that describes the module's functionality and behavior.
  // See https://docs.expo.dev/modules/module-api for more details about available components.
  override fun definition() = ModuleDefinition {
    Name("AndroidDefaultBrowser")

    AsyncFunction("openURL") { url: String ->
      // https://stackoverflow.com/a/58801285
      // This forces <PERSON> to open the URL in the default browser
      val defaultBrowser =
        Intent.makeMainSelectorActivity(Intent.ACTION_MAIN, Intent.CATEGORY_APP_BROWSER)
      defaultBrowser.setData(url.toUri())
      appContext.throwingActivity.startActivity(defaultBrowser)
    }
  }
}
