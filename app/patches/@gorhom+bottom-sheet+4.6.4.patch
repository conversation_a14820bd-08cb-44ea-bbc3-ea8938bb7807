diff --git a/node_modules/@gorhom/bottom-sheet/src/components/bottomSheet/BottomSheet.tsx b/node_modules/@gorhom/bottom-sheet/src/components/bottomSheet/BottomSheet.tsx
index cd6ea6b..85a02cf 100644
--- a/node_modules/@gorhom/bottom-sheet/src/components/bottomSheet/BottomSheet.tsx
+++ b/node_modules/@gorhom/bottom-sheet/src/components/bottomSheet/BottomSheet.tsx
@@ -527,7 +527,7 @@ const BottomSheetComponent = forwardRef<BottomSheet, BottomSheetProps>(
         animatedAnimationSource.value === ANIMATION_SOURCE.SNAP_POINT_CHANGE &&
         animatedAnimationState.value === ANIMATION_STATE.RUNNING
       ) {
-        return animatedNextPositionIndex.value;
+        return Math.max(animatedNextPositionIndex.value, 1);
       }
 
       return currentIndex;
