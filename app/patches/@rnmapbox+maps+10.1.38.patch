diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/RNMBXImagesManager.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/RNMBXImagesManager.kt
index fb2147f..a80c05d 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/RNMBXImagesManager.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/RNMBXImagesManager.kt
@@ -249,10 +249,12 @@ class RNMBXImagesManager(private val mContext: ReactApplicationContext) :
                     Logger.e("RNMBXImages", "each element of strech should be an array but was: ${array.getDynamic(i)}")
                 } else {
                     val pair = array.getArray(i)
-                    if (pair.size() != 2 || pair.getType(0) != ReadableType.Number || pair.getType(1) != ReadableType.Number) {
-                        Logger.e("RNMBXImages", "each element of stretch should be pair of 2 integers but was ${pair}")
-                    }
-                    result.add(ImageStretches(pair.getDouble(0).toFloat(), pair.getDouble(1).toFloat()))
+                    if (pair != null) {
+                       if (pair.size() != 2 || pair.getType(0) != ReadableType.Number || pair.getType(1) != ReadableType.Number) {
+                           Logger.e("RNMBXImages", "each element of stretch should be pair of 2 integers but was ${pair}")
+                       }
+                       result.add(ImageStretches(pair.getDouble(0).toFloat(), pair.getDouble(1).toFloat()))
+                   }
                 }
             }
             return result;
diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapView.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapView.kt
index d390514..77e7b61 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapView.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapView.kt
@@ -15,7 +15,7 @@ import android.widget.FrameLayout
 import androidx.lifecycle.Lifecycle
 import androidx.lifecycle.LifecycleOwner
 import androidx.lifecycle.LifecycleRegistry
-import androidx.lifecycle.ViewTreeLifecycleOwner
+import androidx.lifecycle.setViewTreeLifecycleOwner
 import com.facebook.react.bridge.*
 import com.mapbox.android.gestures.*
 import com.mapbox.bindgen.Value
@@ -131,11 +131,9 @@ class RNMBXLifeCycle {
                     }
                 }
 
-                override fun getLifecycle(): Lifecycle {
-                    return lifecycleRegistry
-                }
+                override val lifecycle: Lifecycle get() = lifecycleRegistry
             }
-            ViewTreeLifecycleOwner.set(view, lifecycleOwner);
+            view.setViewTreeLifecycleOwner(lifecycleOwner)
         }
         lifecycleOwner?.handleLifecycleEvent(Lifecycle.Event.ON_START)
     }
@@ -1573,6 +1571,3 @@ fun OrnamentSettings.setPosAndMargins(posAndMargins: ReadableMap?) {
     this.position = position
     this.margins = margins
 }
\ No newline at end of file
-
-
-
diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapViewManager.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapViewManager.kt
index 4df25e1..5edd07e 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapViewManager.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapViewManager.kt
@@ -34,13 +34,13 @@ import java.util.HashMap
 
 fun ReadableArray.forEachString(action: (String) -> Unit) {
     for (i in 0 until size()) {
-        action(getString(i))
+       getString(i)?.let { action(it) }
     }
 }
 
 fun ReadableArray.asArrayString(): Array<String> {
     val result = Array<String>(size()) {
-        getString(it)
+        getString(it).toString()
     }
     return result
 }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/RNMBXStyleValue.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/RNMBXStyleValue.kt
index db13128..bf003c1 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/RNMBXStyleValue.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/RNMBXStyleValue.kt
@@ -82,7 +82,9 @@ class RNMBXStyleValue(config: ReadableMap) {
         val result = ArrayList<Double>(arr!!.size())
         for (i in 0 until arr.size()) {
             val item = arr.getMap(i)
-            result.add(item.getDouble("value"))
+            if (item != null) {
+               result.add(item.getDouble("value"))
+            }
         }
         return result
     }
@@ -104,7 +106,7 @@ class RNMBXStyleValue(config: ReadableMap) {
         val result = ArrayList<String>(arr!!.size())
         for (i in 0 until arr.size()) {
             val item = arr.getMap(i)
-            val value = item.getString("value")
+            val value = item?.getString("value")
             if (value != null) {
                 result.add(value)
             } else {
@@ -121,9 +123,11 @@ class RNMBXStyleValue(config: ReadableMap) {
                 val result = WritableNativeMap()
                 for (i in 0 until keyValues!!.size()) {
                     val keyValue = keyValues.getArray(i)
-                    val stringKey = keyValue.getMap(0).getString("value")
+                    val stringKey = keyValue?.getMap(0)?.getString("value")
                     val value = WritableNativeMap()
-                    value.merge(keyValue.getMap(1))
+                    if (keyValue != null) {
+                        keyValue.getMap(1)?.let { value.merge(it) }
+                    }
                     result.putMap(stringKey!!, value)
                 }
                 return result
@@ -131,7 +135,7 @@ class RNMBXStyleValue(config: ReadableMap) {
             return null
         }
 
-    fun getMap(_key: String?): ReadableMap? {
+    fun getMap(_key: String): ReadableMap? {
         return map
     }
 
diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXShapeSourceManager.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXShapeSourceManager.kt
index 637372d..2b45f07 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXShapeSourceManager.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXShapeSourceManager.kt
@@ -119,7 +119,7 @@ class RNMBXShapeSourceManager(private val mContext: ReactApplicationContext, val
                     )
                     ReadableType.Boolean -> Expression.literal(expressions.getBoolean(iExp))
                     ReadableType.Number -> Expression.literal(expressions.getDouble(iExp))
-                    else -> Expression.literal(expressions.getString(iExp))
+                    else -> expressions.getString(iExp)?.let { Expression.literal(it) }!!
                 }
                 builder.add(argument)
             }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXTileSourceManager.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXTileSourceManager.kt
index 3b0a072..bd61299 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXTileSourceManager.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXTileSourceManager.kt
@@ -41,7 +41,7 @@ abstract class RNMBXTileSourceManager<T : RNMBXTileSource<*>> internal construct
         val urls: MutableList<String> = ArrayList()
         for (i in 0 until tileUrlTemplates.asArray().size()) {
             if (tileUrlTemplates.asArray().getType(0) == ReadableType.String) {
-                urls.add(tileUrlTemplates.asArray().getString(i))
+                tileUrlTemplates.asArray().getString(i)?.let { urls.add(it) }
             }
         }
         source!!.tileUrlTemplates = urls
diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/shapeAnimators/RNMBXChangeLineOffsetsShapeAnimatorModule.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/shapeAnimators/RNMBXChangeLineOffsetsShapeAnimatorModule.kt
index 28d1020..9d77aba 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/shapeAnimators/RNMBXChangeLineOffsetsShapeAnimatorModule.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/shapeAnimators/RNMBXChangeLineOffsetsShapeAnimatorModule.kt
@@ -210,7 +210,7 @@ private fun buildLineString(_coordinates: ReadableArray): LineString {
 
     for (i in 0 until _coordinates.size()) {
         val arr = _coordinates.getArray(i)
-        val coord = Point.fromLngLat(arr.getDouble(0), arr.getDouble(1))
+        val coord = Point.fromLngLat(arr!!.getDouble(0), arr.getDouble(1))
         coordinates = coordinates.plus(coord)
     }
 
diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/Dynamic.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/Dynamic.kt
index d52fa21..b1d2c85 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/Dynamic.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/Dynamic.kt
@@ -33,15 +33,16 @@ fun ReadableArray.toValue(): Value {
     var result = ArrayList<Value>(size())
 
     for (i in 0 until size()) {
-        result.add(
         when (getType(i)) {
             ReadableType.Null -> Value.nullValue()
             ReadableType.Boolean -> Value.valueOf(getBoolean(i))
             ReadableType.Number -> Value.valueOf(getDouble(i))
-            ReadableType.String -> Value.valueOf(getString(i))
-            ReadableType.Array -> getArray(i).toValue()
-            ReadableType.Map -> getMap(i).toValue()
-        })
+            ReadableType.String -> getString(i)?.let { Value.valueOf(it) }
+            ReadableType.Array -> getArray(i)?.toValue()
+            ReadableType.Map -> getMap(i)?.toValue()
+        }?.let {
+            result.add(it)
+        }
     }
     return Value.valueOf(result)
 }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableArray.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableArray.kt
index 83a78bc..5e02895 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableArray.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableArray.kt
@@ -46,8 +46,8 @@ fun ReadableArray.toJsonArray() : JsonArray {
     val result = JsonArray(size())
     for (i in 0 until size()) {
         when (getType(i)) {
-            ReadableType.Map -> result.add(getMap(i).toJsonObject())
-            ReadableType.Array -> result.add(getArray(i).toJsonArray())
+            ReadableType.Map -> result.add(getMap(i)!!.toJsonObject())
+            ReadableType.Array -> result.add(getArray(i)!!.toJsonArray())
             ReadableType.Null -> result.add(null as JsonElement?)
             ReadableType.Number -> result.add(getDouble(i))
             ReadableType.String -> result.add(getString(i))
diff --git a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableMap.kt b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableMap.kt
index c69ef8c..d2e99cd 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableMap.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableMap.kt
@@ -25,7 +25,7 @@ fun ReadableMap.forEach(action: (String, Any) -> Unit) {
     val iterator = this.entryIterator
     while (iterator.hasNext()) {
         val next = iterator.next()
-        action(next.key, next.value)
+        next.value?.let { action(next.key, it) }
     }
 }
 fun ReadableMap.getIfDouble(key: String): Double? {
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXAtmosphereManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXAtmosphereManagerDelegate.java
index 5a06406..7032b6c 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXAtmosphereManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXAtmosphereManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXAtmosphereManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXAtmosphereManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXAtmosphereManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXAtmosphereManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXAtmosphereManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXBackgroundLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXBackgroundLayerManagerDelegate.java
index 76568d1..89c5662 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXBackgroundLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXBackgroundLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXBackgroundLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXBackgroundLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXBackgroundLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXBackgroundLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXBackgroundLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCalloutManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCalloutManagerDelegate.java
index 2b3e086..dbace3f 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCalloutManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCalloutManagerDelegate.java
@@ -11,10 +11,12 @@ package com.facebook.react.viewmanagers;
 
 import android.view.View;
 import androidx.annotation.Nullable;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXCalloutManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXCalloutManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXCalloutManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXCalloutManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXCalloutManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCameraManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCameraManagerDelegate.java
index eef1384..9e4c5a2 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCameraManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCameraManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXCameraManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXCameraManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXCameraManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXCameraManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXCameraManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCircleLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCircleLayerManagerDelegate.java
index 3b2b41b..1ee3092 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCircleLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCircleLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXCircleLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXCircleLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXCircleLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXCircleLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXCircleLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCustomLocationProviderManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCustomLocationProviderManagerDelegate.java
index f3bd0f8..7f63c59 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCustomLocationProviderManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXCustomLocationProviderManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXCustomLocationProviderManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXCustomLocationProviderManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXCustomLocationProviderManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXCustomLocationProviderManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXCustomLocationProviderManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXFillExtrusionLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXFillExtrusionLayerManagerDelegate.java
index 0293627..c37a8b7 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXFillExtrusionLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXFillExtrusionLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXFillExtrusionLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXFillExtrusionLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXFillExtrusionLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXFillExtrusionLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXFillExtrusionLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXFillLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXFillLayerManagerDelegate.java
index 6e5f1ad..da19e3f 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXFillLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXFillLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXFillLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXFillLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXFillLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXFillLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXFillLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXHeatmapLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXHeatmapLayerManagerDelegate.java
index f7358c2..39fd79b 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXHeatmapLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXHeatmapLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXHeatmapLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXHeatmapLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXHeatmapLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXHeatmapLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXHeatmapLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImageManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImageManagerDelegate.java
index 0857a07..5abf7c1 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImageManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImageManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXImageManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXImageManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXImageManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXImageManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXImageManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImageSourceManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImageSourceManagerDelegate.java
index d0f6999..bb46c55 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImageSourceManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImageSourceManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXImageSourceManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXImageSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXImageSourceManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXImageSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXImageSourceManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImagesManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImagesManagerDelegate.java
index 59a80b2..7147c92 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImagesManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXImagesManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXImagesManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXImagesManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXImagesManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXImagesManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXImagesManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXLightManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXLightManagerDelegate.java
index 90cd362..31c8194 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXLightManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXLightManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXLightManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXLightManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXLightManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXLightManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXLightManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXLineLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXLineLayerManagerDelegate.java
index 47b42a5..abd033f 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXLineLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXLineLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXLineLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXLineLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXLineLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXLineLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXLineLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMapViewManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMapViewManagerDelegate.java
index 9cff5aa..783ee90 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMapViewManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMapViewManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXMapViewManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXMapViewManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXMapViewManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXMapViewManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXMapViewManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMarkerViewContentManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMarkerViewContentManagerDelegate.java
index 9f88fce..084c995 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMarkerViewContentManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMarkerViewContentManagerDelegate.java
@@ -11,10 +11,12 @@ package com.facebook.react.viewmanagers;
 
 import android.view.View;
 import androidx.annotation.Nullable;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXMarkerViewContentManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXMarkerViewContentManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXMarkerViewContentManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXMarkerViewContentManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXMarkerViewContentManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMarkerViewManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMarkerViewManagerDelegate.java
index 78fd466..1adc4a9 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMarkerViewManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXMarkerViewManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXMarkerViewManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXMarkerViewManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXMarkerViewManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXMarkerViewManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXMarkerViewManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXModelLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXModelLayerManagerDelegate.java
index 28dfb46..1054fbc 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXModelLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXModelLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXModelLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXModelLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXModelLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXModelLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXModelLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXModelsManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXModelsManagerDelegate.java
index 7513c49..fcef289 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXModelsManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXModelsManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXModelsManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXModelsManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXModelsManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXModelsManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXModelsManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXNativeUserLocationManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXNativeUserLocationManagerDelegate.java
index b0ef22a..c73d1a5 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXNativeUserLocationManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXNativeUserLocationManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXNativeUserLocationManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXNativeUserLocationManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXNativeUserLocationManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXNativeUserLocationManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXNativeUserLocationManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXPointAnnotationManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXPointAnnotationManagerDelegate.java
index 70aa36d..9fb8bcb 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXPointAnnotationManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXPointAnnotationManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXPointAnnotationManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXPointAnnotationManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXPointAnnotationManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXPointAnnotationManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXPointAnnotationManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterDemSourceManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterDemSourceManagerDelegate.java
index 6a0ef3b..cdf0f58 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterDemSourceManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterDemSourceManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXRasterDemSourceManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXRasterDemSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXRasterDemSourceManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXRasterDemSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXRasterDemSourceManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterLayerManagerDelegate.java
index 0630ce3..629fd18 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXRasterLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXRasterLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXRasterLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXRasterLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXRasterLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterSourceManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterSourceManagerDelegate.java
index 68c2634..29198ae 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterSourceManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXRasterSourceManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXRasterSourceManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXRasterSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXRasterSourceManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXRasterSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXRasterSourceManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXShapeSourceManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXShapeSourceManagerDelegate.java
index 75ebce1..e697ad3 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXShapeSourceManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXShapeSourceManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXShapeSourceManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXShapeSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXShapeSourceManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXShapeSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXShapeSourceManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXSkyLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXSkyLayerManagerDelegate.java
index 446a1aa..58579fc 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXSkyLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXSkyLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXSkyLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXSkyLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXSkyLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXSkyLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXSkyLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXStyleImportManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXStyleImportManagerDelegate.java
index 5a867b7..0e8f234 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXStyleImportManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXStyleImportManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXStyleImportManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXStyleImportManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXStyleImportManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXStyleImportManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXStyleImportManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXSymbolLayerManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXSymbolLayerManagerDelegate.java
index ebf1112..ee9e8b0 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXSymbolLayerManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXSymbolLayerManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXSymbolLayerManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXSymbolLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXSymbolLayerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXSymbolLayerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXSymbolLayerManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXTerrainManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXTerrainManagerDelegate.java
index 54a428c..332465e 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXTerrainManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXTerrainManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXTerrainManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXTerrainManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXTerrainManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXTerrainManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXTerrainManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXVectorSourceManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXVectorSourceManagerDelegate.java
index c47abe3..55adc84 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXVectorSourceManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXVectorSourceManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXVectorSourceManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXVectorSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXVectorSourceManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXVectorSourceManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXVectorSourceManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXViewportManagerDelegate.java b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXViewportManagerDelegate.java
index dc37ae1..ec23013 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXViewportManagerDelegate.java
+++ b/node_modules/@rnmapbox/maps/android/src/main/old-arch/com/facebook/react/viewmanagers/RNMBXViewportManagerDelegate.java
@@ -12,10 +12,12 @@ package com.facebook.react.viewmanagers;
 import android.view.View;
 import androidx.annotation.Nullable;
 import com.facebook.react.bridge.DynamicFromObject;
+
+import com.facebook.react.uimanager.BaseViewManager;
 import com.facebook.react.uimanager.BaseViewManagerDelegate;
-import com.facebook.react.uimanager.BaseViewManagerInterface;
+import com.facebook.react.uimanager.LayoutShadowNode;
 
-public class RNMBXViewportManagerDelegate<T extends View, U extends BaseViewManagerInterface<T> & RNMBXViewportManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+public class RNMBXViewportManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMBXViewportManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
   public RNMBXViewportManagerDelegate(U viewManager) {
     super(viewManager);
   }
diff --git a/node_modules/@rnmapbox/maps/android/src/main/rn-compat/rn75/com/rnmapbox/rnmbx/rncompat/ReadableMap.kt b/node_modules/@rnmapbox/maps/android/src/main/rn-compat/rn75/com/rnmapbox/rnmbx/rncompat/ReadableMap.kt
index fee7f22..6181dc9 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/rn-compat/rn75/com/rnmapbox/rnmbx/rncompat/ReadableMap.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/rn-compat/rn75/com/rnmapbox/rnmbx/rncompat/ReadableMap.kt
@@ -1,7 +1,7 @@
 package com.rnmapbox.rnmbx.rncompat.readable_map
 import com.facebook.react.bridge.ReadableMap
 
-fun ReadableMap.getEntryIterator():  Iterator<Map. Entry<String, Any>>
+fun ReadableMap.getEntryIterator(): Iterator<Map.Entry<String, Any?>>
 {
     return this.entryIterator
 }
