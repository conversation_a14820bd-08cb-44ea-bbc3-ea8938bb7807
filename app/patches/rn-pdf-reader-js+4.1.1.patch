diff --git a/node_modules/rn-pdf-reader-js/lib/index.js b/node_modules/rn-pdf-reader-js/lib/index.js
index 86a7e0a..dc78ce6 100644
--- a/node_modules/rn-pdf-reader-js/lib/index.js
+++ b/node_modules/rn-pdf-reader-js/lib/index.js
@@ -280,7 +280,7 @@ class PdfReader extends React.Component {
                     onError,
                     onHttpError: onError,
                     style,
-                    source: renderedOnce || !isAndroid ? source : undefined,
+                    source: renderedOnce || !isAndroid ? source : { uri: '' },
                 }, { allowFileAccess: isAndroid, allowFileAccessFromFileURLs: isAndroid, allowUniversalAccessFromFileURLs: isAndroid, scalesPageToFit: Platform.select({ android: false }), mixedContentMode: isAndroid ? 'always' : undefined, sharedCookiesEnabled: false, startInLoadingState: !noLoader, renderLoading: () => (noLoader ? React.createElement(View, null) : React.createElement(Loader, null)) }, webviewProps))));
         }
         return !noLoader && !ready && React.createElement(Loader, null);
