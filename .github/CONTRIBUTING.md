# Contribution guidelines

This document is essential reading for anyone contributing to this project. Please take some time to read through it carefully.

If you have any questions, you can reach the team on the [#ask-trade-experience](https://hshomeexperts.slack.com/archives/C01K3SWG35Y) Slack channel

## Version control, branching, & pull request strategies

A typical workflow for new features or refactoring would be:

1. Branch from main - name your branch using the pattern: `feature/{JIRA-number}_{description}`
2. Do your work
3. Push your branch to origin
4. CI workflows will trigger on the every commit to the remote branch
5. Raise a PR when your work is feature-complete. PR titles should follow this format `feature(JIRA-NUMBER): descriptive title`
6. Product, design and QA review process begins
7. Merge back to main following QA approval
8. Tag the main branch to begin release workflows

### Branches

Valid branch regexp:

```
^(chore|fix|bugfix|feat|feature|next|story)\/[a-zA-Z]+\-[0-9._-]+\_[a-zA-Z0-9-]+$
```

- Feature branches should be based off `main`
- Branch names should be all lower case letters, numbers, `-`, `_`, and `/`.
- Use this pattern for feature branch names: `feature/{JIRA-number}_{short-description}` (and push to `origin` for others to contribute to)
- branches can be prefixed with `feature`, `feat`, `chore`, `fix`, `bugfix`, `story` or `next` (native release only)

Generally speaking, for a Jira ticket `TEX-XXX`, the git graph should look something like:

```mermaid
gitGraph
  commit
  commit
  branch feature/tex-xxx-short-description
  checkout feature/tex-xxx-short-description
  branch story/tex-xxx-sub-task-1
  checkout story/tex-xxx-sub-task-1
  commit
  commit
  checkout feature/tex-xxx-short-description
  merge story/tex-xxx-sub-task-1
  branch story/tex-xxx-sub-task-2
  checkout story/tex-xxx-sub-task-2
  commit
  commit
  checkout feature/tex-xxx-short-description
  merge story/tex-xxx-sub-task-2
  checkout main
  merge feature/tex-xxx-short-description
  commit
  commit
```

### Pull Requests

> [!IMPORTANT]
> Please be respectful of the team's time. Smaller, more frequent PRs are going to get a quicker response. Use the suggested branching strategies to break up big work into smaller parts.

- When creating a pull request, if it's not ready for review, open it in [Draft mode][gh-draft]
- Pull requests should be titled using `feature(JIRA-NUMBER): descriptive title` (don't just accept GitHub's default title)
- Use "Squash and merge" to merge a pull request to `main` (only option available)
- The body of the pull request must not be empty

### Updating PR Reviewed Branches

Once you've requested reviews from your teammates, please **avoid** using **Force Push** unless absolutely necessary. Force Pushing causes the branch to become detached and changes that someone may have already reviewed become disconnected.

- Existing comments and suggestions will not be kept in line with subsequent updates (you might mark a comment as "resolved" but your reviewer won't be able to see how you resolved it)
- Reviewers won't be able to view changes-since-last-review forcing them to re-review everything
- If using the branching model from above, any branches off of your branch will become disconnected as well

Use **Merge Commits** to update your branches _from_ `main`. Use **Squash and merge** to merge your branches _to_ `main`.

## Collaborating / Pair-Programming

If you're going to pair up with someone, make sure you give them credit for their work by including the following in the commit message:

```text
Co-Authored-By: First Last <<EMAIL>>
```

[gh-draft]: https://github.blog/2019-02-14-introducing-draft-pull-requests/
