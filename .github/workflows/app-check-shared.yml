name: Run checks (/app)

on:
  workflow_call:
    secrets:
      CI_GH_PACKAGE_TOKEN:
        description: 'GitHub Packages token'
        required: true
      CAT_SCOPE_PAT:
        description: 'GH Packages token with access to @checkatrade scope'
        required: true

jobs:
  app-type-checks:
    name: 'Typescript (tsc) check'
    runs-on: ubuntu-latest
    timeout-minutes: 15
    defaults:
      run:
        working-directory: app
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'app/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'app/package-lock.json'
      - name: Install
        run: npm ci
        env:
          CAT_GITHUB_PACKAGES_TOKEN: ${{ secrets.CAT_SCOPE_PAT }}
      - name: Check types
        run: npm run check-types

  app-type-lint-checks:
    name: 'Linting checks'
    runs-on: ubuntu-latest
    timeout-minutes: 15
    defaults:
      run:
        working-directory: app
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'app/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'app/package-lock.json'
      - name: Install
        run: npm ci
        env:
          CAT_GITHUB_PACKAGES_TOKEN: ${{ secrets.CAT_SCOPE_PAT }}
      - name: Check types
        run: npm run format
      - name: Check lint
        run: npm run lint

  app-unit-tests:
    name: Jest test ${{ matrix.chunk }}/4
    runs-on: ubuntu-latest
    timeout-minutes: 15
    strategy:
      # if one jobs fails, will be canceled
      fail-fast: false
      matrix:
        chunk: [1, 2, 3, 4]
    defaults:
      run:
        working-directory: app
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'app/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'app/package-lock.json'
        env:
          ADYEN_POS_TOKEN: ${{ secrets.ADYEN_POS_TOKEN }}
      - name: Install
        run: npm ci
        env:
          CAT_GITHUB_PACKAGES_TOKEN: ${{ secrets.CAT_SCOPE_PAT }}
      - name: Run unit tests - Jest
        run: npm run test:ci -- --maxWorkers=2 --shard=${{ matrix.chunk }}/4
