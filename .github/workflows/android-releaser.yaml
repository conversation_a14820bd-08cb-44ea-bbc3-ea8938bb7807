name: android-release
on:
  push:
    branches:
      - main
    paths:
      - 'app/android/**/*'

jobs:
  android-release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Release to CCV
        id: release
        uses: cat-home-experts/go-releaser@main
        with:
          token: ${{ secrets.CCV_TOKEN }}
          branch: ${{ github.ref_name }}
          environment: prod
          service: trade-experience-android
          revision: ${{ github.sha }}
          repository_url: https://github.com/${{ github.repository }}
