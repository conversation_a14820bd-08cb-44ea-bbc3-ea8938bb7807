name: EAS Updates - Publish

on:
  workflow_call:
    secrets:
      DATADOG_API_KEY:
        description: 'Datadog sourcemap api key'
        required: true
      EXPO_TOKEN:
        description: 'Expo EAS token'
        required: true
      CI_GH_PACKAGE_TOKEN:
        description: 'GitHub Packages token'
        required: true
    inputs:
      environment:
        description: 'Environment - staging, production'
        required: true
        type: string
      channel:
        description: 'EAS Updates channel'
        required: true
        type: string

jobs:
  eas-updates:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    defaults:
      run:
        working-directory: app
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'app/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'app/package-lock.json'
      - name: Install
        run: npm ci
        env:
          CAT_GITHUB_PACKAGES_TOKEN: ${{ secrets.CAT_SCOPE_PAT }}
      - name: Setup Expo and EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      - name: Publish update
        run: eas update --channel ${{ inputs.channel }} --auto
        env:
          EXPO_PUBLIC_ENVIRONMENT: ${{ inputs.environment }}
          NODE_OPTIONS: '--max-old-space-size=4096'
      - name: Upload sourcemaps
        if: ${{ inputs.environment == 'production' }}
        run: ./scripts/upload-datadog-eas-updates-sourcemap.sh
        env:
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          DATADOG_SITE: 'datadoghq.eu'
