# This workflow will triage pull requests and apply a label based on the
# paths that are modified in the pull request & the target branch.
#
# https://github.com/actions/labeler

name: Labeler

on:
  pull_request:
    types:
      [
        opened,
        synchronize,
        edited,
        reopened,
        ready_for_review,
        converted_to_draft,
      ]

jobs:
  label:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    steps:
      - uses: actions/checkout@v4

      - name: Add main label to any PR merging to main
        if: ${{ github.base_ref == 'main' }}
        run: gh pr edit ${{ github.head_ref }} --add-label ":rocket:main"

      - name: Remove main label if not targeting main
        if: ${{ github.base_ref != 'main' }}
        run: gh pr edit ${{ github.head_ref }} --remove-label ":rocket:main"

      - uses: actions/labeler@v5
        with:
          repo-token: '${{ secrets.GITHUB_TOKEN }}'
