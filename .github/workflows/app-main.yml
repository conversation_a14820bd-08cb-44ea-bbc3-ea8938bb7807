name: Push to main branch (app/)

on:
  push:
    branches: [main]
    paths: ['app/**', '.github/workflows/app-*.yml']

jobs:
  check:
    name: Run lint, format, types, and tests
    uses: ./.github/workflows/app-check-shared.yml
    secrets: inherit

  eas-build-staging:
    name: Run Expo EAS Build - Staging
    uses: ./.github/workflows/app-eas-build-shared.yml
    secrets: inherit
    with:
      profile: staging

  eas-publish-staging:
    name: Run Expo EAS Publish - Staging
    uses: ./.github/workflows/app-eas-updates-shared.yml
    secrets: inherit
    with:
      channel: staging
      environment: staging

  eas-publish-prod:
    name: Run Expo EAS Publish - Prod
    needs: check
    uses: ./.github/workflows/app-eas-updates-shared.yml
    secrets: inherit
    with:
      channel: production
      environment: production
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
      cancel-in-progress: false
