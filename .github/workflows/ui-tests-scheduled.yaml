name: Scheduled - Automated Tests

on:
  schedule:
    - cron: '0 1 * * 1-6' # 1:00am every day, except Sunday
  workflow_dispatch:
    inputs:
      test-type:
        description: 'Test type to run'
        required: true
        default: 'regression-quick'
        type: choice
        options:
          - feature
          - main
          - production
          - regression
          - regression-quick
      disable-slack-notifications:
        description: 'Disable Slack notifications'
        default: true
        required: true
        type: boolean

jobs:
  run_web_ui_tests:
    name: Run Web UI tests
    uses: ./.github/workflows/ui-tests-shared.yml
    secrets: inherit
    with:
      platform: 'web'
      test-type: ${{ inputs.test-type || 'regression' }}
      disable-slack-notifications: ${{ inputs.disable-slack-notifications || false }}

  run_android_ui_tests:
    name: Run Android UI tests
    uses: ./.github/workflows/ui-tests-shared.yml
    secrets: inherit
    with:
      platform: 'android'
      test-type: ${{ inputs.test-type || 'regression' }}
      disable-slack-notifications: ${{ inputs.disable-slack-notifications || false }}

  run_ios_ui_tests:
    name: Run iOS UI tests
    needs: run_android_ui_tests
    uses: ./.github/workflows/ui-tests-shared.yml
    if: ${{ !cancelled() }}
    secrets: inherit
    with:
      platform: 'ios'
      test-type: ${{ inputs.test-type || 'regression' }}
      disable-slack-notifications: ${{ inputs.disable-slack-notifications || false }}
