name: Expo EAS Build

on:
  workflow_call:
    secrets:
      EXPO_TOKEN:
        description: 'Expo EAS token'
        required: true
      CI_GH_PACKAGE_TOKEN:
        description: 'GitHub Packages token'
        required: true
    inputs:
      profile:
        description: 'Profile - staging, production_internal, production'
        required: true
        type: string
      auto-submit:
        description: 'If true, automatically submit the build to the store. Will only run with profile production'
        required: false
        type: boolean
      requires_label:
        description: 'Label required to trigger build'
        required: false
        type: string

jobs:
  check_label:
    name: Check label
    uses: ./.github/workflows/check-label-shared.yml
    with:
      label: ${{ inputs.requires_label }}

  eas-build:
    needs: check_label
    if: ${{ contains(needs.check_label.outputs.label_exists, 'true') }}
    runs-on: ubuntu-latest
    timeout-minutes: 15
    defaults:
      run:
        working-directory: app
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'app/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'app/package-lock.json'
      - name: Install
        run: npm ci
        env:
          CAT_GITHUB_PACKAGES_TOKEN: ${{ secrets.CAT_SCOPE_PAT }}
      - name: Setup Expo and EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      - name: Build on EAS
        if: ${{ !contains(inputs.auto-submit, 'true') }}
        run: eas build --platform all --profile ${{ inputs.profile }} --message ${{ github.head_ref || github.ref_name }} --non-interactive --no-wait
      - name: Build on EAS & auto-submit
        if: ${{ contains(inputs.auto-submit, 'true') && contains(inputs.profile, 'production') }}
        run: eas build --platform all --profile ${{ inputs.profile }} --message ${{ github.head_ref || github.ref_name }} --auto-submit --non-interactive --no-wait
