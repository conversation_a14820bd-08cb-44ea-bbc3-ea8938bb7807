name: Check for label

# TODO: can move this to external shared repo

on:
  workflow_call:
    inputs:
      label:
        description: 'Label to find on pull request, if not provided, will return true'
        required: false
        type: string
    outputs:
      label_exists:
        description: 'Whether or not the label exists on the pull request'
        value: ${{ jobs.check_label.outputs.label_exists }}

jobs:
  check_label:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    outputs:
      label_exists: ${{ steps.check_label.outputs.label_exists }}
    steps:
      - id: check_label
        uses: actions/github-script@v7
        with:
          script: |
            const label = '${{ inputs.label }}';
            if (!label) {
              core.setOutput('label_exists', true);
              return;
            }

            const labels = context.payload.pull_request.labels.map(label => label.name);
            core.setOutput('label_exists', labels.includes(label));
