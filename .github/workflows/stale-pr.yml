name: 'Close stale PRs'

on:
  schedule:
    - cron: '30 7 * * *' # 7:30am every day
  workflow_dispatch:

permissions:
  issues: write
  pull-requests: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          stale-pr-message: 'This PR is stale because it has been open for 14 days. Add "Not stale" label or this PR will be closed in 5 days.'
          close-pr-message: 'This PR was closed because it has been stalled for 5 days.'
          days-before-pr-stale: 14
          days-before-pr-close: 5
          ignore-updates: true
          exempt-pr-labels: 'Not Stale'
