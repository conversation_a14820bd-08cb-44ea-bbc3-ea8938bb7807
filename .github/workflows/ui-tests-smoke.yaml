name: Scheduled - Production Smoke Test - Web App

on:
  schedule:
    - cron: '0 7-20 * * *' # every hour between 7:00-20:00 UTC
  workflow_dispatch:

jobs:
  run_web_ui_smoke_test:
    name: Run Web UI SMOKE test
    uses: ./.github/workflows/ui-tests-shared.yml
    secrets: inherit
    with:
      platform: 'web'
      test-type: 'production'
      failed-slack-notifications-only: true
      trigger-opsgenie-alert: false
