name: EAS Preview

on:
  workflow_call:
    secrets:
      EXPO_TOKEN:
        description: 'Expo EAS token'
        required: true
      CI_GH_PACKAGE_TOKEN:
        description: 'GitHub Packages token'
        required: true
    inputs:
      head_ref:
        description: 'Branch name, used for "branch" on preview build'
        required: true
        type: string
      environment:
        description: 'Environment - dev, staging, production'
        required: true
        type: string
      requires_label:
        description: 'Label required to trigger preview'
        required: false
        type: string

jobs:
  check_label:
    name: Check label
    uses: ./.github/workflows/check-label-shared.yml
    with:
      label: ${{ inputs.requires_label }}

  eas-preview:
    needs: check_label
    if: ${{ contains(needs.check_label.outputs.label_exists, 'true') }}
    runs-on: ubuntu-latest
    timeout-minutes: 30
    permissions:
      contents: read
      issues: write
      pull-requests: write
    defaults:
      run:
        working-directory: app
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'app/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'app/package-lock.json'
      - name: Install
        run: npm ci
        env:
          CAT_GITHUB_PACKAGES_TOKEN: ${{ secrets.CAT_SCOPE_PAT }}
      - name: Setup Expo and EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      - name: 🚀 Create PR preview
        id: preview
        uses: expo/expo-github-action/preview@v8
        with:
          working-directory: app
          command: eas update --auto --branch ${{ inputs.head_ref }} --message ${{ inputs.head_ref }} --non-interactive
          comment: false
        env:
          EXPO_PUBLIC_ENVIRONMENT: ${{ inputs.environment }}
          NODE_OPTIONS: '--max-old-space-size=4096'
      - uses: actions/github-script@v7
        with:
          script: |
            const commentId = `${context.issue.number}-${{ steps.preview.outputs.projectId }}-${{ inputs.environment }}`;
            const otaPreviewUrl = 'https://u.expo.dev/${{ steps.preview.outputs.projectId }}/group/${{ steps.preview.outputs.groupId }}';
            const body = '🚀 Preview build for `${{ inputs.environment }}` is ready!' +
              '\n\n' +
              'Links:' +
              '\n' +
              '- [Expo.dev update page](${{ steps.preview.outputs.link }})' +
              '\n' +
              `- Dev client URL ("Enter URL Manually"): ${otaPreviewUrl}` +
              '\n' +
              `- Native app deep link: \`exp+trade-experience://expo-development-client/?url=${otaPreviewUrl}\`` +
              '\n\n' +
              'QR Code: ' +
              '\n' +
              `<a href="${{ steps.preview.outputs.link }}" target="_blank"><img src="https://qr.expo.dev/development-client?appScheme=exp%2Btrade-experience&url=${otaPreviewUrl}" height="200px" width="200px"></a>` +
              '\n\n' +
              'Read more about Expo dev clients:' +
              '\n' +
              '- https://docs.expo.dev/develop/development-builds/introduction/' +
              '\n' +
              '- https://checkatrade.atlassian.net/wiki/spaces/TRADE/pages/3549069550/How+to+use+Expo+Dev+clients';

            github.rest.issues.createComment({
              id: commentId,
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            })
