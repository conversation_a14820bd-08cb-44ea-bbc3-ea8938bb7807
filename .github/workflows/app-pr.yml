name: Pull request (app/)

on:
  pull_request:
    paths: ['app/**', '.github/workflows/app-*.yml']

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref }}
  cancel-in-progress: true

jobs:
  pr-preview-staging:
    name: Create PR preview for staging env
    if: github.event.pull_request.draft == false
    uses: ./.github/workflows/app-eas-preview-shared.yml
    secrets: inherit
    with:
      environment: 'staging'
      head_ref: ${{ github.head_ref }}

  pr-preview-prod:
    name: Create PR preview for prod env
    uses: ./.github/workflows/app-eas-preview-shared.yml
    secrets: inherit
    with:
      environment: 'production'
      head_ref: ${{ github.head_ref }}
      requires_label: 'Create prod Preview'

  pr-eas-build-staging:
    name: Run Expo EAS Build - Staging
    uses: ./.github/workflows/app-eas-build-shared.yml
    secrets: inherit
    with:
      profile: staging
      requires_label: 'build-native-apps'
