name: Push to next branch (app/)

on:
  push:
    branches: [next/**]
    paths: ['app/**', '.github/workflows/app-*.yml']

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  check:
    name: Run lint, format, types, and tests
    uses: ./.github/workflows/app-check-shared.yml
    secrets: inherit

  eas-build-internal-prod:
    name: Run Expo EAS Build - Internal Prod
    needs: check
    uses: ./.github/workflows/app-eas-build-shared.yml
    secrets: inherit
    with:
      profile: production_internal

  eas-build-appstore-prod:
    name: Run Expo EAS Build - App Store Prod
    needs: check
    uses: ./.github/workflows/app-eas-build-shared.yml
    secrets: inherit
    with:
      profile: production
      auto-submit: true
