name: ios-release
on: 
  push:
    branches:
      - main
    paths:
      - 'app/ios/**/*'
  
jobs:
  ios-release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@master
      - name: Release to CCV
        id: release
        uses: cat-home-experts/go-releaser@main
        with:
          token: ${{ secrets.CCV_TOKEN }}
          branch: ${{ github.ref_name }}
          environment: prod
          service: trade-experience-ios
          revision: ${{ github.sha }}
          repository_url: https://github.com/${{ github.repository }}
