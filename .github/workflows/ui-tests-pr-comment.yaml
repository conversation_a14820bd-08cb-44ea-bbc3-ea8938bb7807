name: Run UI Automation Tests on PR Comment

on:
  issue_comment:
    types: [created]

permissions:
  contents: read
  issues: write
  pull-requests: write
  statuses: write

jobs:
  prepare:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    if: github.event.issue.pull_request && startsWith(github.event.comment.body, '/ui-tests')
    outputs:
      pr_head_ref: ${{ steps.comment_branch.outputs.head_ref }}
      pr_head_sha: ${{ steps.comment_branch.outputs.head_sha }}
      ios_bs_url: ${{ steps.find_browserstack_app_urls.outputs.ios_bs_url }}
      android_bs_url: ${{ steps.find_browserstack_app_urls.outputs.android_bs_url }}
    steps:
      - id: comment_branch
        name: Get PR branch
        uses: xt0rted/pull-request-comment-branch@v2
      - name: Set commit status as pending
        uses: myrotvorets/set-commit-status-action@v2.0.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          status: pending
          context: Run UI Automation Tests
          sha: ${{ steps.comment_branch.outputs.head_sha }}
      - uses: actions/checkout@v4
        with:
          ref: ${{ steps.comment_branch.outputs.head_ref }}
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'ui-tests/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'ui-tests/package-lock.json'
      - name: Install
        run: npm install
        working-directory: ui-tests
      - id: find_browserstack_app_urls
        name: Find Browserstack app URLs
        run: |
          # find iOS app url
          ios_bs_url=$(PLATFORM="ios" npx ts-node ./scripts/getBrowserstackAppId.ts)
          echo "$ios_bs_url"
          echo "ios_bs_url=$ios_bs_url" >> "$GITHUB_OUTPUT"

          # find Android app url
          android_bs_url=$(PLATFORM="android" npx ts-node ./scripts/getBrowserstackAppId.ts)
          echo "$android_bs_url"
          echo "android_bs_url=$android_bs_url" >> "$GITHUB_OUTPUT"
        working-directory: ui-tests
        env:
          BROWSERSTACK_USER: ${{ secrets.BS_USER }}
          BROWSERSTACK_KEY: ${{ secrets.BS_KEY }}
          BRANCH_NAME: ${{ steps.comment_branch.outputs.head_ref }}
      - uses: actions/github-script@v7
        with:
          script: |
            const firstLine = '🧪 UI Automation tests have started. You can monitor the overall progress on the [App Automate Dashboard](https://app-automate.browserstack.com/dashboard/v2/builds)';
            const secondLine = 'Ensure you\'ve finished building the iOS and Android apps before running this script.';
            const thirdLine = 'You can build the native apps by adding the "build-native-apps" label.';
            const firstSection = `${firstLine}\n\n${secondLine}\n${thirdLine}`;

            const firstAppFound = 'The following builds were found on browserstack:';
            const secondAppFound = '- iOS app URL: `${{ steps.find_browserstack_app_urls.outputs.ios_bs_url }}`';
            const thirdAppFound = '- Android app URL: `${{ steps.find_browserstack_app_urls.outputs.android_bs_url }}`';
            const finalAppFound = 'If either urls are undefined, then the tests will run against the latest main build.';
            const secondSection = `${firstAppFound}\n${secondAppFound}\n${thirdAppFound}\n\n${finalAppFound}`;

            const body = `${firstSection}\n\n${secondSection}`;
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            })

  run_android_ui_tests:
    name: Run Android UI tests
    needs: prepare
    if: github.event.issue.pull_request && startsWith(github.event.comment.body, '/ui-tests')
    uses: ./.github/workflows/ui-tests-shared.yml
    secrets: inherit
    with:
      platform: 'android'
      test-type: 'main'
      build-id: ${{ needs.prepare.outputs.android_bs_url }}
      disable-slack-notifications: true
      head_ref: ${{ needs.prepare.outputs.pr_head_ref }}

  post-run-android:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [prepare, run_android_ui_tests]
    if: ${{ !cancelled() && github.event.issue.pull_request && startsWith(github.event.comment.body, '/ui-tests') }}
    steps:
      - uses: actions/github-script@v7
        with:
          script: |
            const firstLine = 'Android Automation tests have finished running. 🎉';
            const secondLine = '[View Android test results](${{ needs.run_android_ui_tests.outputs.bs_test_run_url }})';
            const body = `${firstLine} ${secondLine}`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            })

  run_ios_ui_tests:
    name: Run iOS UI tests
    needs: [prepare]
    if: ${{ !cancelled() && github.event.issue.pull_request && startsWith(github.event.comment.body, '/ui-tests') }}
    uses: ./.github/workflows/ui-tests-shared.yml
    secrets: inherit
    with:
      platform: 'ios'
      test-type: 'main'
      build-id: ${{ needs.prepare.outputs.ios_bs_url }}
      disable-slack-notifications: true
      head_ref: ${{ needs.prepare.outputs.pr_head_ref }}

  post-run-ios:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [run_ios_ui_tests]
    if: ${{ !cancelled() && github.event.issue.pull_request && startsWith(github.event.comment.body, '/ui-tests') }}
    steps:
      - uses: actions/github-script@v7
        with:
          script: |
            const firstLine = 'iOS Automation tests have finished running. 🎉';
            const secondLine = '[View iOS test results](${{ needs.run_ios_ui_tests.outputs.bs_test_run_url }})';
            const body = `${firstLine} ${secondLine}`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            })

  post-run:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [prepare, run_android_ui_tests, run_ios_ui_tests]
    if: ${{ !cancelled() && github.event.issue.pull_request && startsWith(github.event.comment.body, '/ui-tests') }}
    steps:
      - name: Set final commit status
        uses: myrotvorets/set-commit-status-action@v2.0.0
        if: always()
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          status: ${{ job.status }}
          context: Run UI Automation Tests
          sha: ${{ needs.prepare.outputs.pr_head_sha }}
