name: Run tsc & lint checks on ui-tests directory

on:
  push:
    branches:
      - main
    paths:
      - 'ui-tests/**'
  pull_request:
    paths:
      - 'ui-tests/**'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    defaults:
      run:
        working-directory: ui-tests
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'ui-tests/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'ui-tests/package-lock.json'
      - name: Install
        run: npm ci
        env:
          APPIUM_SKIP_CHROMEDRIVER_INSTALL: true
      - name: Check types
        run: npm run check-types
      - name: Check lint
        run: npm run lint
      - name: Check format
        run: npm run format
