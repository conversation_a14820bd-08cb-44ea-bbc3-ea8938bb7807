name: Build dev client (app/)

on:
  workflow_dispatch:
  schedule:
    # 1am on the first day of the month
    - cron: '0 1 1 * *'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-dev-client:
    name: Build an Expo Dev Client
    defaults:
      run:
        working-directory: app
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'app/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'app/package-lock.json'
      - name: Install
        run: npm ci
        env:
          CAT_GITHUB_PACKAGES_TOKEN: ${{ secrets.CAT_SCOPE_PAT }}
      - name: Setup Expo and EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Build dev client for Android Emulator & real iOS/Android devices
        run: eas build --profile dev-client --platform all --message ${{ github.ref_name }} --non-interactive --no-wait
        env:
          EXPO_PUBLIC_ENVIRONMENT: staging

      - name: Build dev client for iOS Simulator
        run: eas build --profile dev-client-simulator --platform ios --message ${{  github.ref_name }} --non-interactive --no-wait
        env:
          EXPO_PUBLIC_ENVIRONMENT: staging
