name: UI Tests Workflow
run-name: UI Tests Workflow - "${{ inputs.test-type }}" on "${{ inputs.platform }}"

on:
  workflow_call:
    secrets:
      BS_USER:
        description: 'BrowserStack user'
        required: true
      BS_KEY:
        description: 'BrowserStack key'
        required: true
      SLACK_WEBHOOK_URL_SUCCESS:
        description: 'Successful test run - Slack webhook URL'
        required: false
      SLACK_WEBHOOK_URL_FAILED:
        description: 'Failed test run - Slack webhook URL'
        required: false
      IAP_TESTER_SA_KEY:
        description: 'Service account key for IAP'
        required: false
      IAP_SERVICE_ACCOUNT:
        description: 'IAP service account'
        required: false
      IAP_AUDIENCE:
        description: 'IAP audience'
        required: false
      OPSGENIE_API_KEY:
        description: 'Opsgenie API key'
        required: false
    inputs:
      head_ref:
        description: 'git head ref'
        required: false
        type: string
      build-id:
        description: 'BrowserStack Build ID'
        required: false
        type: string
      platform:
        description: 'Platform to test on'
        required: true
        default: 'android'
        type: string
      test-type:
        description: 'Test type to run'
        required: true
        default: 'main'
        type: string
      disable-slack-notifications:
        description: 'Disable Slack notifications'
        required: false
        type: boolean
      failed-slack-notifications-only:
        description: 'Only send Slack notifications on failed tests'
        required: false
        type: boolean
      trigger-opsgenie-alert:
        description: 'Trigger OpsGenie alert on failed tests'
        required: false
        type: boolean
    outputs:
      bs_test_run_url:
        description: 'Browserstack test run URL'
        value: ${{ jobs.run-ui-tests.outputs.bs_test_run_url }}
  workflow_dispatch:
    inputs:
      build-id:
        description: 'Browserstack build ID'
        required: false
        type: string
      platform:
        description: 'Platform to test on'
        required: true
        default: 'ios'
        type: choice
        options:
          - ios
          - android
          - web
      test-type:
        description: 'Test type to run'
        required: true
        default: 'main'
        type: choice
        options:
          - feature
          - main
          - production
          - regression
          - regression-quick
      disable-slack-notifications:
        description: 'Disable Slack notifications'
        default: true
        required: true
        type: boolean

jobs:
  run-ui-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 120
    outputs:
      bs_test_run_url: ${{ steps.get_bs_test_url.outputs.bs_test_run_url }}
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.head_ref || github.head_ref }}
      - name: Get git sha
        id: get_git_sha
        run: echo "git_sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
      - uses: actions/setup-node@v4
        with:
          node-version-file: 'ui-tests/.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'ui-tests/package-lock.json'
      - name: Install
        run: npm install
        working-directory: ui-tests
      - uses: 'google-github-actions/auth@v2'
        if: ${{ inputs.platform == 'web' }}
        with:
          credentials_json: ${{ secrets.IAP_TESTER_SA_KEY }}
      - name: Run UI Automation Tests
        env:
          BROWSERSTACK_USER: ${{ secrets.BS_USER }}
          BROWSERSTACK_KEY: ${{ secrets.BS_KEY }}
          BROWSERSTACK_BUILD_ID: ${{ inputs.build-id }}
          IAP_SERVICE_ACCOUNT: ${{ secrets.IAP_SERVICE_ACCOUNT }}
          IAP_AUDIENCE: ${{ secrets.IAP_AUDIENCE }}
          PLATFORM: ${{ inputs.platform }}
          TEST_TYPE: ${{ inputs.test-type }}
          BUILD_TAG: '${{ inputs.platform }}-${{ steps.get_git_sha.outputs.git_sha }}-${{ github.run_id }}'
        run: |
          SCRIPT_NAME="test:$TEST_TYPE-$PLATFORM"
          npm run $SCRIPT_NAME
        working-directory: ui-tests
      - id: get_bs_test_url
        name: Get BrowserStack test run URL
        if: ${{ !cancelled() }}
        run: |
          res_test_url=$(npx ts-node ./scripts/getBrowserstackTestUrl.ts)
          echo "$res_test_url"
          echo "bs_test_run_url=$res_test_url" >> "$GITHUB_OUTPUT"
        working-directory: ui-tests
        env:
          BROWSERSTACK_USER: ${{ secrets.BS_USER }}
          BROWSERSTACK_KEY: ${{ secrets.BS_KEY }}
          PLATFORM: ${{ inputs.platform }}
          BUILD_TAG: '${{ inputs.platform }}-${{ steps.get_git_sha.outputs.git_sha }}-${{ github.run_id }}'
      - name: Send successful test summary to Slack
        id: slack_notification_success
        if: ${{ success() && !contains(inputs.disable-slack-notifications, 'true') && !contains(inputs.failed-slack-notifications-only, 'true') }}
        uses: slackapi/slack-github-action@v1
        with:
          payload: |
            {
              "workflowName": "${{ github.workflow }}",
              "platform": "${{ inputs.platform }}",
              "testType": "${{ inputs.test-type }}",
              "ghActionRunURL": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "browserstackURL": "${{ steps.get_bs_test_url.outputs.bs_test_run_url }}",
              "status": "${{ job.status }}"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_SUCCESS }}
      - name: Send failed test summary to Slack
        id: slack_notification_failed
        if: ${{ failure() && !contains(inputs.disable-slack-notifications, 'true') }}
        uses: slackapi/slack-github-action@v1
        with:
          payload: |
            {
              "workflowName": "${{ github.workflow }}",
              "platform": "${{ inputs.platform }}",
              "testType": "${{ inputs.test-type }}",
              "ghActionRunURL": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "browserstackURL": "${{ steps.get_bs_test_url.outputs.bs_test_run_url }}",
              "status": "${{ job.status }}"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_FAILED }}
      - name: Trigger OpsGenie alert
        if: ${{ failure() && inputs.trigger-opsgenie-alert }}
        working-directory: ui-tests
        run: |
          ./scripts/opsgenie/create-opsgenie-alert.sh $GH_ACTION_RUN_URL $BS_RUN_URL
        env:
          OPSGENIE_API_KEY: ${{ secrets.OPSGENIE_API_KEY }}
          GH_ACTION_RUN_URL: '${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}'
          BS_RUN_URL: ${{ steps.get_bs_test_url.outputs.bs_test_run_url }}
      - name: Close OpsGenie alert
        if: ${{ success() && inputs.trigger-opsgenie-alert }}
        working-directory: ui-tests
        run: |
          ./scripts/opsgenie/close-opsgenie-alert.sh
        env:
          OPSGENIE_API_KEY: ${{ secrets.OPSGENIE_API_KEY }}
      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: logs
          path: ui-tests/logs
