
options:
  logging: CLOUD_LOGGING_ONLY

steps:
- name: gcr.io/cloud-builders/gcloud
  waitFor: ['-']
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      API_CONFIG_PATH=$(gcloud api-gateway gateways describe ${_API_GATEWAY_NAME} --location=${_GCP_REGION} --project=${PROJECT_ID} --format="value(apiConfig)")
      CLOUD_RUN_URL=$(gcloud run services describe ${_SERVICE_NAME} --platform ${_PLATFORM} --region ${_GCP_REGION} --format 'value(status.url)')

      echo "================================================="
      echo "Variables"
      echo "================================================="
      echo "- CLOUD_RUN_URL: " $$CLOUD_RUN_URL
      echo "- API_CONFIG_PATH: " $$API_CONFIG_PATH

      CONFIG_NAME=$(echo $$API_CONFIG_PATH | sed -e 's:.*/::')
      echo "- CONFIG_NAME: " $$CONFIG_NAME

      CONFIG_VERSION=$(echo $$CONFIG_NAME | sed -e 's:.*-::')
      echo '- CONFIG_VERSION: ' $$CONFIG_VERSION

      if [[ $$CONFIG_VERSION =~ ^[0-9]+$ ]] ; then
        CONFIG_NAME_WITHOUT_VERSION=$(echo $$CONFIG_NAME | sed s/"$$CONFIG_VERSION"//)
        CONFIG_NEW_VERSION=$(($$CONFIG_VERSION+1))
        NEW_CONFIG_NAME="$${CONFIG_NAME_WITHOUT_VERSION}$${CONFIG_NEW_VERSION}"
      else
        NEW_CONFIG_NAME="$${CONFIG_NAME}-01"
      fi

      echo "- NEW_CONFIG_NAME: " $$NEW_CONFIG_NAME

      sed -i -e "s|{host}|$$CLOUD_RUN_URL|g" api-gateway/spec.yaml
      sed -i -e 's/{env}/${_VERSION}/g' api-gateway/spec.yaml

      echo "================================================="
      echo "Gateway spec.yaml"
      echo "================================================="
      cat api-gateway/spec.yaml

      echo
      echo "================================================="
      echo "Calling 'gcloud api-gateway api-configs create'"
      echo "================================================="
      gcloud api-gateway api-configs create $$NEW_CONFIG_NAME \
        --project ${PROJECT_ID} \
        --api ${_API_GATEWAY_API_NAME} \
        --openapi-spec=api-gateway/spec.yaml

      echo "================================================="
      echo "Calling 'gcloud api-gateway gateways update'"
      echo "================================================="
      gcloud api-gateway gateways update ${_API_GATEWAY_NAME} \
        --location=${_GCP_REGION} \
        --project ${PROJECT_ID} \
        --api ${_API_GATEWAY_API_NAME} \
        --api-config=$$NEW_CONFIG_NAME
