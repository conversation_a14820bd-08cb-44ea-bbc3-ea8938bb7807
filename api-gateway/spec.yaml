swagger: '2.0'
info:
  title: TradesAppApi {env}
  description: Provides an API for Trade App
  version: v3
x-google-backend:
  address: {host}
  path_translation: APPEND_PATH_TO_ADDRESS
securityDefinitions:
  Bearer:
    type: apiKey
    name: Authorization
    in: header
security:
  - bearerAuth: []
paths:
  '/api/availability/{companyId}/{tradeId}':
    get:
      security:
        - Bearer: []
      tags:
        - RequestAvailabilityData
      operationId: RequestAvailabilityData
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: tradeId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
    options:
      security:
        - Bearer: []
      tags:
        - RequestAvailabilityData
      operationId: RequestAvailabilityDataOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: tradeId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
  '/api/profile-description/{companyId}/{tradeId}':
    get:
      security:
        - Bearer: []
      tags:
        - RequestProfileDescriptionData
      operationId: RequestProfileDescriptionData
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: tradeId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
    options:
      security:
        - Bearer: []
      tags:
        - RequestProfileDescriptionData
      operationId: RequestProfileDescriptionDataOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: tradeId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
  '/api/search-preview/{companyId}/{tradeId}':
    get:
      security:
        - Bearer: []
      tags:
        - RequestSearchPreviewData
      operationId: RequestSearchPreviewData
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: tradeId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
    options:
      security:
        - Bearer: []
      tags:
        - RequestSearchPreviewData
      operationId: RequestSearchPreviewDataOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: tradeId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
  '/api/zuora/request/{companyId}':
    get:
      security:
        - Bearer: []
      tags:
        - Zuora
      operationId: Invoices
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
        '404':
          description: Not Found
    options:
      security:
        - Bearer: []
      tags:
        - Zuora
      operationId: InvoicesOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
  '/api/zuora/request/{companyId}/{invoiceId}':
    get:
      security:
        - Bearer: []
      tags:
        - Zuora
      operationId: InvoiceFile
      parameters:
        - in: query
          name: companyId
          required: true
          type: integer
          format: int32
        - in: query
          name: invoiceId
          required: true
          type: string
      responses:
        '200':
          description: Success
        '404':
          description: Not Found
    options:
      security:
        - Bearer: []
      tags:
        - Zuora
      operationId: InvoiceFileOptions
      parameters:
        - in: query
          name: companyId
          required: true
          type: integer
          format: int32
        - in: query
          name: invoiceId
          required: true
          type: string
      responses:
        '200':
          description: Success
  '/api/zuora/request/{companyId}/{invoiceId}/items':
    get:
      security:
        - Bearer: [ ]
      tags:
        - Zuora
      operationId: InvoiceItems
      parameters:
        - in: query
          name: companyId
          required: true
          type: integer
          format: int32
        - in: query
          name: invoiceId
          required: true
          type: string
      responses:
        '200':
          description: Success
        '404':
          description: Not Found
    options:
      security:
        - Bearer: [ ]
      tags:
        - Zuora
      operationId: InvoiceItemOptions
      parameters:
        - in: query
          name: companyId
          required: true
          type: integer
          format: int32
        - in: query
          name: invoiceId
          required: true
          type: string
      responses:
        '200':
          description: Success
  '/api/reviews/{reviewId}/report':
    post:
      security:
        - Bearer: []
      tags:
        - Review
      operationId: Report
      parameters:
        - in: path
          name: reviewId
          required: true
          type: string
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
    options:
      security:
        - Bearer: []
      tags:
        - Review
      operationId: ReportOptions
      parameters:
        - in: path
          name: reviewId
          required: true
          type: string
      responses:
        '200':
          description: Success
  '/api/quotes-and-invoices/generate-pdf/{companyId}/{quoteId}/{type}':
    post:
      security:
        - Bearer: [ ]
      tags:
        - QuotesAndInvoices
      operationId: GeneratePDF
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: quoteId
          required: true
          type: string
        - in: path
          name: type
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
    options:
      security:
        - Bearer: [ ]
      tags:
        - QuotesAndInvoices
      operationId: GeneratePDFOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: quoteId
          required: true
          type: string
        - in: path
          name: type
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
  '/api/quotes-and-invoices/share-quote/{companyId}/{quoteId}':
    post:
      security:
        - Bearer: [ ]
      tags:
        - QuotesAndInvoices
      operationId: ShareQuote
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: quoteId
          required: true
          type: string
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
    options:
      security:
        - Bearer: [ ]
      tags:
        - QuotesAndInvoices
      operationId: ShareQuoteOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: quoteId
          required: true
          type: string
      responses:
        '200':
          description: Success
  '/api/zuora/account/{companyId}':
    get:
      security:
        - Bearer: []
      tags:
        - RequestZuoraAccountSummaryData
      operationId: RequestZuoraAccountSummaryData
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
    options:
      security:
        - Bearer: []
      tags:
        - RequestZuoraAccountSummaryData
      operationId: RequestZuoraAccountSummaryDataOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
  '/api/zuora/account/signature/{companyId}':
    get:
      security:
        - Bearer: []
      tags:
        - RequestZuoraSignatureData
      operationId: RequestZuoraSignatureData
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
    options:
      security:
        - Bearer: []
      tags:
        - RequestZuoraSignatureData
      operationId: RequestZuoraSignatureDataOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
  '/api/zuora/payment/{companyId}/{paymentMethodId}/{amount}':
    post:
      security:
        - Bearer: []
      tags:
        - RequestZuoraPaymentData
      operationId: RequestZuoraPaymentData
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: paymentMethodId
          required: true
          type: string
        - in: path
          name: amount
          required: true
          type: number
          format: float
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
        '401':
          description: Unauthorized
    options:
      security:
        - Bearer: []
      tags:
        - RequestZuoraPaymentData
      operationId: RequestZuoraPaymentDataOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
        - in: path
          name: paymentMethodId
          required: true
          type: string
        - in: path
          name: amount
          required: true
          type: number
          format: float
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
        '401':
          description: Unauthorized
  '/api/company-categories/{companyId}':
    get:
      security:
        - Bearer: []
      tags:
        - RequestReadCompanyCategoriesData
      operationId: RequestReadCompanyCategoriesData
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
    options:
      security:
        - Bearer: []
      tags:
        - RequestReadCompanyCategoriesData
      operationId: RequestReadCompanyCategoriesDataOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
  '/api/campaign/{companyId}/{campaignId}/send-complete-campaign-setup-email':
    post:
      summary: "Sends an email via the comms service asking the company to finish setting up their campaign. Email template ID is 9462"
      consumes:
        - application/json
        - text/json
      produces:
        - text/plain
      security:
        - Bearer: [ ]
      tags:
        - Campaign
      operationId: SendCompleteCampaignSetupEmail
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
        - in: path
          name: campaignId
          required: true
          type: string
      responses:
        '200':
          description: Success
        '400':
          description: Bad request
        '401':
          description: Unauthorized
    options:
      security:
        - Bearer: [ ]
      tags:
        - Campaign
      operationId: SendCompleteCampaignSetupEmailOptions
      parameters:
        - in: path
          name: companyId
          required: true
          type: integer
        - in: path
          name: campaignId
          required: true
          type: string
      responses:
        '200':
          description: Success
